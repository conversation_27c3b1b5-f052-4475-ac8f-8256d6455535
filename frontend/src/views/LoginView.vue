<template>
  <div class="login-container">
    <div class="login-form">
      <div class="logo-container">
        <img src="../assets/logo.png" alt="系統Logo" class="logo" />
      </div>
      <h1 class="system-title">MES管理系統</h1>
      
      <div class="form-group">
        <span class="input-icon">
          <i class="fas fa-user"></i>
        </span>
        <input 
          type="text" 
          v-model="username" 
          placeholder="請輸入帳號" 
          class="form-control with-icon"
        />
      </div>
      
      <div class="form-group">
        <span class="input-icon">
          <i class="fas fa-lock"></i>
        </span>
        <input 
          type="password" 
          v-model="password" 
          placeholder="請輸入密碼" 
          class="form-control with-icon"
        />
        <span class="toggle-password" @click="togglePasswordVisibility">
          <i :class="['fas', passwordVisible ? 'fa-eye-slash' : 'fa-eye']"></i>
        </span>
      </div>
      
      <div v-if="errorMessage" class="error-message">
        <i class="fas fa-exclamation-circle"></i>
        {{ errorMessage }}
      </div>
      
      <button @click="handleLogin" class="login-button">
        <i class="fas fa-sign-in-alt"></i>
        登入
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import api from '../services/api';

const router = useRouter();
const username = ref('');
const password = ref('');
const passwordVisible = ref(false);
const errorMessage = ref('');

const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value;
  const passwordInput = document.querySelector('input[type="password"]');
  if (passwordInput) {
    passwordInput.type = passwordVisible.value ? 'text' : 'password';
  }
};

const handleLogin = async () => {
  try {
    errorMessage.value = '';
    
    if (!username.value || !password.value) {
      errorMessage.value = '請輸入帳號和密碼';
      return;
    }
    
    // 調用API登入
    const response = await api.login(username.value, password.value);
    
    if (response.status === 'error') {
      throw new Error(response.message || '登入失敗');
    }
    
    // 保存令牌到本地存儲
    localStorage.setItem('token', response.token);
    localStorage.setItem('user', JSON.stringify(response.data.employee));
    
    // 重定向到儀表板
    router.push('/dashboard');
  } catch (error) {
    errorMessage.value = error.message;
    console.error('登入錯誤:', error);
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f9f9f9;
}

.login-form {
  width: 450px;
  padding: 40px 30px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.logo-container {
  margin-bottom: 15px;
}

.logo {
  width: 80px;
  height: 80px;
}

.system-title {
  font-size: 24px;
  margin-bottom: 30px;
  color: #333;
}

.form-group {
  position: relative;
  margin-bottom: 20px;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 5px;
  outline: none;
  transition: border-color 0.3s;
}

.form-control.with-icon {
  padding-left: 40px;
}

.form-control:focus {
  border-color: #dba364;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #999;
  transition: color 0.3s;
}

.toggle-password:hover {
  color: #666;
}

.error-message {
  color: #f44336;
  margin-bottom: 15px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-message i {
  margin-right: 8px;
}

.login-button {
  width: 100%;
  padding: 12px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-button i {
  margin-right: 8px;
}

.login-button:hover {
  background-color: #1565c0;
}
</style> 