<template>
  <div class="dashboard-container">

    <div class="grid-container">
      <div class="grid-item full-width">
        <DashboardMaterialCharts />
      </div>
      <div class="grid-item">
        <h3>文件</h3>
        <!-- 文件內容 -->
      </div>
      <div class="grid-item">
        <h3>訂單進度</h3>
        <!-- 訂單進度內容 -->
      </div>
      <div class="grid-item">
        <h3>公告欄</h3>
        <DashboardAnnouncement />
      </div>
      <div class="grid-item">
        <h3>其他</h3>
        <!-- 其他內容 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import DashboardAnnouncement from '@/components/DashboardAnnouncement.vue';
import DashboardMaterialCharts from '@/components/DashboardMaterialCharts.vue';

const router = useRouter();
const user = ref(null);

onMounted(() => {
  // 從本地存儲獲取用戶資訊
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      user.value = JSON.parse(userStr);
    } catch (error) {
      console.error('解析用戶資訊出錯:', error);
    }
  }

  // 檢查是否已登入
  const token = localStorage.getItem('token');
  if (!token) {
    router.push('/login');
  }
});

const handleLogout = () => {
  // 清除本地存儲
  localStorage.removeItem('token');
  localStorage.removeItem('user');

  // 重定向到登入頁面
  router.push('/login');
};
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #333;
  margin-bottom: 30px;
}

.user-info {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.user-info h2 {
  margin-top: 0;
  color: #444;
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info p {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
}

.logout-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logout-button:hover {
  background-color: #d32f2f;
}

i {
  font-size: 1.1em;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 兩欄 */
  grid-gap: 20px; /* 間距 */
}

.grid-item {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.full-width {
  grid-column: 1 / -1; /* 讓元素佔據整行 */
  padding: 0; /* 移除內部填充，因為組件已經有自己的填充 */
  box-shadow: none; /* 移除陰影，因為組件已經有自己的陰影 */
  background-color: transparent; /* 透明背景，因為組件已經有自己的背景 */
}

.grid-item h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>
