<template>
  <div class="announcement-container">
    <div class="announcement-header">
      <h2>公告欄</h2>
      <div class="announcement-actions">
        <div class="announcement-actions-left">
        <button
          class="add-btn"
          @click="openAnnouncementForm"
        >
          <i class="fas fa-plus"></i>
          新增公告
        </button>
        <button
          class="add-btn"
          @click="toggleAnnouncementVisibility"
        >
          <i :class="isShowingHidden ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
          {{ isShowingHidden ? '顯示正常公告' : '顯示隱藏公告' }}
        </button>
      </div>
        <div class="search-container">
          <span>搜尋</span>
          <input type="text" class="search-input" placeholder="請輸入關鍵字" v-model="searchQuery" />
        </div>
      </div>
    </div>

    <div class="announcement-table-container">
      <table class="announcement-table">
        <thead>
          <tr>
            <th>公告日期</th>
            <th>公告類型</th>
            <th>公告單位</th>
            <th>主旨</th>
            <th>公告人</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in announcementList"
            :key="item.id"
            @click="viewAnnouncement(item)"
            style="cursor: pointer"
          >
            <td>{{ formatDate(item.createdAt) }}</td>
            <td>{{ item.type }}</td>
            <td>
              <div class="unit-tags">
                <span
                  v-for="(unit, index) in getUnitArray(item.announcementUnit)"
                  :key="index"
                  class="unit-tag"
                >
                  {{ unit }}
                </span>
              </div>
            </td>
            <td>{{ item.title }}</td>
            <td>{{ item.informer }}</td>
            <td>
              <button
                v-if="!isShowingHidden"
                class="edit-btn"
                @click.stop="hideAnnouncement(item.id)"
              >
                <i class="fas fa-eye-slash"></i>
                隱藏
              </button>
              <button
                v-else
                class="edit-btn"
                @click.stop="showAnnouncement(item.id)"
              >
                <i class="fas fa-eye"></i>
                顯示
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="loading" class="loading-indicator">
        <i class="fas fa-spinner fa-spin"></i> 載入中...
      </div>

      <div v-if="!loading && announcementList.length === 0" class="empty-state">
        暫無公告資料，請點擊「新增公告」添加
      </div>
    </div>

    <div class="pagination">
      <button
        @click="currentPage > 1 && (currentPage--)"
        :disabled="currentPage === 1"
        class="pagination-btn"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      <span class="page-number">{{ currentPage }}</span>
      <button
        @click="currentPage < totalPages && (currentPage++)"
        :disabled="currentPage >= totalPages"
        class="pagination-btn"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>

    <AnnouncementForm
      :visible="showAnnouncementForm"
      :edit-data="currentEditAnnouncement"
      :edit-mode="isEditMode"
      @close="closeAnnouncementForm"
      @save="saveAnnouncement"
      @delete="deleteAnnouncement"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { debounce } from 'lodash-es';
import AnnouncementForm from '../../components/AnnouncementForm.vue';
import { showSuccess, showError, showConfirm } from '../../services/notificationService';
import api from '../../services/api';

const searchQuery = ref('');
const announcementList = ref([]);
const loading = ref(false);
const isShowingHidden = ref(false); // 追踪當前顯示的是否為隱藏公告

const hideAnnouncement = async (id) => {
  showConfirm(
    `確定要隱藏此公告嗎？`,
    async () => {
      try {
        loading.value = true;

        const token = localStorage.getItem('token');
        if (!token) {
          console.error('使用者未登入');
          return;
        }

        const response = await api.announcement.update(id, { display: 0 });

        if (response.status === 'success') {
          // 如果當前正在顯示正常公告，則從列表中移除該公告
          if (!isShowingHidden.value) {
            announcementList.value = announcementList.value.filter(item => item.id !== id);
          }
          showSuccess('公告已隱藏');
        } else {
          showError('隱藏公告失敗');
        }
      } catch (error) {
        console.error('隱藏公告錯誤:', error);
        showError('隱藏公告時出錯，請稍後再試');
      } finally {
        loading.value = false;
      }
    },
    null,
    '確認隱藏',
    'warning'
  );
};

const showAnnouncement = async (id) => {
  showConfirm(
    `確定要顯示此公告嗎？`,
    async () => {
      try {
        loading.value = true;

        const token = localStorage.getItem('token');
        if (!token) {
          console.error('使用者未登入');
          return;
        }

        const response = await api.announcement.update(id, { display: 1 });

        if (response.status === 'success') {
          // 如果當前正在顯示隱藏公告，則從列表中移除該公告
          if (isShowingHidden.value) {
            announcementList.value = announcementList.value.filter(item => item.id !== id);
          }
          showSuccess('公告已顯示');
        } else {
          showError('顯示公告失敗');
        }
      } catch (error) {
        console.error('顯示公告錯誤:', error);
        showError('顯示公告時出錯，請稍後再試');
      } finally {
        loading.value = false;
      }
    },
    null,
    '確認顯示',
    'info'
  );
};

const showAnnouncementForm = ref(false);
const currentEditAnnouncement = ref(null);
const isEditMode = ref(false);
const currentPage = ref(1);
const itemsPerPage = 10;
const totalPages = computed(() => Math.ceil(announcementList.value.length / itemsPerPage));

onMounted(async () => {
  await fetchAnnouncementList();

  // 監聽搜尋框的變化，使用debounce避免頻繁請求
  watch(
    () => searchQuery.value,
    debounce(async () => {
      await handleSearch();
    }, 300)
  );
});

// 切換顯示隱藏公告或正常公告
const toggleAnnouncementVisibility = async () => {
  try {
    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    // 切換狀態
    isShowingHidden.value = !isShowingHidden.value;

    try {
      const response = await api.announcement.getAll();

      if (response.status === 'success' && response.data && response.data.length > 0) {
        // 根據當前狀態篩選公告
        // isShowingHidden 為 true 時顯示隱藏公告 (display = 0)
        // isShowingHidden 為 false 時顯示正常公告 (display = 1)
        announcementList.value = response.data.filter(item =>
          isShowingHidden.value ? item.display === 0 : item.display === 1
        );
      } else {
        // 當API返回空列表時
        announcementList.value = [];
      }
    } catch (error) {
      console.error('API請求失敗:', error);
      // 發生錯誤時設置為空列表
      announcementList.value = [];
    }
  } catch (error) {
    console.error(`獲取${isShowingHidden.value ? '隱藏' : '正常'}公告列表錯誤:`, error);
  } finally {
    loading.value = false;
  }
};

const fetchAnnouncementList = async () => {
  try {
    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    try {
      const response = await api.announcement.getAll();

      if (response.status === 'success' && response.data && response.data.length > 0) {
        // 根據當前狀態篩選公告
        // isShowingHidden 為 true 時顯示隱藏公告 (display = 0)
        // isShowingHidden 為 false 時顯示正常公告 (display = 1)
        announcementList.value = response.data.filter(item =>
          isShowingHidden.value ? item.display === 0 : item.display === 1
        );
      } else {
        // 當API返回空列表時
        announcementList.value = [];
      }
    } catch (error) {
      console.error('API請求失敗:', error);
      // 發生錯誤時設置為空列表
      announcementList.value = [];
    }
  } catch (error) {
    console.error(`獲取${isShowingHidden.value ? '隱藏' : '正常'}公告列表錯誤:`, error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = async () => {
  try {
    if (!searchQuery.value.trim()) {
      await fetchAnnouncementList();
      return;
    }

    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    try {
      const response = await api.announcement.search(searchQuery.value);

      if (response.status === 'success' && response.data) {
        // 根據當前狀態篩選搜尋結果
        // isShowingHidden 為 true 時只顯示隱藏公告 (display = 0)
        // isShowingHidden 為 false 時只顯示正常公告 (display = 1)
        announcementList.value = response.data.filter(item =>
          isShowingHidden.value ? item.display === 0 : item.display === 1
        );
      }
    } catch (error) {
      console.error('API搜尋請求失敗:', error);
    }
  } catch (error) {
    console.error('搜尋公告錯誤:', error);
  } finally {
    loading.value = false;
  }
};

const openAnnouncementForm = () => {
  isEditMode.value = false;
  currentEditAnnouncement.value = null;
  showAnnouncementForm.value = true;
};

const viewAnnouncement = async (item) => {
  if (!item.id) {
    console.error('無法查看：缺少公告ID');
    showError('無法獲取此公告的詳細資料');
    return;
  }

  try {
    loading.value = true;
    const response = await api.announcement.getById(item.id);

    if (response.status === 'success' && response.data) {
      currentEditAnnouncement.value = response.data;
      isEditMode.value = true;
      showAnnouncementForm.value = true;
    } else {
      console.error('獲取公告詳細資料失敗:', response.message);
      showError('獲取公告詳細資料失敗');
    }
  } catch (error) {
    console.error('查看公告時發生錯誤:', error);
    showError('查看公告時出錯，請稍後再試');
  } finally {
    loading.value = false;
  }
};

const editAnnouncement = async (item) => {
  if (!item.id) {
    console.error('無法編輯：缺少公告ID');
    showError('無法獲取此公告的詳細資料');
    return;
  }

  try {
    loading.value = true;
    const response = await api.announcement.getById(item.id);

    if (response.status === 'success' && response.data) {
      currentEditAnnouncement.value = response.data;
      isEditMode.value = true;
      showAnnouncementForm.value = true;
    } else {
      console.error('獲取公告詳細資料失敗:', response.message);
      showError('獲取公告詳細資料失敗');
    }
  } catch (error) {
    console.error('編輯公告時發生錯誤:', error);
    showError('編輯公告時出錯，請稍後再試');
  } finally {
    loading.value = false;
  }
};

const closeAnnouncementForm = () => {
  showAnnouncementForm.value = false;
  isEditMode.value = false;
};

const saveAnnouncement = async (formData) => {
  try {
    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    let response;

    if (isEditMode.value && currentEditAnnouncement.value && currentEditAnnouncement.value.id) {
      response = await api.announcement.update(currentEditAnnouncement.value.id, formData);
      if (response.status === 'success' && response.data) {
        const index = announcementList.value.findIndex(item => item.id === response.data.id);
        if (index !== -1) {
          announcementList.value.splice(index, 1, response.data);
        }
      }
    } else {
      response = await api.announcement.create(formData);
      if (response.status === 'success' && response.data) {
        announcementList.value.unshift(response.data);
      }
    }

    // 關閉表單
    closeAnnouncementForm();

    // 重新獲取公告列表
    // await fetchAnnouncementList();

    // 顯示成功提示
    showSuccess(isEditMode.value ? '公告更新成功' : '公告創建成功');
  } catch (error) {
    console.error('保存公告錯誤:', error);
    showError(error.message);
  } finally {
    loading.value = false;
  }
};

const deleteAnnouncement = async (item) => {
  showConfirm(
    `確定要刪除此公告嗎？`,
    async () => {
      try {
        loading.value = true;

        const token = localStorage.getItem('token');
        if (!token) {
          console.error('使用者未登入');
          return;
        }

        const id = item.id;
        await api.announcement.delete(id);

        // 從列表中移除已刪除的項目
        announcementList.value = announcementList.value.filter(announcement => announcement.id !== id);

        // 關閉表單（如果是從視窗中刪除的話）
        closeAnnouncementForm();

        // 顯示成功提示
        showSuccess('公告刪除成功');
      } catch (error) {
        console.error('刪除公告錯誤:', error);
        showError(error.message);
      } finally {
        loading.value = false;
      }
    },
    null,
    '確認刪除',
    'warning'
  );
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 將公告單位字符串轉換為數組
const getUnitArray = (unitString) => {
  if (!unitString) return [];

  return unitString.split(',').map(unit => unit.trim());
};
</script>

<style scoped>
.announcement-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.announcement-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.announcement-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.announcement-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.announcement-actions-left {
  display: flex;
  gap: 6px;
}

.add-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-btn:hover {
  background-color: #1d4ed8;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-container span {
  font-size: 15px;
  color: #666;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 240px;
}

.announcement-table-container {
  overflow-x: auto;
}

.announcement-table {
  width: 100%;
  border-collapse: collapse;
}

.announcement-table th,
.announcement-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.announcement-table th {
  background-color: #f5f7fa;
  color: #666;
  font-weight: 500;
}

.unit-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.unit-tag {
  background-color: #e5e7eb;
  color: #4b5563;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.view-btn,
.edit-btn,
.delete-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.view-btn {
  background-color: #10b981;
  color: white;
}

.view-btn:hover {
  background-color: #059669;
}

.edit-btn {
  background-color: #3b82f6;
  color: white;
}

.edit-btn:hover {
  background-color: #2563eb;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
  font-style: italic;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 10px;
}

.pagination-btn {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.page-number {
  font-size: 14px;
  padding: 0 10px;
}
</style>
