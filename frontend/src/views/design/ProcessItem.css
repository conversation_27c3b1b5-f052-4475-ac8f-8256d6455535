/* 工序項目基本樣式 */
.process-item {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px 6px 8px;
  text-align: center;
  position: relative;
  min-width: 50px;
  max-width: 120px;
  font-size: 0.9em;
  color: #0369a1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  margin: 0 auto;
  cursor: move;
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 1;
}

/* 工序項目順序號碼 */
.process-item-order {
  position: absolute;
  top: -8px;
  left: -8px;
  background-color: #0b0b0bb9;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 固定工序的順序號碼背景色 */
.process-item.fixed .process-item-order {
  background-color: #4f46e5; /* 與固定工序圖標背景色相同的紫色 */
}

/* 工序名稱 */
.process-item-name {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 小工序名稱 */
.process-item-minor {
  font-size: 0.8em;
  color: #64748b;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}



/* 工序完成狀態樣式 */
.process-item.completed {
  background-color: #bbf7d0;
  border-color: #4ade80;
  color: #166534;
}





/* 工序高亮樣式 */
.process-item.highlighted {
  background-color: #bae6fd;
  border-color: #0284c7;
  box-shadow: 0 0 0 2px rgba(2, 132, 199, 0.4);
  transform: translateY(-2px);
  z-index: 11;
  opacity: 1 !important;
}

/* 工序固定樣式 */
.process-item.fixed {
  box-shadow: 0 0 0 2px #4f46e5;
  border-color: #4f46e5;
  background-color: #e0e7ff;
  color: #4338ca;
  position: relative;
}

/* 添加固定圖標 */
.process-item.fixed::after {
  content: '\f08d'; /* 固定圖標的 Unicode */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #4f46e5;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: rotate(45deg);
}

