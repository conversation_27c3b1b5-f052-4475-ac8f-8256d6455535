/**
 * 歷史記錄管理器測試文件
 * 用於驗證歷史記錄功能的正確性
 */

import HistoryManager from './historyManager.js';

// 測試歷史記錄管理器
function testHistoryManager() {
  console.log('開始測試歷史記錄管理器...');
  
  const historyManager = new HistoryManager(3); // 設置最大3個歷史記錄用於測試
  
  // 測試1：保存狀態
  console.log('\n測試1：保存狀態');
  const state1 = { processForm: { pieceName: '測試1' }, savedProcesses: [] };
  const state2 = { processForm: { pieceName: '測試2' }, savedProcesses: [{ id: 1 }] };
  const state3 = { processForm: { pieceName: '測試3' }, savedProcesses: [{ id: 1 }, { id: 2 }] };
  
  historyManager.saveState(state1, '第一個狀態');
  historyManager.saveState(state2, '第二個狀態');
  historyManager.saveState(state3, '第三個狀態');
  
  console.log('歷史記錄數量:', historyManager.getStats().totalCount);
  console.log('當前索引:', historyManager.getStats().currentIndex);
  
  // 測試2：後退功能
  console.log('\n測試2：後退功能');
  const backState = historyManager.goBack();
  console.log('後退到:', backState?.description);
  console.log('當前索引:', historyManager.getStats().currentIndex);
  
  // 測試3：前進功能
  console.log('\n測試3：前進功能');
  const forwardState = historyManager.goForward();
  console.log('前進到:', forwardState?.description);
  console.log('當前索引:', historyManager.getStats().currentIndex);
  
  // 測試4：超出最大數量限制
  console.log('\n測試4：超出最大數量限制');
  const state4 = { processForm: { pieceName: '測試4' }, savedProcesses: [] };
  historyManager.saveState(state4, '第四個狀態');
  
  console.log('歷史記錄數量:', historyManager.getStats().totalCount);
  console.log('歷史記錄列表:', historyManager.getHistoryList().map(h => h.description));
  
  // 測試5：跳轉到指定索引
  console.log('\n測試5：跳轉到指定索引');
  const targetState = historyManager.goToIndex(0);
  console.log('跳轉到索引0:', targetState?.description);
  console.log('當前索引:', historyManager.getStats().currentIndex);
  
  // 測試6：序列化和恢復
  console.log('\n測試6：序列化和恢復');
  const serialized = historyManager.serialize();
  const newHistoryManager = new HistoryManager(3);
  newHistoryManager.restore(serialized);
  
  console.log('恢復後的歷史記錄數量:', newHistoryManager.getStats().totalCount);
  console.log('恢復後的當前索引:', newHistoryManager.getStats().currentIndex);
  
  console.log('\n歷史記錄管理器測試完成！');
}

// 如果在瀏覽器環境中運行，將測試函數掛載到window對象
if (typeof window !== 'undefined') {
  window.testHistoryManager = testHistoryManager;
}

export { testHistoryManager };
