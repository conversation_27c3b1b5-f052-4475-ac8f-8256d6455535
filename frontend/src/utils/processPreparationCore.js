/**
 * 備料工序視覺化、交互和編輯工具函數
 * 用於處理工序流程圖的連接線、高亮顯示、拖放排序、工序編輯和狀態管理
 */

// CSS 轉義函數，用於轉義 CSS 選擇器中的特殊字符
export const escapeCSSSelector = (str) => {
  if (!str) return '';
  return str.toString().replace(/[\!\"\#\$\%\&\'\(\)\*\+\,\.\/\:\;\<\=\>\?\@\[\\\]\^\`\{\|\}\~]/g, '\\$&');
};

/**
 * 找到從材料到當前工序的所有相關連接線和工序項目
 * @param {string} processId - 工序ID
 * @param {Set} visitedIds - 已訪問的工序ID集合，用於避免循環依賴
 * @param {string} direction - 查找方向：'incoming' - 只找連入的線, 'outgoing' - 只找連出的線, 'both' - 找兩種線, 'next-level' - 找連入的線和下一層連出的線
 * @param {number} level - 當前遞歸層級
 * @returns {Object} 包含相關連接線和工序項目的對象
 */
export const findAllRelatedItems = (processId, visitedIds = new Set(), direction = 'both', level = 0) => {
  // 避免循環依賴
  if (visitedIds.has(processId)) return { lines: [], processes: [] };
  visitedIds.add(processId);

  // 找到以此工序為目標的所有連接線（連入的線）
  const incomingLines = Array.from(document.querySelectorAll(`.process-connection-line[data-target-id="${processId}"]`));

  // 找到以此工序為來源的所有連接線（連出的線）
  const outgoingLines = (direction === 'incoming') ? [] : Array.from(document.querySelectorAll(`.process-connection-line[data-source-id="${processId}"]`));

  // 找到當前工序項目
  const currentProcess = document.querySelector(`.process-item[data-id="${processId}"]`);
  let relatedProcesses = currentProcess ? [currentProcess] : [];

  // 根據方向參數決定要包含的連接線
  let allRelatedLines = [];
  if (direction === 'incoming' || direction === 'both' || direction === 'next-level') {
    allRelatedLines = [...allRelatedLines, ...incomingLines];
  }
  if (direction === 'outgoing' || direction === 'both' || (direction === 'next-level' && level === 0)) {
    allRelatedLines = [...allRelatedLines, ...outgoingLines];
  }

  // 遍歷所有連入的連接線，找到它們的來源工序的相關連接線和工序項目
  incomingLines.forEach(line => {
    const sourceId = line.getAttribute('data-source-id');
    if (sourceId) {
      const { lines: sourceRelatedLines, processes: sourceRelatedProcesses } = findAllRelatedItems(sourceId, visitedIds, direction, level + 1);
      allRelatedLines = [...allRelatedLines, ...sourceRelatedLines];
      relatedProcesses = [...relatedProcesses, ...sourceRelatedProcesses];
    }
  });

  // 如果方向包含 outgoing，才遍歷連出的連接線
  if (direction === 'outgoing' || direction === 'both' || (direction === 'next-level' && level === 0)) {
    outgoingLines.forEach(line => {
      const targetId = line.getAttribute('data-target-id');
      if (targetId) {
        // 如果是 next-level 模式，只收集下一層的工序，不進一步遍歷
        if (direction === 'next-level') {
          const targetProcess = document.querySelector(`.process-item[data-id="${targetId}"]`);
          if (targetProcess) {
            relatedProcesses.push(targetProcess);
          }
        } else {
          const { lines: targetRelatedLines, processes: targetRelatedProcesses } = findAllRelatedItems(targetId, visitedIds, direction, level + 1);
          allRelatedLines = [...allRelatedLines, ...targetRelatedLines];
          relatedProcesses = [...relatedProcesses, ...targetRelatedProcesses];
        }
      }
    });
  }

  return { lines: allRelatedLines, processes: relatedProcesses };
};

/**
 * 更新連接線
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const updateConnections = (connectionsContainer, savedProcesses) => {
  if (!connectionsContainer) return;

  // 清除現有的連接線
  connectionsContainer.innerHTML = '';

  // 確保連接線容器的 z-index 為 5
  connectionsContainer.style.zIndex = '5';

  // 先創建一個 defs 元素來存放所有的箭頭
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  defs.style.overflow = 'visible'; // 確保 defs 元素可以正確顯示 z-index
  connectionsContainer.appendChild(defs);

  // 將所有工序的來源信息收集到一個 Map 中
  const processSourceMap = new Map();

  // 先記錄每個工序的來源
  savedProcesses.forEach(process => {
    // 創建一個陣列來存放所有的來源
    const sources = [];

    // 添加主要來源
    if (process.sourceProcessId) {
      // 如果是從已有工序選擇的，記錄來源工序 ID
      sources.push({ sourceProcessId: process.sourceProcessId, sourcePosition: null });
    } else if (process.sourcePosition) {
      // 如果是從原始材料選擇的，記錄來源材料名稱
      sources.push({ sourceProcessId: null, sourcePosition: process.sourcePosition });
    }

    // 向後兼容，處理舊版本的原始來源屬性
    if (process.originalSourceProcessId) {
      sources.push({ sourceProcessId: process.originalSourceProcessId, sourcePosition: null });
    } else if (process.originalSourcePosition) {
      sources.push({ sourceProcessId: null, sourcePosition: process.originalSourcePosition });
    }

    // 將所有來源記錄到 Map 中
    processSourceMap.set(process.id, sources);
  });

  // 為每個工序項目繪製連接線
  savedProcesses.forEach(process => {
    // 找到目標工序元素
    const targetElements = document.querySelectorAll(`.process-item[data-id="${process.id}"]`);
    if (targetElements.length === 0) return; // 如果沒有目標元素，則跳過

    const targetElement = targetElements[0];
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = connectionsContainer.getBoundingClientRect();
    const targetX = targetRect.right - containerRect.left;
    const targetY = targetRect.top + targetRect.height / 2 - containerRect.top;

    // 從記錄中獲取來源信息
    const sources = processSourceMap.get(process.id) || [];

    // 為每個來源繪製連接線
    sources.forEach(sourceInfo => {
      let sourceElement = null;

      if (sourceInfo.sourceProcessId) {
        // 如果是從已有工序選擇的，找到來源工序元素
        const sourceProcessElements = document.querySelectorAll(`.process-item[data-id="${sourceInfo.sourceProcessId}"]`);
        if (sourceProcessElements.length > 0) {
          sourceElement = sourceProcessElements[0];
        }
      } else if (sourceInfo.sourcePosition) {
        // 如果是從原始材料選擇的，找到源材料元素
        const sourceElements = document.querySelectorAll(`.position-item:not(.selected)`);

        for (const element of sourceElements) {
          if (element.textContent.trim() === sourceInfo.sourcePosition) {
            sourceElement = element;
            break;
          }
        }
      }

      // 如果沒有找到來源元素，則跳過
      if (!sourceElement) return;

      // 獲取元素的位置
      const sourceRect = sourceElement.getBoundingClientRect();

      // 計算相對位置 - 修正為從右側工序的左邊緣開始
      const sourceX = sourceRect.left - containerRect.left;
      const sourceY = sourceRect.top + sourceRect.height / 2 - containerRect.top;

      // 創建連接線的分組
      const lineGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      lineGroup.classList.add('process-connection-line');
      lineGroup.setAttribute('data-source-id', sourceInfo.sourceProcessId || '');
      lineGroup.setAttribute('data-target-id', process.id);

      // 添加唯一的 ID，使用來源和目標的組合
      const sourceId = sourceInfo.sourceProcessId || sourceInfo.sourcePosition;
      const rawConnectionId = `connection-line-${process.id}-${sourceId}`;
      // 使用轉義函數確保 ID 中的特殊字符被正確轉義
      const connectionId = rawConnectionId;
      lineGroup.setAttribute('id', connectionId);

      // 創建連接線
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      line.setAttribute('d', `M${sourceX},${sourceY} C${sourceX + (targetX - sourceX) / 2},${sourceY} ${sourceX + (targetX - sourceX) / 2},${targetY} ${targetX},${targetY}`);
      line.setAttribute('stroke', '#e2e8f0'); // 設置為非常淡的顏色
      line.setAttribute('stroke-width', '2');
      line.setAttribute('fill', 'none');

      // 設置連接線的 z-index 為 5
      lineGroup.style.zIndex = '5';

      // 將連接線添加到分組中
      lineGroup.appendChild(line);

      // 添加箭頭
      // 為每個連接線創建唯一的箭頭 ID
      // 使用簡單的數字 ID 避免特殊字符問題
      const arrowheadId = `arrowhead-${process.id}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // 檢查是否已存在相同 ID 的箭頭
      const existingMarker = document.getElementById(arrowheadId);
      if (!existingMarker) {
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', arrowheadId);
        marker.setAttribute('markerWidth', '10');
        marker.setAttribute('markerHeight', '7');
        marker.setAttribute('refX', '9');
        marker.setAttribute('refY', '3.5');
        marker.setAttribute('orient', 'auto');
        marker.setAttribute('z-index', '5'); // 設置箭頭的 z-index 為 5
        marker.style.overflow = 'visible'; // 確保 marker 元素可以正確顯示 z-index
        marker.style.zIndex = '5'; // 同時設置 style 屬性

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
        polygon.setAttribute('fill', '#e2e8f0'); // 設置為非常淡的顏色

        marker.appendChild(polygon);

        // 添加到已存在的 defs 元素中
        const defs = connectionsContainer.querySelector('defs');
        if (defs) {
          defs.appendChild(marker);
        }
      }

      // 使用唯一的箭頭 ID
      line.setAttribute('marker-end', `url(#${arrowheadId})`);

      // 將分組添加到 SVG 容器
      connectionsContainer.appendChild(lineGroup);
    });
  });
};

/**
 * 處理工序項目滑鼠移入事件
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const handleProcessItemMouseEnter = (event, savedProcesses, connectionsContainer) => {
  const processId = event.currentTarget.getAttribute('data-id');
  if (!processId) return;

  // 找到從材料到當前工序的所有相關連接線和工序項目
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  const { lines: relatedLines, processes: relatedProcesses } = findAllRelatedItems(processId, new Set(), 'next-level');

  // 找到所有固定的工序
  const fixedProcesses = savedProcesses.filter(p => p.isFixed);
  const fixedProcessIds = fixedProcesses.map(p => p.id);

  // 所有固定工序的相關連接線和工序項目
  let allFixedRelatedLines = [];
  let allFixedRelatedProcesses = [];

  // 收集所有固定工序的相關項目
  fixedProcessIds.forEach(fixedId => {
    const { lines: fixedRelatedLines, processes: fixedRelatedProcesses } = findAllRelatedItems(fixedId, new Set(), 'next-level');
    allFixedRelatedLines = [...allFixedRelatedLines, ...fixedRelatedLines];
    allFixedRelatedProcesses = [...allFixedRelatedProcesses, ...fixedRelatedProcesses];
  });

  // 將所有連接線設置為非高亮狀態並加上半透明效果，但排除固定的相關線
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    // 檢查此線是否為固定工序的相關線
    const isFixedRelatedLine = allFixedRelatedLines.some(fixedLine => fixedLine === line);

    if (!isFixedRelatedLine) {
      line.style.zIndex = '5'; // 設置為預設層級 5
      line.classList.add('faded'); // 加上半透明效果
      line.classList.remove('highlighted'); // 移除高亮

      // 確保所有箭頭也設置為預設層
      const path = line.querySelector('path');
      if (path) {
        path.setAttribute('stroke', '#e2e8f0');
        path.setAttribute('stroke-width', '2');

        const arrowheadId = path.getAttribute('marker-end');
        if (arrowheadId) {
          // 直接從 url(#id) 中提取 id
          const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
          if (arrowheadMatch && arrowheadMatch[1]) {
            const markerId = arrowheadMatch[1];
            const marker = document.getElementById(markerId);
            if (marker) {
              marker.style.zIndex = '5';
            }

            const arrowhead = document.querySelector(`#${markerId} polygon`);
            if (arrowhead) {
              arrowhead.setAttribute('fill', '#e2e8f0');
            }
          }
        }
      }
    }
  });

  // 將所有工序項目設置為非高亮狀態並加上半透明效果，但排除固定的相關工序
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    // 檢查此項目是否為固定工序的相關項目
    const isFixedRelatedProcess = allFixedRelatedProcesses.some(fixedProcess => fixedProcess === item);

    if (!isFixedRelatedProcess) {
      item.classList.remove('highlighted');
      item.classList.add('faded'); // 加上半透明效果
    }
  });

  // 將相關的連接線移動到 SVG 容器的最後，確保它們在最後渲染
  relatedLines.forEach(line => {
    // 將連接線元素移動到其父元素的最後
    const parent = line.parentNode;
    if (parent) {
      // 將連接線元素移動到 SVG 容器的最後
      const svg = connectionsContainer;
      if (svg) {
        // 先將元素從原來的父元素中移除，然後添加到 SVG 容器的最後
        svg.appendChild(line);
      } else {
        parent.appendChild(line); // 如果找不到 SVG 容器，則移動到父元素的最後
      }
    }

    line.style.zIndex = '30'; // 設置為高層級 30，顯示在高亮工序項目上方

    // 確保相關的箭頭也設置為最高層
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
          if (marker) {
            marker.style.zIndex = '30';

            // 將箭頭元素移動到 defs 的最後
            const defs = connectionsContainer.querySelector('defs');
            if (defs) {
              defs.appendChild(marker); // 移動到最後會導致它在最後渲染
            }
          }
        }
      }
    }
  });

  // 將相關連接線的顏色變深並添加高亮類
  relatedLines.forEach(lineGroup => {
    // 添加高亮類到分組
    lineGroup.classList.add('highlighted');

    // 找到分組中的連接線元素
    const line = lineGroup.querySelector('path');
    if (line) {
      // 直接設置線條的顏色
      line.setAttribute('stroke', '#475569'); // 更深的灰色
      line.setAttribute('stroke-width', '3'); // 增加線條寬度

      // 同時變深箭頭的顏色
      const arrowheadId = line.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
          const arrowhead = document.querySelector(`#arrowhead-${arrowheadMatch[1]} polygon`);
          if (marker) {
            marker.setAttribute('z-index', '30'); // 設置箭頭的 z-index 為高層級 30
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#475569'); // 更深的灰色
          }
        }
      }
    }
  });

  // 將相關工序項目高亮
  relatedProcesses.forEach(processItem => {
    processItem.classList.add('highlighted');
  });
};

/**
 * 處理工序項目滑鼠移出事件
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const handleProcessItemMouseLeave = (event, savedProcesses) => {
  const processId = event.currentTarget.getAttribute('data-id');
  if (!processId) return;

  // 找到所有固定的工序
  const fixedProcesses = savedProcesses.filter(p => p.isFixed);
  const fixedProcessIds = fixedProcesses.map(p => p.id);

  // 所有固定工序的相關連接線和工序項目
  let allFixedRelatedLines = [];
  let allFixedRelatedProcesses = [];

  // 收集所有固定工序的相關項目
  fixedProcessIds.forEach(fixedId => {
    const { lines: fixedRelatedLines, processes: fixedRelatedProcesses } = findAllRelatedItems(fixedId, new Set(), 'next-level');
    allFixedRelatedLines = [...allFixedRelatedLines, ...fixedRelatedLines];
    allFixedRelatedProcesses = [...allFixedRelatedProcesses, ...fixedRelatedProcesses];
  });

  // 將所有連接線恢復為預設狀態並移除半透明效果，但保留固定的相關線的高亮
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    // 檢查此線是否為固定工序的相關線
    const isFixedRelatedLine = allFixedRelatedLines.some(fixedLine => fixedLine === line);

    line.classList.remove('faded'); // 移除半透明效果

    if (!isFixedRelatedLine) {
      line.style.zIndex = '5'; // 設置為預設層級 5
      line.classList.remove('highlighted'); // 移除高亮類

      // 確保所有箭頭也恢復為預設狀態
      const path = line.querySelector('path');
      if (path) {
        path.setAttribute('stroke', '#e2e8f0'); // 淡色
        path.setAttribute('stroke-width', '2'); // 恢復原始寬度

        const arrowheadId = path.getAttribute('marker-end');
        if (arrowheadId) {
          // 直接從 url(#id) 中提取 id
          const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
          if (arrowheadMatch && arrowheadMatch[1]) {
            const markerId = arrowheadMatch[1];
            const marker = document.getElementById(markerId);
            if (marker) {
              marker.style.zIndex = '5';
            }

            const arrowhead = marker ? marker.querySelector('polygon') : null;
            if (arrowhead) {
              arrowhead.setAttribute('fill', '#e2e8f0');
            }
          }
        }
      }
    }
  });

  // 將所有工序項目恢復為預設狀態並移除半透明效果，但保留固定的相關工序的高亮
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    // 檢查此項目是否為固定工序的相關項目
    const isFixedRelatedProcess = allFixedRelatedProcesses.some(fixedProcess => fixedProcess === item);

    item.classList.remove('faded'); // 移除半透明效果

    if (!isFixedRelatedProcess) {
      item.classList.remove('highlighted'); // 移除高亮
    }
  });

  // 重新應用固定工序的高亮
  fixedProcessIds.forEach(fixedId => {
    const { lines: fixedRelatedLines, processes: fixedRelatedProcesses } = findAllRelatedItems(fixedId, new Set(), 'next-level');

    // 高亮固定工序的相關連接線
    fixedRelatedLines.forEach(line => {
      line.classList.add('highlighted');
      line.style.zIndex = '30';

      const path = line.querySelector('path');
      if (path) {
        path.setAttribute('stroke', '#475569');
        path.setAttribute('stroke-width', '3');

        const arrowheadId = path.getAttribute('marker-end');
        if (arrowheadId) {
          const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
          if (arrowheadMatch && arrowheadMatch[1]) {
            const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
            const arrowhead = document.querySelector(`#arrowhead-${arrowheadMatch[1]} polygon`);
            if (marker) {
              marker.setAttribute('z-index', '30');
            }
            if (arrowhead) {
              arrowhead.setAttribute('fill', '#475569');
            }
          }
        }
      }
    });

    // 高亮固定工序的相關工序項目
    fixedRelatedProcesses.forEach(processItem => {
      processItem.classList.add('highlighted');
    });
  });
};

/**
 * 處理材料項目滑鼠移入事件
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const handleMaterialItemMouseEnter = (event, savedProcesses, connectionsContainer) => {
  const materialName = event.currentTarget.textContent.trim();
  if (!materialName) return;

  // 找到所有以此材料為來源的工序
  const relatedProcesses = savedProcesses.filter(process => process.sourcePosition === materialName);

  // 對每個相關工序，找到它的所有相關連接線和工序項目
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  let allRelatedLines = [];
  let allRelatedProcesses = [];
  relatedProcesses.forEach(process => {
    const { lines: processRelatedLines, processes: processRelatedProcesses } = findAllRelatedItems(process.id, new Set(), 'next-level');
    allRelatedLines = [...allRelatedLines, ...processRelatedLines];
    allRelatedProcesses = [...allRelatedProcesses, ...processRelatedProcesses];
  });

  // 找到所有固定的工序
  const fixedProcesses = savedProcesses.filter(p => p.isFixed);
  const fixedProcessIds = fixedProcesses.map(p => p.id);

  // 所有固定工序的相關連接線和工序項目
  let allFixedRelatedLines = [];
  let allFixedRelatedProcesses = [];

  // 收集所有固定工序的相關項目
  fixedProcessIds.forEach(fixedId => {
    const { lines: fixedRelatedLines, processes: fixedRelatedProcesses } = findAllRelatedItems(fixedId, new Set(), 'next-level');
    allFixedRelatedLines = [...allFixedRelatedLines, ...fixedRelatedLines];
    allFixedRelatedProcesses = [...allFixedRelatedProcesses, ...fixedRelatedProcesses];
  });

  // 將所有連接線設置為非高亮狀態並加上半透明效果，但排除固定的相關線
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    // 檢查此線是否為固定工序的相關線
    const isFixedRelatedLine = allFixedRelatedLines.some(fixedLine => fixedLine === line);

    if (!isFixedRelatedLine) {
      line.style.zIndex = '5'; // 設置為預設層級 5
      line.classList.add('faded'); // 加上半透明效果
      line.classList.remove('highlighted'); // 移除高亮
    }
  });

  // 將所有工序項目設置為非高亮狀態並加上半透明效果，但排除固定的相關工序
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    // 檢查此項目是否為固定工序的相關項目
    const isFixedRelatedProcess = allFixedRelatedProcesses.some(fixedProcess => fixedProcess === item);

    if (!isFixedRelatedProcess) {
      item.classList.remove('highlighted');
      item.classList.add('faded'); // 加上半透明效果
    }
  });

  // 高亮相關連接線
  allRelatedLines.forEach(line => {
    line.classList.add('highlighted');
    line.classList.remove('faded');
    line.style.zIndex = '30';

    const path = line.querySelector('path');
    if (path) {
      path.setAttribute('stroke', '#475569');
      path.setAttribute('stroke-width', '3');

      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          const arrowhead = document.querySelector(`#${markerId} polygon`);
          if (marker) {
            marker.style.zIndex = '30';
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#475569');
          }
        }
      }
    }
  });

  // 高亮相關工序項目
  allRelatedProcesses.forEach(processItem => {
    processItem.classList.add('highlighted');
    processItem.classList.remove('faded');
  });

  // 高亮當前材料項目
  event.currentTarget.classList.add('highlighted');
};

/**
 * 處理材料項目滑鼠移出事件
 * @param {Event} event - 滑鼠事件
 * @param {Array} savedProcesses - 已保存的工序列表
 */
export const handleMaterialItemMouseLeave = (event, savedProcesses) => {
  // 移除當前材料項目的高亮
  event.currentTarget.classList.remove('highlighted');

  // 使用與工序項目滑鼠移出事件相同的邏輯
  handleProcessItemMouseLeave({ currentTarget: { getAttribute: () => null } }, savedProcesses);
};

/**
 * 設置工序項目和材料項目的滑鼠事件監聽
 * @param {Array} savedProcesses - 已保存的工序列表
 * @param {HTMLElement} connectionsContainer - SVG連接線容器元素
 */
export const setupProcessItemHoverEvents = (savedProcesses, connectionsContainer) => {
  // 首先確保所有工序項目的 fixed 類別與數據模型中的 isFixed 屬性一致
  const allProcessIds = savedProcesses.map(p => p.id);
  allProcessIds.forEach(id => {
    const process = savedProcesses.find(p => p.id === id);
    const processElement = document.querySelector(`.process-item[data-id="${id}"]`);

    if (process && processElement) {
      if (process.isFixed) {
        processElement.classList.add('fixed');
      } else {
        processElement.classList.remove('fixed');
      }
    }
  });

  // 為所有工序項目添加滑鼠事件
  const processItems = document.querySelectorAll('.process-item');

  processItems.forEach(item => {
    // 移除現有的事件監聽器，避免重複添加
    const oldEnterHandler = item._mouseenterHandler;
    const oldLeaveHandler = item._mouseleaveHandler;

    if (oldEnterHandler) {
      item.removeEventListener('mouseenter', oldEnterHandler);
    }

    if (oldLeaveHandler) {
      item.removeEventListener('mouseleave', oldLeaveHandler);
    }

    // 添加新的事件監聽器
    if (!item.classList.contains('fixed')) {
      const enterHandler = (e) => handleProcessItemMouseEnter(e, savedProcesses, connectionsContainer);
      const leaveHandler = (e) => handleProcessItemMouseLeave(e, savedProcesses);

      item._mouseenterHandler = enterHandler;
      item._mouseleaveHandler = leaveHandler;

      item.addEventListener('mouseenter', enterHandler);
      item.addEventListener('mouseleave', leaveHandler);
    }
  });

  // 為所有材料項目添加滑鼠事件
  const materialItems = document.querySelectorAll('.position-item');

  materialItems.forEach(item => {
    // 移除現有的事件監聽器，避免重複添加
    const oldEnterHandler = item._mouseenterHandler;
    const oldLeaveHandler = item._mouseleaveHandler;

    if (oldEnterHandler) {
      item.removeEventListener('mouseenter', oldEnterHandler);
    }

    if (oldLeaveHandler) {
      item.removeEventListener('mouseleave', oldLeaveHandler);
    }

    // 添加新的事件監聽器
    const enterHandler = (e) => handleMaterialItemMouseEnter(e, savedProcesses, connectionsContainer);
    const leaveHandler = (e) => handleMaterialItemMouseLeave(e, savedProcesses);

    item._mouseenterHandler = enterHandler;
    item._mouseleaveHandler = leaveHandler;

    item.addEventListener('mouseenter', enterHandler);
    item.addEventListener('mouseleave', leaveHandler);
  });
}; 