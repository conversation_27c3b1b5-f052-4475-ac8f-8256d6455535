/**
 * 備料工序靜態數據模組
 * 包含所有備料工序相關的靜態數據，如大工序列表、部位組織列表、分片組織列表等
 */

/**
 * 大工序列表
 * 包含所有備料工序的大工序名稱
 */
export const majorProcesses = ['A選料', 'B開料', 'C備料', 'D塗邊', 'E製作', 'F包裝', 'G補料'];

/**
 * 預設的部位名稱示例
 * 用於初始化部位名稱列表
 */
export const defaultPositionNames = [
  '-1前上片皮',
  '-2後上片皮',
  '-3左側皮',
  '-4左側內'
];

/**
 * 部位組織列表
 * 包含所有部位組織的代碼和名稱
 */
export const partStructureList = [
  { code: 'A', name: '袋蓋' },
  { code: 'B', name: '前身' },
  { code: 'C', name: '後身' },
  { code: 'D', name: '側片' },
  { code: 'E', name: '底片' },
  { code: 'F', name: '內身' },
  { code: 'G', name: '附件' },
  { code: 'H', name: '背帶' },
  { code: 'I', name: '百褚' }
];

/**
 * 分片組織列表
 * 按部位組織代碼分類的分片組織列表
 */
export const pieceStructureList = {
  'A': [
    { code: 'U', name: '上' }
  ],
  'B': [
    { code: 'D', name: '下' }
  ],
  'C': [
    { code: 'L', name: '左' }
  ],
  'D': [
    { code: 'R', name: '右' }
  ],
  'E': [
    { code: 'M', name: '中' }
  ],
  'F': [
    { code: '/', name: '身片' }
  ],
  'G': [
    { code: '#', name: '共用分片' }
  ],
  'H': [
    { code: '+', name: '組合' }
  ],
  'I': []
};

/**
 * 材料區列表
 * 包含所有可用的材料
 */
export const materialList = [
  { name: 'A牛' },
  { name: '裡布' }
];

/**
 * 小工序列表
 * 按大工序分類的小工序列表
 */
export const minorProcessList = {
  'A選料': [
    { code: '01', name: '選皮' },
    { code: '02', name: '畫皮' }
  ],
  'B開料': [
    { code: '01', name: '電裁' },
    { code: '02', name: '裁斷' },
    { code: '03', name: '手裁' }
  ],
  'C備料': [
    { code: '01', name: '起皮' },
    { code: '02', name: '燒金' },
    { code: '03', name: '烤印' },
    { code: '04', name: '削邊' }
  ],
  'D塗邊': [
    { code: '01', name: '塗填充' },
    { code: '02', name: '邊油上色' }
  ],
  'E製作': [
    { code: '01', name: '手工上膠(刷膠)' },
    { code: '02', name: '機器上膠(噴膠)' },
    { code: '03', name: '貼合(搭接,滾平)' },
    { code: '04', name: '畫線' },
    { code: '05', name: '打磨' },
    { code: '06', name: '補色' },
    { code: '07', name: '折邊' },
    { code: '08', name: '包邊(法國滾)' },
    { code: '09', name: '拉邊' },
    { code: '10', name: '撿角' },
    { code: '11', name: '壓線' },
    { code: '12', name: '燒線' },
    { code: '13', name: '手縫' },
    { code: '14', name: '收線' },
    { code: '15', name: '車縫' },
    { code: '16', name: '手削薄' },
    { code: '17', name: '手工修邊' },
    { code: '18', name: '機器修邊' },
    { code: '19', name: '裁實模' },
    { code: '20', name: '塑型' },
    { code: '21', name: '燒熱' },
    { code: '22', name: '分條' },
    { code: '23', name: '沖孔,打洞' },
    { code: '24', name: 'A釘五金製作中' },
    { code: '25', name: 'B釘五金製作後' },
    { code: '26', name: '整理' }
  ],
  'F包裝': [
    { code: '01', name: '準備前製' },
    { code: '02', name: '品檢' },
    { code: '03', name: '包前(裝箱)' }
  ],
  'G補料': [
    { code: '01', name: '補分片' }
  ]
};

/**
 * 工具列表
 * 按工序代碼分類的工具列表
 */
export const toolList = {
  'A選料-01': ['電裁機'],
  'A選料-02': ['銀筆'],
  'B開料-01': ['電裁機'],
  'B開料-02': ['小沖台', '大沖台'],
  'C備料-01': ['起皮機'],
  'C備料-02': ['烤印機'],
  'C備料-03': ['烤印機'],
  'C備料-04': ['削邊機'],
  'D塗邊-01': ['手工填縫', '邊油機'],
  'D塗邊-02': ['邊油機'],
  'E製作-01': ['上膠刷'],
  'E製作-02': ['噴膠機'],
  'E製作-03': ['滾平機', '一段輪'],
  'E製作-04': ['銀筆'],
  'E製作-05': ['手持式打粗機'],
  'E製作-07': ['折尺'],
  'E製作-08': ['669機台'],
  'E製作-09': ['美工刀'],
  'E製作-10': ['錫子'],
  'E製作-11': ['壓線機'],
  'E製作-12': ['電烤鐵'],
  'E製作-13': ['手縫針'],
  'E製作-14': ['紗剪'],
  'E製作-15': ['669機台', '869機台', 'JUKI平車', '包邊機'],
  'E製作-16': ['裁刀'],
  'E製作-17': ['裁刀'],
  'E製作-18': ['修邊機'],
  'E製作-19': ['沖台'],
  'E製作-20': ['烤箱'],
  'E製作-21': ['燒斗'],
  'E製作-22': ['分條機'],
  'E製作-23': ['六分頭', '圓沖,鐵鉗,斬版'],
  'E製作-24': ['沖模,鐵鍊'],
  'E製作-25': ['模具,鐵鍊']
};

/**
 * 耗材列表
 * 按工序代碼分類的耗材列表
 */
export const consumableList = {
  'C備料-02': ['燒金紙'],
  'D塗邊-01': ['填縫劑'],
  'D塗邊-02': ['各色邊油'],
  'E製作-01': ['無苯黃膠'],
  'E製作-02': ['水性白膠'],
  'E製作-05': ['鑽石磨棒', '砂紙磨頭'],
  'E製作-06': ['邊油'],
  'E製作-08': ['車線'],
  'E製作-10': ['膠水'],
  'E製作-13': ['車線'],
  'E製作-15': ['車針,車線', '車針,車線', '車針,車線', '車針,車線'],
  'E製作-18': ['膠版'],
  'E製作-19': ['膠版'],
  'E製作-24': ['膠版'],
  'E製作-25': ['膠版'],
  'F包裝-01': ['米紙'],
  'F包裝-03': ['防塵袋']
};

/**
 * 導出所有靜態數據
 */
export default {
  majorProcesses,
  defaultPositionNames,
  partStructureList,
  pieceStructureList,
  materialList,
  minorProcessList,
  toolList,
  consumableList
};
