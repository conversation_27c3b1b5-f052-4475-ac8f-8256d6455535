import { ref, computed } from 'vue';
import { processHistoryApi } from '../services/api/processHistory';

/**
 * 歷史記錄管理模組 - Vue 3 組合式 API 版本
 * 用於管理工序編輯的歷史狀態，支援最多20個版本的保存和恢復
 * 支援後端資料庫同步
 */

export function createHistoryManager(maxHistorySize = 20, bomId = null) {
  // 響應式狀態
  const history = ref([]);
  const currentIndex = ref(-1);
  const isNavigating = ref(false); // 防止在導航時觸發新的歷史記錄
  const isSyncing = ref(false); // 防止在同步時觸發新的保存

  // 計算屬性
  const canGoBack = computed(() => currentIndex.value > 0);
  const canGoForward = computed(() => currentIndex.value < history.value.length - 1);
  const stats = computed(() => ({
    totalCount: history.value.length,
    currentIndex: currentIndex.value,
    canGoBack: canGoBack.value,
    canGoForward: canGoForward.value,
    maxSize: maxHistorySize
  }));

  const historyList = computed(() => {
    return history.value.map((item, index) => ({
      id: item.id,
      index: index,
      description: item.description,
      timestamp: item.timestamp,
      isCurrent: index === currentIndex.value
    }));
  });

  /**
   * 保存當前狀態到歷史記錄
   * @param {Object} state - 要保存的狀態對象
   * @param {string} description - 狀態描述
   * @param {boolean} syncToBackend - 是否同步到後端，默認為 true
   */
  const saveState = async (state, description = '', syncToBackend = true) => {
    // 如果正在導航中或正在同步，不保存新狀態
    if (isNavigating.value || isSyncing.value) {
      return;
    }

    // 深拷貝狀態以避免引用問題
    const stateCopy = JSON.parse(JSON.stringify(state));
    
    // 創建歷史記錄項目
    const historyItem = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      description: description || `編輯狀態 ${history.value.length + 1}`,
      state: stateCopy
    };

    // 如果當前不在最新位置，移除後面的歷史記錄
    if (currentIndex.value < history.value.length - 1) {
      history.value = history.value.slice(0, currentIndex.value + 1);
    }

    // 添加新的歷史記錄
    history.value.push(historyItem);

    // 如果超過最大數量，移除最舊的記錄
    if (history.value.length > maxHistorySize) {
      history.value.shift();
    } else {
      currentIndex.value++;
    }

    // 確保 currentIndex 不超出範圍
    currentIndex.value = Math.min(currentIndex.value, history.value.length - 1);

    // 同步到後端
    if (syncToBackend && bomId) {
      try {
        await processHistoryApi.saveHistory(bomId, historyItem.description, historyItem.state);
      } catch (error) {
        console.error('同步歷史記錄到後端失敗:', error);
        // 不影響前端功能，只是記錄錯誤
      }
    }
  };

  /**
   * 後退到上一個狀態
   * @returns {Object|null} 上一個狀態，如果沒有則返回 null
   */
  const goBack = () => {
    if (!canGoBack.value) {
      return null;
    }

    isNavigating.value = true;
    currentIndex.value--;
    const state = history.value[currentIndex.value];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      isNavigating.value = false;
    }, 100);

    return state;
  };

  /**
   * 前進到下一個狀態
   * @returns {Object|null} 下一個狀態，如果沒有則返回 null
   */
  const goForward = () => {
    if (!canGoForward.value) {
      return null;
    }

    isNavigating.value = true;
    currentIndex.value++;
    const state = history.value[currentIndex.value];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      isNavigating.value = false;
    }, 100);

    return state;
  };

  /**
   * 跳轉到指定索引的狀態
   * @param {number} index - 目標索引
   * @returns {Object|null} 目標狀態，如果索引無效則返回 null
   */
  const goToIndex = (index) => {
    if (index < 0 || index >= history.value.length) {
      return null;
    }

    isNavigating.value = true;
    currentIndex.value = index;
    const state = history.value[currentIndex.value];
    
    setTimeout(() => {
      isNavigating.value = false;
    }, 100);

    return state;
  };

  /**
   * 獲取當前狀態
   * @returns {Object|null}
   */
  const getCurrentState = () => {
    if (currentIndex.value >= 0 && currentIndex.value < history.value.length) {
      return history.value[currentIndex.value];
    }
    return null;
  };

  /**
   * 清除所有歷史記錄
   * @param {boolean} syncToBackend - 是否同步到後端，默認為 true
   */
  const clear = async (syncToBackend = true) => {
    history.value = [];
    currentIndex.value = -1;
    isNavigating.value = false;

    // 同步到後端
    if (syncToBackend && bomId) {
      try {
        await processHistoryApi.clearHistory(bomId);
      } catch (error) {
        console.error('清除後端歷史記錄失敗:', error);
      }
    }
  };

  /**
   * 從後端載入歷史記錄
   */
  const loadFromBackend = async () => {
    if (!bomId) {
      console.warn('未提供 bomId，無法從後端載入歷史記錄');
      return;
    }

    try {
      isSyncing.value = true;
      const response = await processHistoryApi.getHistoryList(bomId, maxHistorySize);
      
      if (response.success && response.data) {
        // 清除本地歷史記錄
        history.value = [];
        currentIndex.value = -1;

        // 載入後端歷史記錄
        const backendHistory = response.data.map(item => ({
          id: item.id,
          timestamp: item.timestamp,
          description: item.description,
          state: item.state
        }));

        // 按時間排序 (最舊的在前)
        backendHistory.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        history.value = backendHistory;
        currentIndex.value = backendHistory.length - 1;
      }
    } catch (error) {
      console.error('從後端載入歷史記錄失敗:', error);
    } finally {
      isSyncing.value = false;
    }
  };

  /**
   * 初始化歷史管理器 (從後端載入數據)
   */
  const initialize = async () => {
    await loadFromBackend();
  };

  /**
   * 從 JSON 數據恢復歷史記錄
   * @param {Object} data - 序列化的歷史數據
   */
  const restore = (data) => {
    if (data && Array.isArray(data.history)) {
      history.value = data.history;
      currentIndex.value = data.currentIndex || -1;
      
      // 確保索引在有效範圍內
      if (currentIndex.value >= history.value.length) {
        currentIndex.value = history.value.length - 1;
      }
    }
  };

  /**
   * 序列化歷史記錄為 JSON
   * @returns {Object}
   */
  const serialize = () => {
    return {
      history: history.value,
      currentIndex: currentIndex.value,
      maxHistorySize: maxHistorySize
    };
  };

  // 返回所有方法和狀態
  return {
    // 狀態
    history: history.value,
    currentIndex: currentIndex.value,
    isNavigating: isNavigating.value,
    isSyncing: isSyncing.value,
    
    // 計算屬性
    canGoBack,
    canGoForward,
    stats,
    historyList,
    
    // 方法
    saveState,
    goBack,
    goForward,
    goToIndex,
    getCurrentState,
    clear,
    restore,
    serialize,
    loadFromBackend,
    initialize,
    
    // 兼容舊版本的方法
    getStats: () => stats.value,
    getHistoryList: () => historyList.value
  };
}

// 保留舊版本的類定義以維持向後兼容性
class HistoryManager {
  constructor(maxHistorySize = 10) {
    this.maxHistorySize = maxHistorySize;
    this.history = [];
    this.currentIndex = -1;
    this.isNavigating = false; // 防止在導航時觸發新的歷史記錄
  }

  /**
   * 保存當前狀態到歷史記錄
   * @param {Object} state - 要保存的狀態對象
   * @param {string} description - 狀態描述
   */
  saveState(state, description = '') {
    // 如果正在導航中，不保存新狀態
    if (this.isNavigating) {
      return;
    }

    // 深拷貝狀態以避免引用問題
    const stateCopy = JSON.parse(JSON.stringify(state));
    
    // 創建歷史記錄項目
    const historyItem = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      description: description || `編輯狀態 ${this.history.length + 1}`,
      state: stateCopy
    };

    // 如果當前不在最新位置，移除後面的歷史記錄
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    // 添加新的歷史記錄
    this.history.push(historyItem);

    // 如果超過最大數量，移除最舊的記錄
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    } else {
      this.currentIndex++;
    }

    // 確保 currentIndex 不超出範圍
    this.currentIndex = Math.min(this.currentIndex, this.history.length - 1);
  }

  /**
   * 後退到上一個狀態
   * @returns {Object|null} 上一個狀態，如果沒有則返回 null
   */
  goBack() {
    if (!this.canGoBack()) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex--;
    const state = this.history[this.currentIndex];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 前進到下一個狀態
   * @returns {Object|null} 下一個狀態，如果沒有則返回 null
   */
  goForward() {
    if (!this.canGoForward()) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex++;
    const state = this.history[this.currentIndex];
    
    // 延遲重置導航標誌，確保狀態更新完成
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 跳轉到指定索引的狀態
   * @param {number} index - 目標索引
   * @returns {Object|null} 目標狀態，如果索引無效則返回 null
   */
  goToIndex(index) {
    if (index < 0 || index >= this.history.length) {
      return null;
    }

    this.isNavigating = true;
    this.currentIndex = index;
    const state = this.history[this.currentIndex];
    
    setTimeout(() => {
      this.isNavigating = false;
    }, 100);

    return state;
  }

  /**
   * 檢查是否可以後退
   * @returns {boolean}
   */
  canGoBack() {
    return this.currentIndex > 0;
  }

  /**
   * 檢查是否可以前進
   * @returns {boolean}
   */
  canGoForward() {
    return this.currentIndex < this.history.length - 1;
  }

  /**
   * 獲取當前狀態
   * @returns {Object|null}
   */
  getCurrentState() {
    if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
      return this.history[this.currentIndex];
    }
    return null;
  }

  /**
   * 獲取歷史記錄列表（用於顯示）
   * @returns {Array}
   */
  getHistoryList() {
    return this.history.map((item, index) => ({
      id: item.id,
      index: index,
      description: item.description,
      timestamp: item.timestamp,
      isCurrent: index === this.currentIndex
    }));
  }

  /**
   * 清除所有歷史記錄
   */
  clear() {
    this.history = [];
    this.currentIndex = -1;
    this.isNavigating = false;
  }

  /**
   * 獲取歷史記錄統計信息
   * @returns {Object}
   */
  getStats() {
    return {
      totalCount: this.history.length,
      currentIndex: this.currentIndex,
      canGoBack: this.canGoBack(),
      canGoForward: this.canGoForward(),
      maxSize: this.maxHistorySize
    };
  }

  /**
   * 從 JSON 數據恢復歷史記錄
   * @param {Object} data - 序列化的歷史數據
   */
  restore(data) {
    if (data && Array.isArray(data.history)) {
      this.history = data.history;
      this.currentIndex = data.currentIndex || -1;
      
      // 確保索引在有效範圍內
      if (this.currentIndex >= this.history.length) {
        this.currentIndex = this.history.length - 1;
      }
    }
  }

  /**
   * 序列化歷史記錄為 JSON
   * @returns {Object}
   */
  serialize() {
    return {
      history: this.history,
      currentIndex: this.currentIndex,
      maxHistorySize: this.maxHistorySize
    };
  }
}

export default HistoryManager;
