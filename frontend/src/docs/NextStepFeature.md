# 工序列表「下一步」功能使用說明

## 功能概述

「下一步」功能為工序列表提供了完整的編輯歷史記錄管理，讓用戶可以在不同的編輯狀態間自由切換，大大提升了工作效率和容錯性。

## 功能位置

下一步控制項位於工序列表頁面的頂部中央區域，包含以下元素：

- **後退按鈕**（←）：返回到上一個編輯狀態
- **前進按鈕**（→）：前進到下一個編輯狀態  
- **保存按鈕**（🔖）：手動保存當前編輯狀態
- **歷史記錄按鈕**（🕐）：顯示/隱藏歷史記錄面板
- **狀態指示器**：顯示當前位置（如：3/10）

## 主要功能

### 1. 自動狀態保存
系統會在以下操作時自動保存編輯狀態：
- 頁面初始載入
- 開始編輯工序
- 保存工序
- 刪除工序
- 批量新增工序
- 清除所有工序

### 2. 手動狀態保存
點擊保存按鈕（🔖）可以手動保存當前的編輯狀態，適用於：
- 重要的編輯節點
- 實驗性的修改前
- 需要標記的特定狀態

### 3. 前進/後退導航
- **後退**：返回到上一個保存的狀態
- **前進**：如果之前有後退操作，可以前進到較新的狀態
- 按鈕會根據可用性自動啟用/禁用

### 4. 歷史記錄面板
點擊歷史記錄按鈕（🕐）可以：
- 查看所有保存的編輯狀態
- 直接跳轉到任意歷史狀態
- 查看每個狀態的描述和時間
- 清除所有歷史記錄

## 狀態包含內容

每個保存的狀態包含：
- 工序表單數據
- 已保存的工序列表
- 當前選擇狀態
- 材料篩選設定
- 多選模式狀態
- 其他相關的編輯狀態

## 使用場景

### 1. 實驗性編輯
在進行大量修改前，手動保存當前狀態，如果結果不滿意可以快速回退。

### 2. 錯誤恢復
如果意外刪除或修改了重要數據，可以通過歷史記錄快速恢復。

### 3. 版本比較
通過歷史記錄面板可以查看不同編輯階段的狀態，便於比較和選擇。

### 4. 工作流程追蹤
自動保存的歷史記錄可以幫助追蹤整個編輯過程。

## 限制說明

- 最多保存10個歷史狀態
- 超過限制時會自動移除最舊的記錄
- 歷史記錄僅在當前瀏覽器會話中有效
- 刷新頁面後歷史記錄會重置

## 技術實現

### 核心組件
- `HistoryManager`：歷史記錄管理模組
- `NextStepControl.vue`：UI控制組件
- 整合在 `ProcessPreparationView.vue` 中

### 狀態管理
- 使用深拷貝避免引用問題
- 智慧的導航標誌防止循環觸發
- 完整的狀態序列化和恢復機制

## 故障排除

### 按鈕無法點擊
- 檢查是否有可用的歷史記錄
- 確認當前位置是否允許該操作

### 狀態恢復異常
- 檢查瀏覽器控制台是否有錯誤訊息
- 嘗試清除歷史記錄重新開始

### 性能問題
- 如果數據量很大，考慮清除部分歷史記錄
- 避免頻繁的手動保存操作

## 更新日誌

### v1.0.0 (2025-01-17)
- 初始版本發布
- 支援基本的前進/後退功能
- 實現歷史記錄面板
- 自動狀態保存機制
- 完整的UI集成
