<template>
  <div class="material-usage-records-overlay" v-if="visible">
    <div class="material-usage-records-container">
      <div class="material-usage-records-header">
        <h3>原物料歷史紀錄</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="material-usage-records-content">
        <div class="filter-section">
          <div class="form-row">
            <div class="form-group">
              <label>開始日期</label>
              <input type="date" v-model="filters.start_date">
            </div>
            <div class="form-group">
              <label>結束日期</label>
              <input type="date" v-model="filters.end_date">
            </div>
            <div class="form-group">
              <label>部門</label>
              <select v-model="filters.department">
                <option value="">全部部門</option>
                <option value="台北部">台北部</option>
                <option value="廠務部A">廠務部A</option>
                <option value="廠務部B">廠務部B</option>
                <option value="廠務部C">廠務部C</option>
                <option value="廠務部D">廠務部D</option>
                <option value="廠務部E">廠務部E</option>
              </select>
            </div>
            <div class="form-group">
              <label>原物料名稱</label>
              <input type="text" v-model="searchQuery" placeholder="搜尋材料編號或名稱">
            </div>
          </div>
          <div class="filter-actions">
            <button class="filter-btn" @click="fetchUsageRecords">
              <i class="fas fa-search"></i> 查詢
            </button>
            <button class="reset-btn" @click="resetFilters">
              <i class="fas fa-undo"></i> 重置
            </button>
          </div>
        </div>

        <div class="records-table-container">
          <table class="records-table">
            <thead>
              <tr>
                <th>倉庫</th>
                <th>材料編號</th>
                <th>材料名稱</th>
                <th>規格</th>
                <th>顏色</th>
                <th>交易類型</th>
                <th>數量/面積</th>
                <th>皮料詳情</th>
                <th>申請人</th>
                <th>部門</th>
                <th>用途</th>
                <th>變更時間</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading">
                <td colspan="12" class="loading-data">
                  <div class="loading-content">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>載入中...</span>
                  </div>
                </td>
              </tr>
              <tr v-else-if="filteredRecords.length === 0">
                <td colspan="12" class="no-data">
                  <div class="no-data-content">
                    <img src="@/assets/no-data.svg" alt="No data" class="no-data-icon">
                    <span>無交易記錄</span>
                  </div>
                </td>
              </tr>
              <tr v-for="record in filteredRecords" :key="record.id">
                <td>{{ record.storage_location }}</td>
                <td>{{ record.material_code }}</td>
                <td>{{ record.material_name }}</td>
                <td>{{ record.specification }}</td>
                <td>{{ record.color }}</td>
                <td>{{ record.transaction_type }}</td>
                <td>
                  <!-- 如果是皮料交易，顯示面積而不是數量 -->
                  <span v-if="record.leatherInfo">{{ formatQuantity(record.leatherInfo.area) }} m²</span>
                  <span v-else>{{ formatQuantity(record.quantity) }}</span>
                </td>
                <td>
                  <div v-if="record.leatherInfo" class="leather-info">
                    <div><strong>編碼:</strong> {{ record.leatherInfo.leather_code }}</div>
                    <div><strong>張數:</strong> {{ record.leatherInfo.quantity }}</div>
                  </div>
                  <span v-else>-</span>
                </td>
                <td>{{ record.requester }}</td>
                <td>{{ record.department }}</td>
                <td>{{ record.purpose || '-' }}</td>
                <td>{{ formatDate(record.createdAt) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { debounce } from 'lodash-es';
import api from '@/services/api';
import { useNotification } from '@/services/notificationService';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

// Emit events
const emit = defineEmits(['close']);

// 通知服務
const notification = useNotification();

// 數據和響應式變量
const usageRecords = ref([]);
const leatherMaterials = ref([]); // 存儲皮料特殊資料
const loading = ref(false);
const searchQuery = ref('');

// 篩選條件
const filters = ref({
  start_date: '',
  end_date: '',
  department: '',
  material_id: ''
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 格式化數量，顯示到小數點第二位
const formatQuantity = (value) => {
  const num = Number(value);
  if (isNaN(num)) {
    return value; // 如果不是有效數字，返回原值
  }
  // 使用 toFixed(2) 確保始終顯示兩位小數
  return num.toFixed(2);
};

// 獲取皮料特殊資料
const fetchLeatherMaterials = async () => {
  try {
    // 如果有篩選條件，則只獲取符合條件的皮料資料
    const params = {};
    if (filters.value.material_id) {
      params.material_id = filters.value.material_id;
    }

    // 如果有日期篩選，則不傳遞到皮料API，因為它不支援日期篩選
    // 我們會在後續的關聯邏輯中處理時間匹配

    console.log('獲取皮料資料參數:', params);
    const response = await api.materials.leatherMaterials.getAll(params);

    if (response && response.data) {
      console.log('獲取到皮料資料數量:', response.data.length);
      leatherMaterials.value = response.data;
    } else {
      console.log('無皮料資料返回');
      leatherMaterials.value = [];
    }
  } catch (error) {
    console.error('獲取皮料特殊資料失敗:', error);
    leatherMaterials.value = [];
  }
};

// 將皮料特殊資料與交易記錄關聯
const associateLeatherInfo = () => {
  // 先按照交易ID將皮料資料分組
  const leatherByTransactionId = {};
  leatherMaterials.value.forEach(leather => {
    if (leather.transaction_id) {
      leatherByTransactionId[leather.transaction_id] = leather;
    }
  });

  // 將皮料資料關聯到交易記錄
  usageRecords.value.forEach(record => {
    // 首先檢查是否有直接關聯的皮料資料
    if (leatherByTransactionId[record.id]) {
      record.leatherInfo = leatherByTransactionId[record.id];
      return;
    }

    // 如果沒有直接關聯，檢查是否為皮料相關交易
    if (record.notes && record.notes.includes('皮料入庫')) {
      // 尋找相同原物料ID、倉庫和時間接近的皮料資料
      const matchingLeathers = leatherMaterials.value.filter(item =>
        item.material_id === record.material_id &&
        item.warehouse === record.storage_location &&
        Math.abs(new Date(item.createdAt) - new Date(record.createdAt)) < 60000 // 時間差小於1分鐘
      );

      // 如果找到多個匹配項，選擇時間最接近的
      if (matchingLeathers.length > 0) {
        matchingLeathers.sort((a, b) =>
          Math.abs(new Date(a.createdAt) - new Date(record.createdAt)) -
          Math.abs(new Date(b.createdAt) - new Date(record.createdAt))
        );
        record.leatherInfo = matchingLeathers[0];
      }
    }
  });
};

// 獲取領用記錄
const fetchUsageRecords = async () => {
  try {
    loading.value = true;

    // 準備查詢參數
    const params = {};

    if (filters.value.start_date) {
      params.start_date = filters.value.start_date;
    }

    if (filters.value.end_date) {
      params.end_date = filters.value.end_date;
    }

    if (filters.value.department) {
      params.department = filters.value.department;
    }

    if (filters.value.material_id) {
      params.material_id = filters.value.material_id;
    }

    // 使用交易記錄 API
    const response = await api.materials.getTransactions(params);

    if (response && Array.isArray(response)) {
      // 按創建時間降序排序，最新的記錄在最上方
      usageRecords.value = response.sort((a, b) =>
        new Date(b.createdAt) - new Date(a.createdAt)
      );

      // 獲取皮料特殊資料並關聯
      console.log('交易記錄數量:', usageRecords.value.length);

      // 檢查是否有皮料相關交易
      const hasLeatherTransactions = usageRecords.value.some(record =>
        record.notes && record.notes.includes('皮料入庫')
      );

      if (hasLeatherTransactions) {
        console.log('發現皮料相關交易，獲取皮料資料');
        await fetchLeatherMaterials();
        associateLeatherInfo();
      } else {
        console.log('無皮料相關交易，跳過皮料資料獲取');
      }
    } else {
      usageRecords.value = [];
    }
  } catch (error) {
    console.error('獲取交易記錄失敗:', error);
    notification.error('獲取交易記錄失敗');

    // 如果新 API 失敗，嘗試使用舊 API 作為備用
    try {
      console.log('嘗試使用舊的 API 端點...');
      const oldParams = {};

      if (filters.value.start_date) {
        oldParams.start_date = filters.value.start_date;
      }

      if (filters.value.end_date) {
        oldParams.end_date = filters.value.end_date;
      }

      if (filters.value.department) {
        oldParams.department = filters.value.department;
      }

      if (filters.value.material_id) {
        oldParams.material_id = filters.value.material_id;
      }

      const backupResponse = await api.materials.getUsage(oldParams);

      if (backupResponse && Array.isArray(backupResponse)) {
        usageRecords.value = backupResponse.sort((a, b) =>
          new Date(b.createdAt) - new Date(a.createdAt)
        );
      }
    } catch (backupError) {
      console.error('備用 API 也失敗:', backupError);
    }
  } finally {
    loading.value = false;
  }
};

// 重置篩選條件
const resetFilters = () => {
  filters.value = {
    start_date: '',
    end_date: '',
    department: '',
    material_id: ''
  };
  searchQuery.value = '';
  fetchUsageRecords();
};

// 過濾後的記錄
const filteredRecords = computed(() => {
  const query = searchQuery.value.toLowerCase().trim();
  if (!query) return usageRecords.value;

  return usageRecords.value.filter(record =>
    (record.material_code && record.material_code.toLowerCase().includes(query)) ||
    (record.material_name && record.material_name.toLowerCase().includes(query))
  );
});

// 組件掛載時獲取數據
onMounted(() => {
  // 不在掛載時獲取數據，而是在彈窗出現時獲取
});

// 當彈窗可見時獲取數據
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    await fetchUsageRecords();
  }
});

// 關閉彈窗
const closeModal = () => {
  emit('close');
};
</script>

<style scoped>
.material-usage-records-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1010;
}

.material-usage-records-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.material-usage-records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.material-usage-records-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #333;
}

.material-usage-records-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eee;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  min-width: 200px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input[type="text"],
input[type="date"],
select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="date"]:focus,
select:focus {
  border-color: #2196F3;
  outline: none;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.filter-btn,
.reset-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filter-btn {
  background-color: #2196F3;
  color: white;
}

.filter-btn:hover {
  background-color: #1976D2;
}

.reset-btn {
  background-color: #f5f5f5;
  color: #333;
}

.reset-btn:hover {
  background-color: #e0e0e0;
}

.records-table-container {
  overflow-x: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.records-table {
  width: 100%;
  border-collapse: collapse;
}

.records-table th,
.records-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.records-table th {
  background-color: #f7f7f7;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

.records-table tr:hover {
  background-color: #f9f9f9;
}

.loading-data,
.no-data {
  padding: 40px 0;
  text-align: center;
}

.loading-content,
.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #999;
}

.loading-content i {
  font-size: 24px;
}

.no-data-icon {
  width: 60px;
  height: 60px;
  opacity: 0.5;
}

/* 皮料信息樣式 */
.leather-info {
  padding: 2px 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.3;
}

.leather-info div {
  margin: 2px 0;
}

.leather-info strong {
  color: #666;
  margin-right: 5px;
}
</style>