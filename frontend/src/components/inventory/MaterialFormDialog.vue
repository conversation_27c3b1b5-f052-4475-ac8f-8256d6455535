<template>
  <div class="material-form-overlay" v-if="visible">
    <div class="material-form-container">
      <div class="material-form-header">
        <h3>{{ isEdit ? '編輯原物料' : '原物料入庫' }}</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="material-form-content">
        <form @submit.prevent="handleSubmit">
          <!-- 已移除入庫倉庫欄位 -->

          <div class="form-row">
            <div class="form-group">
              <label data-hint="(必填)">材料編號</label>
              <div class="autocomplete">
                <input
                  type="text"
                  v-model="productCodeInput"
                  placeholder="請輸入材料編號"
                  @input="searchProducts"
                  @focus="showProductDropdown = true"
                  @blur="handleProductBlur"
                >
                <div class="dropdown" v-if="showProductDropdown && filteredProducts.length">
                  <div
                    v-for="product in filteredProducts"
                    :key="product.id"
                    class="dropdown-item"
                    @mousedown="selectProduct(product)"
                  >
                    <div class="product-code">{{ product.code }}</div>
                    <div class="product-name">{{ product.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label data-hint="(必填)">材料名稱</label>
              <div class="autocomplete">
                <input
                  type="text"
                  v-model="productNameInput"
                  placeholder="請輸入材料名稱"
                  @input="searchProducts"
                  @focus="showProductDropdown = true"
                  @blur="handleProductBlur"
                >
                <div class="dropdown" v-if="showProductDropdown && filteredProducts.length">
                  <div
                    v-for="product in filteredProducts"
                    :key="product.id"
                    class="dropdown-item"
                    @mousedown="selectProduct(product)"
                  >
                    <div class="product-code">{{ product.code }}</div>
                    <div class="product-name">{{ product.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>分類</label>
              <div class="select-with-setting">
                <div class="custom-select">
                  <div class="select-input-container">
                    <input
                      type="text"
                      v-model="formData.category"
                      @input="searchCategories"
                      @focus="searchCategories"
                      @blur="handleCategoryBlur"
                      placeholder="請選擇或輸入分類"
                      :disabled="!hasMaterialInfo"
                      :class="{ 'disabled-input': !hasMaterialInfo }"
                    >
                    <i class="fas fa-chevron-down select-arrow" @click="hasMaterialInfo ? toggleCategoryDropdown() : null" :class="{ 'disabled-arrow': !hasMaterialInfo }"></i>
                  </div>
                  <div class="select-items" v-if="showCategoryDropdown">
                    <div
                      v-for="(category, index) in filteredCategories"
                      :key="index"
                      class="select-item"
                      @mousedown="selectCategory(category)"
                    >
                      {{ category }}
                    </div>
                  </div>
                </div>
                <button
                  class="setting-btn"
                  type="button"
                  @click="hasMaterialInfo ? openSettingManager('materialCategory', '原物料分類管理') : null"
                  title="管理常用分類"
                  :disabled="!hasMaterialInfo"
                  :class="{ 'disabled-btn': !hasMaterialInfo }"
                >
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label>規格</label>
              <input type="text" v-model="formData.specification" placeholder="請輸入規格" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>單位</label>
              <div class="select-with-setting">
                <div class="custom-select">
                  <div class="select-input-container">
                    <input
                      type="text"
                      v-model="formData.unit"
                      @input="searchUnits"
                      @focus="searchUnits"
                      @blur="handleUnitBlur"
                      placeholder="請選擇或輸入單位"
                      :disabled="!hasMaterialInfo"
                      :class="{ 'disabled-input': !hasMaterialInfo }"
                    >
                    <i class="fas fa-chevron-down select-arrow" @click="hasMaterialInfo ? toggleUnitDropdown() : null" :class="{ 'disabled-arrow': !hasMaterialInfo }"></i>
                  </div>
                  <div class="select-items" v-if="showUnitDropdown">
                    <div
                      v-for="(unit, index) in filteredUnits"
                      :key="index"
                      class="select-item"
                      @mousedown="selectUnit(unit)"
                    >
                      {{ unit }}
                    </div>
                  </div>
                </div>
                <button
                  class="setting-btn"
                  type="button"
                  @click="hasMaterialInfo ? openSettingManager('materialUnit', '原物料單位管理') : null"
                  title="管理常用單位"
                  :disabled="!hasMaterialInfo"
                  :class="{ 'disabled-btn': !hasMaterialInfo }"
                >
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label>顏色</label>
              <div class="select-with-setting">
                <div class="custom-select">
                  <div class="select-input-container">
                    <input
                      type="text"
                      v-model="formData.color"
                      @input="searchColors"
                      @focus="searchColors"
                      @blur="handleColorBlur"
                      placeholder="請選擇或輸入顏色"
                      :disabled="!hasMaterialInfo"
                      :class="{ 'disabled-input': !hasMaterialInfo }"
                    >
                    <i class="fas fa-chevron-down select-arrow" @click="hasMaterialInfo ? toggleColorDropdown() : null" :class="{ 'disabled-arrow': !hasMaterialInfo }"></i>
                  </div>
                  <div class="select-items" v-if="showColorDropdown">
                    <div
                      v-for="(color, index) in filteredColors"
                      :key="index"
                      class="select-item"
                      @mousedown="selectColor(color)"
                    >
                      {{ color }}
                    </div>
                  </div>
                </div>
                <button
                  class="setting-btn"
                  type="button"
                  @click="hasMaterialInfo ? openSettingManager('materialColor', '原物料顏色管理') : null"
                  title="管理常用顏色"
                  :disabled="!hasMaterialInfo"
                  :class="{ 'disabled-btn': !hasMaterialInfo }"
                >
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 圖片上傳區域 -->
          <div class="form-group">
            <label>產品圖片</label>
            <div class="image-upload-container">
              <div v-if="imagePreview" class="image-preview">
                <img :src="getImageUrl(imagePreview)" alt="產品圖片預覽" @error="handleImageError" />
                <button type="button" class="remove-image-btn" @click="removeImage">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
              <div v-else class="upload-placeholder" @click="hasMaterialInfo ? triggerUpload() : null" :class="{ 'disabled-upload': !hasMaterialInfo }">
                <i class="fas fa-plus"></i>
                <span>上傳圖片</span>
              </div>
              <input
                type="file"
                ref="fileInput"
                @change="handleImageUpload"
                accept="image/*"
                style="display: none"
              />
            </div>
          </div>

          <!-- 入庫數量欄位，只在新增模式下顯示 -->
          <div class="form-group" v-if="!isEdit">
            <label>入庫數量</label>
            <div class="warehouse-quantity-container">
              <!-- 非皮料的台北部入庫 UI -->
              <div class="warehouse-quantity-row" v-if="!isLeatherCategory">
                <div class="warehouse-label">台北部入庫數量:</div>
                <input type="number" v-model.number="formData.taipei_quantity" min="0" step="1" placeholder="請輸入台北部入庫數量" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
              </div>

              <!-- 皮料的台北部入庫 UI -->
              <div class="warehouse-section" v-if="isLeatherCategory">
                <div class="warehouse-section-header">
                  <div class="warehouse-label">皮料入庫:</div>
                </div>

                <div class="warehouse-entry leather-entry taipei-leather-entry">
                  <div class="warehouse-quantity-row leather-row">
                    <div class="warehouse-label-fixed">台北部</div>
                    <div class="leather-inputs">
                      <div class="leather-input-group">
                        <label>面積(m²):</label>
                        <input type="number" v-model.number="formData.taipei_leather_area" min="0" step="1" placeholder="請輸入面積" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      </div>
                      <div class="leather-input-group">
                        <label>數量(張):</label>
                        <input type="number" v-model.number="formData.taipei_leather_quantity" min="0" step="1" placeholder="請輸入張數" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 非皮料的廠務部入庫UI -->
              <div class="warehouse-section" v-if="!isLeatherCategory">
                <div class="warehouse-section-header">
                  <div class="warehouse-label">廠務部入庫:</div>
                  <button type="button" class="add-warehouse-btn" @click="addWarehouseEntry" title="新增倉庫入庫" :disabled="!hasMaterialInfo" :class="{ 'disabled-btn': !hasMaterialInfo }">
                    <i class="fas fa-plus"></i>
                  </button>
                </div>

                <div v-for="(entry, index) in formData.factory_entries" :key="index" class="warehouse-entry">
                  <div class="warehouse-quantity-row">
                    <select v-model="entry.warehouse" class="warehouse-select" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      <option value="廠務部A">廠務部A</option>
                      <option value="廠務部B">廠務部B</option>
                      <option value="廠務部C">廠務部C</option>
                      <option value="廠務部D">廠務部D</option>
                      <option value="廠務部E">廠務部E</option>
                    </select>
                    <input type="number" v-model.number="entry.quantity" min="0" step="1" placeholder="請輸入入庫數量" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                    <button v-if="index > 0" type="button" class="remove-warehouse-btn" @click="removeWarehouseEntry(index)" title="移除此倉庫" :disabled="!hasMaterialInfo" :class="{ 'disabled-btn': !hasMaterialInfo }">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 皮料專用的廠務部入庫UI -->
              <div class="warehouse-section" v-if="isLeatherCategory">
                <div class="warehouse-section-header">
                  <div class="warehouse-label">皮料入庫:</div>
                  <button type="button" class="add-warehouse-btn" @click="addLeatherWarehouseEntry" title="新增皮料倉庫" :disabled="!hasMaterialInfo" :class="{ 'disabled-btn': !hasMaterialInfo }">
                    <i class="fas fa-plus"></i>
                  </button>
                </div>

                <div v-for="(entry, index) in formData.leather_entries" :key="index" class="warehouse-entry leather-entry">
                  <div class="warehouse-quantity-row leather-row">
                    <select v-model="entry.warehouse" class="warehouse-select" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      <option value="廠務部A">廠務部A</option>
                      <option value="廠務部B">廠務部B</option>
                      <option value="廠務部C">廠務部C</option>
                      <option value="廠務部D">廠務部D</option>
                      <option value="廠務部E">廠務部E</option>
                    </select>
                    <div class="leather-inputs">
                      <div class="leather-input-group">
                        <label>面積(m²):</label>
                        <input type="number" v-model.number="entry.area" min="0" step="1" placeholder="請輸入面積" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      </div>
                      <div class="leather-input-group">
                        <label>數量(張):</label>
                        <input type="number" v-model.number="entry.quantity" min="0" step="1" placeholder="請輸入張數" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
                      </div>
                    </div>
                    <button v-if="index > 0" type="button" class="remove-warehouse-btn" @click="removeLeatherWarehouseEntry(index)" title="移除此倉庫" :disabled="!hasMaterialInfo" :class="{ 'disabled-btn': !hasMaterialInfo }">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="existingTaipeiQuantity > 0 || existingFactoryQuantity > 0 || existingFactoryAQuantity > 0 || existingFactoryBQuantity > 0 || existingFactoryCQuantity > 0 || existingFactoryDQuantity > 0 || existingFactoryEQuantity > 0" class="quantity-info">
              <div v-if="existingTaipeiQuantity > 0">台北部目前庫存: {{ Number(existingTaipeiQuantity).toFixed(2) }} / 入庫後: {{ Number(existingTaipeiQuantity + (formData.taipei_quantity || 0)).toFixed(2) }}</div>

              <!-- 廠務部總庫存 -->
              <div v-if="existingFactoryQuantity > 0">廠務部總庫存: {{ Number(existingFactoryQuantity).toFixed(2) }} / 入庫後: {{ Number(existingFactoryQuantity + getTotalFactoryQuantity()).toFixed(2) }}</div>

              <!-- 廠務部各倉庫庫存 -->
              <div v-if="existingFactoryAQuantity > 0">廠務部A目前庫存: {{ Number(existingFactoryAQuantity).toFixed(2) }}</div>
              <div v-if="existingFactoryBQuantity > 0">廠務部B目前庫存: {{ Number(existingFactoryBQuantity).toFixed(2) }}</div>
              <div v-if="existingFactoryCQuantity > 0">廠務部C目前庫存: {{ Number(existingFactoryCQuantity).toFixed(2) }}</div>
              <div v-if="existingFactoryDQuantity > 0">廠務部D目前庫存: {{ Number(existingFactoryDQuantity).toFixed(2) }}</div>
              <div v-if="existingFactoryEQuantity > 0">廠務部E目前庫存: {{ Number(existingFactoryEQuantity).toFixed(2) }}</div>
            </div>
          </div>

          <!-- 編輯模式下顯示庫存信息 -->
          <div class="form-group" v-if="isEdit">
            <label>庫存信息</label>
            <div class="quantity-info">
              <!-- 台北部庫存 -->
              <div v-if="props.material && props.material.taipei_stock_quantity > 0">台北部庫存數量: {{ Number(props.material.taipei_stock_quantity).toFixed(2) }}</div>

              <!-- 廠務部總庫存 -->
              <div v-if="props.material && props.material.factory_stock_quantity > 0">廠務部總庫存數量: {{ Number(props.material.factory_stock_quantity).toFixed(2) }}</div>

              <!-- 廠務部各倉庫庫存 -->
              <div v-if="props.material && props.material.factory_a_stock_quantity > 0">廠務部A庫存數量: {{ Number(props.material.factory_a_stock_quantity).toFixed(2) }}</div>
              <div v-if="props.material && props.material.factory_b_stock_quantity > 0">廠務部B庫存數量: {{ Number(props.material.factory_b_stock_quantity).toFixed(2) }}</div>
              <div v-if="props.material && props.material.factory_c_stock_quantity > 0">廠務部C庫存數量: {{ Number(props.material.factory_c_stock_quantity).toFixed(2) }}</div>
              <div v-if="props.material && props.material.factory_d_stock_quantity > 0">廠務部D庫存數量: {{ Number(props.material.factory_d_stock_quantity).toFixed(2) }}</div>
              <div v-if="props.material && props.material.factory_e_stock_quantity > 0">廠務部E庫存數量: {{ Number(props.material.factory_e_stock_quantity).toFixed(2) }}</div>
            </div>
          </div>

          <div class="form-group">
            <label>備註</label>
            <textarea v-model="formData.description" placeholder="請輸入備註" rows="3" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }"></textarea>
          </div>

          <!-- 新增外框容器 -->
          <div class="requester-department-section">
            <div class="form-group">
              <label>申請人</label>
              <input type="text" v-model="formData.requester" placeholder="請輸入申請人" :disabled="!hasMaterialInfo" :class="{ 'disabled-input': !hasMaterialInfo }">
            </div>

            <div class="form-group">
              <label>部門</label>
              <div class="custom-select">
                <div class="select-selected" @click="hasMaterialInfo ? toggleDepartmentDropdown() : null" :class="{ 'disabled-select': !hasMaterialInfo }">
                  {{ formData.department || '請選擇部門' }}
                  <i class="fas fa-chevron-down select-arrow" :class="{ 'disabled-arrow': !hasMaterialInfo }"></i>
                </div>
                <div class="select-items" v-if="showDepartmentDropdown">
                  <div class="select-item" @mousedown="selectDepartment('')">請選擇部門</div>
                  <div class="select-item" @mousedown="selectDepartment('台北部')">台北部</div>
                  <div class="select-item" @mousedown="selectDepartment('廠務部A')">廠務部A</div>
                  <div class="select-item" @mousedown="selectDepartment('廠務部B')">廠務部B</div>
                  <div class="select-item" @mousedown="selectDepartment('廠務部C')">廠務部C</div>
                  <div class="select-item" @mousedown="selectDepartment('廠務部D')">廠務部D</div>
                  <div class="select-item" @mousedown="selectDepartment('廠務部E')">廠務部E</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 外框容器結束 -->

          <!-- 原來的部門 form-group 已移入上方容器，此處移除 -->
          <!-- <div class="form-group">
          </div> -->
          <div class="form-buttons">
            <button type="button" class="cancel-btn" @click="closeModal">取消</button>
            <button type="submit" class="submit-btn" :disabled="isSubmitting">{{ isSubmitting ? '提交中...' : '提交' }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 設定管理彈窗 -->
  <SettingManager
    :visible="settingManagerVisible"
    :settingType="currentSettingType"
    :title="currentSettingTitle"
    @close="settingManagerVisible = false"
    @updated="handleSettingUpdated"
  />
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue';
import api from '../../services/api';
import { useNotification } from '../../services/notificationService';
import SettingManager from '../SettingManager.vue'; // 恢復 SettingManager 的使用

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  material: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  userDepartment: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['close', 'submit-success']);
const notification = useNotification();

// 響應式變量
const isSubmitting = ref(false);
const managedColors = ref([]); // 從設定讀取的顏色
const managedUnits = ref([]); // 從設定讀取的單位
const managedCategories = ref([]); // 從設定讀取的分類
const allMaterials = ref([]);
const filteredProducts = ref([]);
const showProductDropdown = ref(false);
const productCodeInput = ref('');
const productNameInput = ref('');
const existingTaipeiQuantity = ref(0);
const existingFactoryQuantity = ref(0);
const existingFactoryAQuantity = ref(0);
const existingFactoryBQuantity = ref(0);
const existingFactoryCQuantity = ref(0);
const existingFactoryDQuantity = ref(0);
const existingFactoryEQuantity = ref(0);
const existingTaipeiMaterialId = ref(null);
const existingFactoryMaterialId = ref(null);
const showColorDropdown = ref(false); // 控制顏色下拉選單顯示
const showUnitDropdown = ref(false); // 控制單位下拉選單顯示
const showCategoryDropdown = ref(false); // 控制分類下拉選單顯示
const showDepartmentDropdown = ref(false); // 控制部門下拉選單顯示
const filteredColors = ref([]); // 過濾後的顏色選項
const filteredUnits = ref([]); // 過濾後的單位選項
const filteredCategories = ref([]); // 過濾後的分類選項
const imagePreview = ref(''); // 圖片預覽 URL
const fileInput = ref(null); // 文件輸入元素引用

// 設定管理相關
const settingManagerVisible = ref(false);
const currentSettingType = ref('');
const currentSettingTitle = ref('');

// 表單數據
const formData = reactive({
  code: '',
  name: '',
  specification: '',
  unit: '', // 單位欄位
  color: '',
  category: '',
  taipei_quantity: 0, // 台北部入庫數量（非皮料）
  factory_entries: [{ warehouse: '廠務部A', quantity: 0 }], // 廠務部入庫數據，包含倉庫和數量
  // 皮料相關欄位
  taipei_leather_area: 0, // 台北部皮料面積
  taipei_leather_quantity: 0, // 台北部皮料張數
  leather_entries: [{ warehouse: '廠務部A', area: 0, quantity: 0 }], // 皮料相關數據
  description: '',
  requester: '',
  department: '',
  notes: '',
  image: '', // 圖片數據（base64）
  image_url: '' // 圖片URL
});

// 判斷當前是否為皮料分類
const isLeatherCategory = computed(() => {
  return formData.category === '皮料';
});

// 判斷是否有材料編號和材料名稱
const hasMaterialInfo = computed(() => {
  return !!formData.code && !!formData.name;
});

// 監聽分類變化，當選擇皮料時初始化皮料相關數據
watch(() => formData.category, (newCategory) => {
  if (newCategory === '皮料') {
    // 如果分類變為皮料，初始化皮料入庫欄位
    if (formData.leather_entries.length === 0) {
      formData.leather_entries = [{ warehouse: '廠務部A', area: 0, quantity: 0 }];
    }
    // 初始化台北部皮料入庫欄位
    formData.taipei_leather_area = 0;
    formData.taipei_leather_quantity = 0;
    // 將單位設置為 m²
    formData.unit = 'm²';
  }
});

// 監聽皮料面積和數量變化，限制小數點位數
watch(() => formData.leather_entries, (entries) => {
  if (!Array.isArray(entries)) return;

  entries.forEach(entry => {
    if (typeof entry.area === 'number') {
      entry.area = Math.round(entry.area * 100) / 100;
    }
    if (typeof entry.quantity === 'number') {
      entry.quantity = Math.round(entry.quantity);
    }
  });
}, { deep: true });

// 監聽台北部皮料面積變化，限制小數點位數
watch(() => formData.taipei_leather_area, (newVal) => {
  if (typeof newVal === 'number') {
    formData.taipei_leather_area = Math.round(newVal * 100) / 100;
  }
});

// 監聽台北部皮料張數變化，確保為整數
watch(() => formData.taipei_leather_quantity, (newVal) => {
  if (typeof newVal === 'number') {
    formData.taipei_leather_quantity = Math.round(newVal);
  }
});

// 新增皮料倉庫入庫項
const addLeatherWarehouseEntry = () => {
  // 檢查是否有重複的倉庫
  const usedWarehouses = formData.leather_entries.map(entry => entry.warehouse);
  const availableWarehouses = ['A', 'B', 'C', 'D', 'E']
    .map(w => `廠務部${w}`)
    .filter(w => !usedWarehouses.includes(w));

  // 如果還有可用的倉庫，則添加一個新的入庫項
  if (availableWarehouses.length > 0) {
    formData.leather_entries.push({
      warehouse: availableWarehouses[0],
      area: 0,
      quantity: 0
    });
  } else {
    notification.warning('已經添加了所有可用的倉庫');
  }
};

// 移除皮料倉庫入庫項
const removeLeatherWarehouseEntry = (index) => {
  if (index > 0) { // 確保至少保留一個倉庫項
    formData.leather_entries.splice(index, 1);
  }
};

// 監聽台北部數量變化，限制小數點位數
watch(() => formData.taipei_quantity, (newVal) => {
  if (typeof newVal === 'number') {
    formData.taipei_quantity = Math.round(newVal * 100) / 100;
  }
});

// 監聽廠務部各倉庫數量變化，限制小數點位數
watch(() => formData.factory_entries, (entries) => {
  entries.forEach(entry => {
    if (typeof entry.quantity === 'number') {
      entry.quantity = Math.round(entry.quantity * 100) / 100;
    }
  });
}, { deep: true });

// 新增倉庫入庫項
const addWarehouseEntry = () => {
  // 檢查是否有重複的倉庫
  const usedWarehouses = formData.factory_entries.map(entry => entry.warehouse);
  const availableWarehouses = ['A', 'B', 'C', 'D', 'E']
    .map(w => `廠務部${w}`)
    .filter(w => !usedWarehouses.includes(w));

  // 如果還有可用的倉庫，則添加一個新的入庫項
  if (availableWarehouses.length > 0) {
    formData.factory_entries.push({
      warehouse: availableWarehouses[0],
      quantity: 0
    });
  } else {
    notification.warning('已經添加了所有可用的倉庫');
  }
};

// 移除倉庫入庫項
const removeWarehouseEntry = (index) => {
  if (index > 0) { // 確保至少保留一個倉庫項
    formData.factory_entries.splice(index, 1);
  }
};

// 計算廠務部入庫總數量
const getTotalFactoryQuantity = () => {
  return formData.factory_entries.reduce((sum, entry) => sum + (entry.quantity || 0), 0);
};

// 監聽材料編號和名稱輸入框的變化，同步到表單數據
watch(productCodeInput, (newVal) => {
  formData.code = newVal;
  // 當材料編號變化時，檢查是否已存在相同材料編號的原物料
  if (newVal) {
    checkExistingProductCode(newVal);
  }
});

watch(productNameInput, (newVal) => {
  formData.name = newVal;
});

// 監聽彈窗顯示狀態和編輯數據
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 如果是編輯模式，填充表單數據
    if (props.isEdit && props.material) {
      Object.keys(formData).forEach(key => {
        if (props.material[key] !== undefined) {
          formData[key] = props.material[key];
        }
      });
      productCodeInput.value = formData.code;
      productNameInput.value = formData.name;

      // 如果有圖片路徑，設置圖片預覽
      if (props.material.image_url) {
        imagePreview.value = props.material.image_url;
        console.log('設置編輯模式圖片預覽:', props.material.image_url);
      }
    } else {
      // 重置表單
      resetForm();

      // 根據用戶部門設置默認值
      if (props.userDepartment !== null) {
        // 根據用戶部門設置默認入庫數量
        if (props.userDepartment === 0) {
          formData.taipei_quantity = 0;
          formData.factory_entries = [{ warehouse: '廠務部A', quantity: 0 }];
        } else {
          formData.taipei_quantity = 0;
          formData.factory_entries = [{ warehouse: '廠務部A', quantity: 0 }];
        }
      }
    }

    // 獲取所有設定
    await fetchColorSettings();
    await fetchUnitSettings();
    await fetchCategorySettings();
    await fetchAllMaterials();

    // 初始化下拉選單
    updateFilteredColors();
    updateFilteredUnits();
    updateFilteredCategories();

    // 關閉所有下拉選單
    showColorDropdown.value = false;
    showUnitDropdown.value = false;
    showCategoryDropdown.value = false;
    showDepartmentDropdown.value = false;
  }
});

// 監聽倉庫變化
watch(() => formData.storage_location, async () => {
  if (formData.code && formData.name) {
    await checkExistingMaterial();
  }
});

// 獲取顏色設定
const fetchColorSettings = async () => {
  try {
    const response = await api.settingRecord.getAll({
      type: 'materialColor'
    });

    if (response && response.data) {
      // 根據 sort_order 排序
      managedColors.value = response.data
        .sort((a, b) => a.sort_order - b.sort_order)
        .map(item => item.name); // 只取名稱
    } else {
      managedColors.value = [];
    }
    // 更新顏色列表
    updateFilteredColors();
  } catch (error) {
    console.error('獲取顏色設定失敗:', error);
    notification.error('獲取顏色設定失敗');
    managedColors.value = []; // 出錯時清空
  }
};

// 獲取單位設定
const fetchUnitSettings = async () => {
  try {
    const response = await api.settingRecord.getAll({
      type: 'materialUnit'
    });

    if (response && response.data) {
      // 根據 sort_order 排序
      managedUnits.value = response.data
        .sort((a, b) => a.sort_order - b.sort_order)
        .map(item => item.name); // 只取名稱
    } else {
      managedUnits.value = [];
    }
    // 更新單位列表
    updateFilteredUnits();
  } catch (error) {
    console.error('獲取單位設定失敗:', error);
    notification.error('獲取單位設定失敗');
    managedUnits.value = []; // 出錯時清空
  }
};

// 獲取分類設定
const fetchCategorySettings = async () => {
  try {
    const response = await api.settingRecord.getAll({
      type: 'materialCategory'
    });

    if (response && response.data) {
      // 根據 sort_order 排序
      managedCategories.value = response.data
        .sort((a, b) => a.sort_order - b.sort_order)
        .map(item => item.name); // 只取名稱
    } else {
      managedCategories.value = [];
    }
    // 更新分類列表
    updateFilteredCategories();
  } catch (error) {
    console.error('獲取分類設定失敗:', error);
    notification.error('獲取分類設定失敗');
    managedCategories.value = []; // 出錯時清空
  }
};

// 獲取所有原物料
const fetchAllMaterials = async () => {
  try {
    const response = await api.materials.getAll();
    if (response && Array.isArray(response)) {
      // 按英文字母由小到大排序
      allMaterials.value = response.sort((a, b) => {
        if (a.code < b.code) return -1;
        if (a.code > b.code) return 1;
        return 0;
      });
    }
    // 更新所有下拉選單選項
    updateFilteredColors();
    updateFilteredUnits();
    updateFilteredCategories();
  } catch (error) {
    console.error('獲取原物料列表失敗:', error);
    notification.error('獲取原物料列表失敗');
  }
};

// 從所有物料中提取不重複的顏色 (排除已在 managedColors 中的)
const uniqueMaterialColors = computed(() => {
  const colorsSet = new Set();
  const managedSet = new Set(managedColors.value); // 用於快速查找
  allMaterials.value.forEach(material => {
    if (material.color && !managedSet.has(material.color)) { // 只加入不在管理列表中的顏色
      colorsSet.add(material.color);
    }
  });
  return Array.from(colorsSet).sort(); // 將額外的顏色排序
});



// 從所有物料中提取不重複的單位 (排除已在 managedUnits 中的)
const uniqueMaterialUnits = computed(() => {
  const unitsSet = new Set();
  const managedSet = new Set(managedUnits.value); // 用於快速查找
  allMaterials.value.forEach(material => {
    if (material.unit && !managedSet.has(material.unit)) { // 只加入不在管理列表中的單位
      unitsSet.add(material.unit);
    }
  });
  return Array.from(unitsSet).sort(); // 將單位排序
});

// 從所有物料中提取不重複的分類 (排除已在 managedCategories 中的)
const uniqueMaterialCategories = computed(() => {
  const categoriesSet = new Set();
  const managedSet = new Set(managedCategories.value); // 用於快速查找
  allMaterials.value.forEach(material => {
    if (material.category && !managedSet.has(material.category)) { // 只加入不在管理列表中的分類
      categoriesSet.add(material.category);
    }
  });
  return Array.from(categoriesSet).sort(); // 將分類排序
});

// 合併管理的顏色和物料中獨特的顏色，用於下拉選單顯示
const displayColors = computed(() => {
  // 先顯示管理的顏色 (已排序)，再顯示物料中其餘的顏色 (已排序)
  return [...managedColors.value, ...uniqueMaterialColors.value];
});

// 合併管理的單位和物料中獨特的單位，用於下拉選單顯示
const displayUnits = computed(() => {
  // 先顯示管理的單位 (已排序), 再顯示物料中其餘的單位 (已排序)
  return [...managedUnits.value, ...uniqueMaterialUnits.value];
});

// 合併管理的分類和物料中獨特的分類，用於下拉選單顯示
const displayCategories = computed(() => {
  // 先顯示管理的分類 (已排序), 再顯示物料中其餘的分類 (已排序)
  return [...managedCategories.value, ...uniqueMaterialCategories.value];
});

// 更新過濾後的顏色列表 (用於初始化和重置)
const updateFilteredColors = () => {
  filteredColors.value = displayColors.value;
};

// 更新過濾後的單位列表 (用於初始化和重置)
const updateFilteredUnits = () => {
  filteredUnits.value = displayUnits.value;
};

// 更新過濾後的分類列表 (用於初始化和重置)
const updateFilteredCategories = () => {
  filteredCategories.value = displayCategories.value;
};

// 切換顏色下拉選單顯示狀態
const toggleColorDropdown = () => {
  showColorDropdown.value = !showColorDropdown.value;
  // 如果顯示下拉選單，更新顏色列表並關閉其他下拉選單
  if (showColorDropdown.value) {
    updateFilteredColors();
    showUnitDropdown.value = false;
    showCategoryDropdown.value = false;
    showDepartmentDropdown.value = false;
  }
};

// 搜索顏色 (現在搜索合併後的列表)
const searchColors = () => {
  const inputColor = formData.color ? formData.color.toLowerCase() : '';
  if (!inputColor) {
    filteredColors.value = displayColors.value; // 輸入為空時顯示所有合併顏色
  } else {
    filteredColors.value = displayColors.value.filter(color =>
      color.toLowerCase().includes(inputColor)
    );
  }
  showColorDropdown.value = true;
};

// 搜索單位
const searchUnits = () => {
  const inputUnit = formData.unit ? formData.unit.toLowerCase() : '';
  if (!inputUnit) {
    filteredUnits.value = displayUnits.value; // 輸入為空時顯示所有合併單位
  } else {
    filteredUnits.value = displayUnits.value.filter(unit =>
      unit.toLowerCase().includes(inputUnit)
    );
  }
  showUnitDropdown.value = true;
};

// 搜索分類
const searchCategories = () => {
  const inputCategory = formData.category ? formData.category.toLowerCase() : '';
  if (!inputCategory) {
    filteredCategories.value = displayCategories.value; // 輸入為空時顯示所有合併分類
  } else {
    filteredCategories.value = displayCategories.value.filter(category =>
      category.toLowerCase().includes(inputCategory)
    );
  }
  showCategoryDropdown.value = true;
};

// 選擇顏色
const selectColor = (color) => {
  formData.color = color;
  showColorDropdown.value = false;
};

// 處理顏色輸入框失焦
const handleColorBlur = () => {
  // 延遲隱藏下拉選單，確保可以點擊選項
  setTimeout(() => {
    showColorDropdown.value = false;
  }, 200);
};

// 處理單位輸入框失焦
const handleUnitBlur = () => {
  // 延遲隱藏下拉選單，確保可以點擊選項
  setTimeout(() => {
    showUnitDropdown.value = false;
  }, 200);
};

// 處理分類輸入框失焦
const handleCategoryBlur = () => {
  // 延遲隱藏下拉選單，確保可以點擊選項
  setTimeout(() => {
    showCategoryDropdown.value = false;
  }, 200);
};

// 切換部門下拉選單顯示狀態
const toggleDepartmentDropdown = () => {
  showDepartmentDropdown.value = !showDepartmentDropdown.value;
  // 關閉其他下拉選單
  if (showDepartmentDropdown.value) {
    showColorDropdown.value = false;
    showUnitDropdown.value = false;
    showCategoryDropdown.value = false;
  }
};

// 選擇部門
const selectDepartment = (department) => {
  formData.department = department;
  showDepartmentDropdown.value = false;
};

// 切換單位下拉選單顯示狀態
const toggleUnitDropdown = () => {
  showUnitDropdown.value = !showUnitDropdown.value;
  // 關閉其他下拉選單
  if (showUnitDropdown.value) {
    showColorDropdown.value = false;
    showCategoryDropdown.value = false;
    showDepartmentDropdown.value = false;
    updateFilteredUnits();
  }
};

// 選擇單位
const selectUnit = (unit) => {
  formData.unit = unit;
  showUnitDropdown.value = false;
};

// 切換分類下拉選單顯示狀態
const toggleCategoryDropdown = () => {
  showCategoryDropdown.value = !showCategoryDropdown.value;
  // 關閉其他下拉選單
  if (showCategoryDropdown.value) {
    showColorDropdown.value = false;
    showUnitDropdown.value = false;
    showDepartmentDropdown.value = false;
    updateFilteredCategories();
  }
};

// 選擇分類
const selectCategory = (category) => {
  formData.category = category;
  showCategoryDropdown.value = false;

  // 如果選擇的是皮料分類，自動設置單位為 m²
  if (category === '皮料') {
    formData.unit = 'm²';
  }
};

// 點擊其他地方關閉下拉選單
document.addEventListener('click', (event) => {
  const target = event.target;
  if (!target.closest('.custom-select') && !target.closest('.setting-btn')) {
    showColorDropdown.value = false;
    showUnitDropdown.value = false;
    showCategoryDropdown.value = false;
    showDepartmentDropdown.value = false;
  }
});

// 搜索產品
const searchProducts = () => {
  if (!productCodeInput.value && !productNameInput.value) {
    filteredProducts.value = [];
    return;
  }

  const codeSearch = productCodeInput.value.toLowerCase();
  const nameSearch = productNameInput.value.toLowerCase();

  filteredProducts.value = allMaterials.value.filter(product => {
    const matchCode = product.code && product.code.toLowerCase().includes(codeSearch);
    const matchName = product.name && product.name.toLowerCase().includes(nameSearch);
    return (codeSearch && matchCode) || (nameSearch && matchName);
  });

  showProductDropdown.value = true;
};

// 選擇產品
const selectProduct = (product) => {
  formData.code = product.code || '';
  formData.name = product.name || '';
  // 自動填充分類
  formData.category = product.category || '';
  formData.specification = product.specification || '';
  // 自動填充單位
  formData.unit = product.unit || '';
  // 當選擇產品時，也自動填入顏色
  formData.color = product.color || '';

  productCodeInput.value = product.code || '';
  productNameInput.value = product.name || '';

  // 設置圖片預覽
  if (product.image) {
    imagePreview.value = product.image;
    formData.image = product.image;
  } else if (product.image_url) {
    imagePreview.value = product.image_url;
    formData.image_url = product.image_url; // 保存圖片URL而不是將URL存入image欄位
  }

  showProductDropdown.value = false;

  // 檢查該產品在各倉庫中是否已存在
  checkExistingMaterial();
};

// 檢查材料編號是否已存在
const checkExistingProductCode = async (code) => {
  try {
    // 尋找相同材料編號的原物料
    const matchingProducts = allMaterials.value.filter(material =>
      material.code && material.code === code
    );

    if (matchingProducts.length > 0) {
      // 如果找到相同材料編號的原物料，自動填充產品信息
      const existingProduct = matchingProducts[0];

      // 只填充產品信息，不填充倉庫和數量信息
      formData.name = existingProduct.name || '';
      // 自動填充分類
      formData.category = existingProduct.category || '';
      formData.specification = existingProduct.specification || '';
      // 自動填充單位
      formData.unit = existingProduct.unit || '';
      formData.color = existingProduct.color || '';

      // 同步到輸入框
      productNameInput.value = existingProduct.name || '';

      // 設置圖片預覽
      if (existingProduct.image) {
        imagePreview.value = existingProduct.image;
        formData.image = existingProduct.image;
      } else if (existingProduct.image_url) {
        imagePreview.value = existingProduct.image_url;
        formData.image_url = existingProduct.image_url; // 保存圖片URL而不是將URL存入image欄位
      }

      // 材料編號已存在，自動填充產品信息，不顯示通知

      // 檢查該產品在各倉庫中是否已存在
      checkExistingMaterial();
    }
  } catch (error) {
    console.error('檢查材料編號失敗:', error);
  }
};

// 檢查現有原物料
const checkExistingMaterial = async () => {
  try {
    // 檢查是否存在相同材料編號的原物料
    const matchingMaterials = allMaterials.value.filter(material =>
      material.code && formData.code && material.code === formData.code
    );

    if (matchingMaterials.length > 0) {
      const existingMaterial = matchingMaterials[0];

      // 設置台北部和廠務部的數量
      existingTaipeiQuantity.value = existingMaterial.taipei_stock_quantity || 0;
      existingFactoryQuantity.value = existingMaterial.factory_stock_quantity || 0;

      // 設置廠務部A-E的數量
      existingFactoryAQuantity.value = existingMaterial.factory_a_stock_quantity || 0;
      existingFactoryBQuantity.value = existingMaterial.factory_b_stock_quantity || 0;
      existingFactoryCQuantity.value = existingMaterial.factory_c_stock_quantity || 0;
      existingFactoryDQuantity.value = existingMaterial.factory_d_stock_quantity || 0;
      existingFactoryEQuantity.value = existingMaterial.factory_e_stock_quantity || 0;

      // 設置原物料 ID
      existingTaipeiMaterialId.value = existingMaterial.id;
      existingFactoryMaterialId.value = existingMaterial.id;
    } else {
      // 沒有找到現有原物料
      existingTaipeiQuantity.value = 0;
      existingFactoryQuantity.value = 0;
      existingTaipeiMaterialId.value = null;
      existingFactoryMaterialId.value = null;
    }
  } catch (error) {
    console.error('檢查現有原物料失敗:', error);
  }
};

// 處理產品輸入框失焦
const handleProductBlur = () => {
  // 延遲隱藏下拉選單，確保可以點擊選項
  setTimeout(() => {
    showProductDropdown.value = false;
  }, 200);
};

// 重置表單
const resetForm = () => {
  // 重置表單數據到初始狀態
  Object.keys(formData).forEach(key => {
    if (key === 'stock_quantity' || key === 'available_quantity') {
      formData[key] = "";
    } else if (key === 'taipei_quantity' || key === 'taipei_leather_area' || key === 'taipei_leather_quantity') {
      formData[key] = 0; // 數量相關欄位設置為 0
    } else {
      formData[key] = '';
    }
  });

  // 如果有用戶部門資訊，設置默認值
  if (props.userDepartment !== null) {
    formData.department = props.userDepartment === 0 ? '台北部' : '廠務部A';
    formData.taipei_quantity = 0;
    formData.taipei_leather_area = 0;
    formData.taipei_leather_quantity = 1;
    formData.factory_entries = [{ warehouse: '廠務部A', quantity: 0 }]; // 重置廠務部入庫數據
    formData.leather_entries = [{ warehouse: '廠務部A', area: 0, quantity: 1 }]; // 重置皮料入庫數據
  } else {
    formData.department = '倉管部門';
    formData.taipei_quantity = 0;
    formData.taipei_leather_area = 0;
    formData.taipei_leather_quantity = 1;
    formData.factory_entries = [{ warehouse: '廠務部A', quantity: 0 }]; // 重置廠務部入庫數據
    formData.leather_entries = [{ warehouse: '廠務部A', area: 0, quantity: 1 }]; // 重置皮料入庫數據
  }

  // 重置各種狀態
  productCodeInput.value = '';
  productNameInput.value = '';
  filteredProducts.value = [];
  showProductDropdown.value = false;
  existingTaipeiQuantity.value = 0;
  existingFactoryQuantity.value = 0;
  existingFactoryAQuantity.value = 0;
  existingFactoryBQuantity.value = 0;
  existingFactoryCQuantity.value = 0;
  existingFactoryDQuantity.value = 0;
  existingFactoryEQuantity.value = 0;
  existingTaipeiMaterialId.value = null;
  existingFactoryMaterialId.value = null;
  // 重置下拉選單相關狀態
  showColorDropdown.value = false;
  showUnitDropdown.value = false;
  showCategoryDropdown.value = false;
  showDepartmentDropdown.value = false;

  // 更新下拉選單列表
  updateFilteredColors();
  updateFilteredUnits();
  updateFilteredCategories();

  // 重置圖片相關狀態
  imagePreview.value = '';
  formData.image = '';
  formData.image_url = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 提交表單
const handleSubmit = async () => {
  try {
    isSubmitting.value = true;

    // 確保輸入框的值同步到表單數據
    formData.code = productCodeInput.value;
    formData.name = productNameInput.value;

    // 驗證材料編號是否存在
    if (!formData.code) {
      notification.error('材料編號為必填欄位');
      isSubmitting.value = false;
      return;
    }

    // 如果是編輯模式且有圖片數據，才需要驗證圖片格式
    if (props.isEdit && formData.image && formData.image.startsWith('data:image') && formData.image.length < 100) {
      notification.error('圖片格式無效，請重新上傳');
      isSubmitting.value = false;
      return;
    }

    // 移除強制輸入數量的限制

    // 如果是新增模式，先檢查材料編號是否已存在
    if (!props.isEdit) {
      // 尋找相同材料編號的原物料
      const matchingProducts = allMaterials.value.filter(material =>
        material.code && material.code === formData.code
      );

      // 如果找到相同材料編號的原物料，自動填充產品信息
      if (matchingProducts.length > 0) {
        const existingProduct = matchingProducts[0];

        // 只填充產品信息，不填充倉庫和數量信息
        formData.name = existingProduct.name || '';
        // 自動填充分類
        formData.category = existingProduct.category || '';
        formData.specification = existingProduct.specification || '';
        // 自動填充單位
        formData.unit = existingProduct.unit || '';
        formData.color = existingProduct.color || '';

        // 同步到輸入框
        productNameInput.value = existingProduct.name || '';

        // 設置圖片預覽
        if (existingProduct.image) {
          imagePreview.value = existingProduct.image;
          formData.image = existingProduct.image;
        } else if (existingProduct.image_url) {
          imagePreview.value = existingProduct.image_url;
          formData.image = existingProduct.image_url;
        }

        // 檢查該產品在各倉庫中是否已存在
        checkExistingMaterial();
      }
    }

    let response;

    // 處理台北部倉庫的原物料
    // 允許數量為 0，只新增品項不入庫
    if (formData.taipei_quantity >= 0) {
      // 創建或更新原物料
      if (!props.isEdit) {
        // 創建新的原物料
        const materialData = {
          code: formData.code,
          name: formData.name,
          specification: formData.specification,
          unit: formData.unit,
          color: formData.color,
          category: formData.category,
          description: formData.description,
          requester: formData.requester,
          department: formData.department,
          notes: formData.notes,
          image: formData.image, // 添加圖片數據
          image_url: formData.image_url || imagePreview.value, // 保存圖片URL
          taipei_quantity: formData.taipei_quantity,
          factory_quantity: isLeatherCategory.value ? 0 : getTotalFactoryQuantity(),
          factory_entries: isLeatherCategory.value ? [] : formData.factory_entries // 根據分類決定是否處理一般廠務部入庫
        };

        // 創建原物料
        const materialResponse = await api.materials.create(materialData);

        if (materialResponse) {
          // 創建交易記錄，正確記錄數量
          // 處理台北部入庫
          if (isLeatherCategory.value) {
            // 皮料入庫邏輯 - 台北部
            if (formData.taipei_leather_area > 0) {
              // 處理每一張皮料，確保每張都有獨立的編碼
              // 使用 for 循環並等待每個操作完成
              for (let i = 0; i < formData.taipei_leather_quantity; i++) {
                try {
                  // 創建皮料入庫交易記錄
                  const transactionResponse = await api.materials.createTransaction({
                    material_id: materialResponse.id,
                    quantity: 0, // 數量設置為0，實際數量記錄在皮料特殊資料中
                    transaction_type: '入庫',
                    storage_location: '台北部', // 台北部倉庫
                    requester: formData.requester || '系統管理員',
                    department: formData.department || '倉管部門',
                    notes: formData.notes || `台北部皮料入庫 - 面積:${formData.taipei_leather_area}m² 第${i+1}張`,
                    is_initial_transaction: true // 標記為初始交易，不更新庫存
                  });

                  // 創建皮料特殊資料，每張皮料獨立記錄
                  if (transactionResponse) {
                    // 等待創建皮料特殊資料完成
                    await api.materials.leatherMaterials.create({
                      material_id: materialResponse.id,
                      warehouse: '台北部',
                      area: formData.taipei_leather_area,
                      quantity: 1, // 每次記錄1張
                      transaction_id: transactionResponse.id,
                      notes: formData.notes || `台北部皮料入庫 - 面積:${formData.taipei_leather_area}m² 第${i+1}張`
                    });
                  }
                } catch (error) {
                  console.error(`創建第 ${i+1} 張台北部皮料失敗:`, error);
                  throw error; // 重新拋出錯誤，以便上層捕捉
                }
              }
            }
          } else {
            // 非皮料的台北部入庫邏輯
            if (formData.taipei_quantity > 0) {
              await api.materials.createTransaction({
                material_id: materialResponse.id,
                quantity: Number(formData.taipei_quantity),
                transaction_type: '入庫',
                storage_location: '台北部',
                requester: formData.requester || '系統管理員',
                department: formData.department || '倉管部門',
                notes: formData.notes || '台北部原物料入庫',
                is_initial_transaction: true // 標記為初始交易，不更新庫存
              });
            }
          }

          // 處理不同分類的入庫邏輯
          if (isLeatherCategory.value) {
            // 處理皮料入庫邏輯
            for (const entry of formData.leather_entries) {
              if (entry.area > 0) {
                // 處理每一張皮料，確保每張都有獨立的編碼
                // 使用 for 循環並等待每個操作完成
                for (let i = 0; i < entry.quantity; i++) {
                  try {
                    // 創建皮料入庫交易記錄
                    const transactionResponse = await api.materials.createTransaction({
                      material_id: materialResponse.id,
                      quantity: 0, // 數量設置為0，實際數量記錄在皮料特殊資料中
                      transaction_type: '入庫',
                      storage_location: entry.warehouse, // 使用選擇的廠務部倉別
                      requester: formData.requester || '系統管理員',
                      department: formData.department || '倉管部門',
                      notes: formData.notes || `${entry.warehouse}皮料入庫 - 面積:${entry.area}m² 第${i+1}張`,
                      is_initial_transaction: true // 標記為初始交易，不更新庫存
                    });

                    // 創建皮料特殊資料，每張皮料獨立記錄
                    if (transactionResponse) {
                      // 等待創建皮料特殊資料完成
                      await api.materials.leatherMaterials.create({
                        material_id: materialResponse.id,
                        warehouse: entry.warehouse,
                        area: entry.area,
                        quantity: 1, // 每次記錄1張
                        transaction_id: transactionResponse.id,
                        notes: formData.notes || `${entry.warehouse}皮料入庫 - 面積:${entry.area}m² 第${i+1}張`
                      });
                    }
                  } catch (error) {
                    console.error(`創建第 ${i+1} 張皮料失敗:`, error);
                    throw error; // 重新拋出錯誤，以便上層捕捉
                  }
                }
              }
            }
          } else {
            // 原本的一般原物料入庫邏輯
            for (const entry of formData.factory_entries) {
              if (entry.quantity > 0) {
                await api.materials.createTransaction({
                  material_id: materialResponse.id,
                  quantity: Number(entry.quantity),
                  transaction_type: '入庫',
                  storage_location: entry.warehouse, // 使用選擇的廠務部倉別
                  requester: formData.requester || '系統管理員',
                  department: formData.department || '倉管部門',
                  notes: formData.notes || `${entry.warehouse}原物料入庫`,
                  is_initial_transaction: true // 標記為初始交易，不更新庫存
                });
              }
            }
          }

          notification.success('原物料入庫成功');
        }
      } else {
        // 編輯模式，更新現有原物料
        const existingMaterial = props.material;

        // 如果有數量更新，則創建交易記錄
        const taipeiQuantity = formData.taipei_quantity !== undefined && formData.taipei_quantity > 0 ? Number(formData.taipei_quantity) : 0;

        if (taipeiQuantity > 0) {
          // 台北部入庫
          await api.materials.createTransaction({
            material_id: existingMaterial.id,
            quantity: taipeiQuantity,
            transaction_type: '入庫',
            storage_location: '台北部',
            requester: formData.requester || '系統管理員',
            department: formData.department || '倉管部門',
            notes: formData.notes || '台北部原物料入庫'
          });
        }

        // 處理不同分類的入庫邏輯
        if (isLeatherCategory.value) {
          // 處理皮料入庫邏輯
          for (const entry of formData.leather_entries) {
            if (entry.area > 0) {
              // 處理每一張皮料，確保每張都有獨立的編碼
              // 使用 for 循環並等待每個操作完成
              for (let i = 0; i < entry.quantity; i++) {
                try {
                  // 創建皮料入庫交易記錄
                  const transactionResponse = await api.materials.createTransaction({
                    material_id: existingMaterial.id,
                    quantity: 0, // 數量設置為0，實際數量記錄在皮料特殊資料中
                    transaction_type: '入庫',
                    storage_location: entry.warehouse, // 使用選擇的廠務部倉別
                    requester: formData.requester || '系統管理員',
                    department: formData.department || '倉管部門',
                    notes: formData.notes || `${entry.warehouse}皮料入庫 - 面積:${entry.area}m² 第${i+1}張`
                  });

                  // 創建皮料特殊資料，每張皮料獨立記錄
                  if (transactionResponse) {
                    // 等待創建皮料特殊資料完成
                    await api.materials.leatherMaterials.create({
                      material_id: existingMaterial.id,
                      warehouse: entry.warehouse,
                      area: entry.area,
                      quantity: 1, // 每次記錄1張
                      transaction_id: transactionResponse.id,
                      notes: formData.notes || `${entry.warehouse}皮料入庫 - 面積:${entry.area}m² 第${i+1}張`
                    });
                  }
                } catch (error) {
                  console.error(`創建第 ${i+1} 張皮料失敗:`, error);
                  throw error; // 重新拋出錯誤，以便上層捕捉
                }
              }
            }
          }
        } else {
          // 原本的一般原物料入庫邏輯
          for (const entry of formData.factory_entries) {
            if (entry.quantity > 0) {
              // 廠務部入庫
              await api.materials.createTransaction({
                material_id: existingMaterial.id,
                quantity: Number(entry.quantity),
                transaction_type: '入庫',
                storage_location: entry.warehouse, // 使用選擇的廠務部倉別
                requester: formData.requester || '系統管理員',
                department: formData.department || '倉管部門',
                notes: formData.notes || `${entry.warehouse}原物料入庫`
              });
            }
          }
        }
      }
    }

    // 編輯模式的處理
    if (props.isEdit) {
      // 編輯原物料
      const editedMaterial = props.material;
      const updatedData = {
        code: formData.code,
        name: formData.name,
        specification: formData.specification,
        unit: formData.unit,
        color: formData.color,
        category: formData.category,
        description: formData.description,
        image: formData.image, // 添加圖片數據
        image_url: formData.image_url || imagePreview.value // 保存圖片URL
      };

      console.log('更新原物料數據:', JSON.stringify(updatedData));



      // 更新原物料資料，不修改庫存數量
      response = await api.materials.update(editedMaterial.id, updatedData);

      if (response) {
        notification.success('原物料資料更新成功');
      }
    }

    // 完成後關閉表單
    emit('submit-success');
    closeModal();
  } catch (error) {
    console.error(props.isEdit ? '更新原物料失敗:' : '創建原物料失敗:', error);

    // 顯示更詳細的錯誤信息
    let errorMessage = props.isEdit ? '更新原物料失敗' : '創建原物料失敗';
    if (error.message) {
      errorMessage += ': ' + error.message;
    }

    notification.error(errorMessage);
  } finally {
    isSubmitting.value = false;
  }
};

// 獲取圖片URL
const getImageUrl = (url) => {
  if (!url) return '';

  console.log('原始圖片URL:', url);

  // 如果是相對路徑，添加基本 URL
  if (url.startsWith('/database/')) {
    // 將 /database/ 路徑轉換為直接訪問 uploads 目錄
    const newUrl = url.replace('/database/uploads', '/api/database/uploads');
    console.log('轉換後的URL:', newUrl);
    return newUrl;
  } else if (url.startsWith('/')) {
    // 其他相對路徑
    return url;
  }

  // 如果是 base64 或完整URL，直接返回
  return url;
};

// 處理圖片加載錯誤
const handleImageError = () => {
  console.error('圖片加載失敗:', imagePreview.value);
  // 如果是編輯模式且圖片加載失敗，則移除圖片
  if (props.isEdit) {
    notification.warning('無法加載圖片，已移除圖片預覽');
    imagePreview.value = '';
  }
};

// 處理圖片上傳
const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    // 驗證檔案類型
    if (!file.type.startsWith('image/')) {
      notification.error('請選擇圖片檔案');
      return;
    }

    // 驗證檔案大小，限制為 5MB
    if (file.size > 5 * 1024 * 1024) {
      notification.error('圖片大小不能超過 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target.result;
      imagePreview.value = result;
      formData.image = result; // 將 base64 圖片數據存入 formData
      console.log('圖片已轉換為 base64 格式，長度:', result.length);
    };
    reader.readAsDataURL(file);
  }
};

// 觸發文件選擇器
const triggerUpload = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 移除圖片
const removeImage = () => {
  imagePreview.value = '';
  formData.image = '';
  formData.image_url = ''; // 同時清除image_url屬性
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 關閉彈窗
const closeModal = () => {
  resetForm();
  emit('close');
};

// 恢復：打開設定管理
const openSettingManager = (type, title) => {
  currentSettingType.value = type;
  currentSettingTitle.value = title;
  settingManagerVisible.value = true;
};

// 處理設定更新
const handleSettingUpdated = async (updatedType) => {
  // 根據更新的設定類型重新獲取相應的設定
  if (updatedType === 'materialColor') {
    await fetchColorSettings();
    // updateFilteredColors() 會在 fetchColorSettings 內部被調用
  } else if (updatedType === 'materialUnit') {
    await fetchUnitSettings();
    // updateFilteredUnits() 會在 fetchUnitSettings 內部被調用
  } else if (updatedType === 'materialCategory') {
    await fetchCategorySettings();
    // updateFilteredCategories() 會在 fetchCategorySettings 內部被調用
  }
};

// 組件掛載時獲取設定
onMounted(() => {
  if (props.visible) {
    // 獲取所有設定
    fetchColorSettings();
    fetchUnitSettings();
    fetchCategorySettings();
    fetchAllMaterials();

    // 關閉所有下拉選單
    showColorDropdown.value = false;
    showUnitDropdown.value = false;
    showCategoryDropdown.value = false;
    showDepartmentDropdown.value = false;
  }
});
</script>

<style scoped>
.material-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1010;
}

.material-form-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  max-height: 95vh;
  overflow: hidden;
}

.material-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.material-form-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.material-form-content {
  padding: 20px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 15px;
  position: relative;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.required {
  color: #e53935;
  margin-left: 2px;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.submit-btn {
  background-color: #2196f3;
  color: white;
}

.submit-btn:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

.select-with-setting {
  display: flex;
  gap: 0; /* 移除間隔 */
  width: 100%;
}

/* 恢復 .setting-btn 的樣式 */
.setting-btn {
  padding: 0 10px; /* 調整內邊距 */
  height: 38px; /* 與輸入框高度一致 */
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-left: none; /* 視覺上連接輸入框 */
  border-radius: 0 4px 4px 0; /* 右側圓角 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  cursor: pointer;
  flex-shrink: 0; /* 防止按鈕被壓縮 */
}

.setting-btn:hover {
  background-color: #e0e0e0;
}

/* 新增：包裹輸入框和按鈕的容器 */
.input-with-dropdown-and-setting {
  display: flex;
  align-items: center;
}

/* 調整 autocomplete 容器，使其能與按鈕並排 */
.input-with-dropdown-and-setting .autocomplete {
  flex-grow: 1; /* 佔據剩餘空間 */
  position: relative; /* 保持相對定位給 dropdown */
}

/* 調整輸入框樣式，移除右側圓角 */
.input-with-dropdown-and-setting .autocomplete input {
  border-radius: 4px 0 0 4px;
  border-right: none; /* 視覺上連接按鈕 */
}

.dropdown {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
  padding: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

/* 材料編號和名稱樣式 */
.product-code {
  font-weight: 500;
  color: #2196f3;
}

.product-name {
  color: #666;
}

/* 顏色下拉項目樣式 (單獨顯示顏色) */
.color-item {
  justify-content: flex-start; /* 讓顏色靠左對齊 */
  color: #333;
}

.quantity-info {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
}

.quantity-info div {
  margin-bottom: 4px;
}

/* 自定義下拉選單樣式 */
.custom-select {
  position: relative;
  width: 100%;
  cursor: pointer;
}

.select-selected {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  border-right: none;
  background-color: white;
  font-size: 14px;
  height: 38px;
  box-sizing: border-box;
}

/* 可輸入下拉選單的輸入框容器 */
.select-input-container {
  position: relative;
  width: 100%;
}

.select-input-container input {
  width: 100%;
  padding: 10px 30px 10px 10px; /* 右側留出空間給箭頭圖標 */
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  border-right: none;
  background-color: white;
  font-size: 14px;
  height: 38px;
  box-sizing: border-box;
}

.select-input-container .select-arrow {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
}

.select-arrow {
  color: #666;
  transition: transform 0.3s;
}

.select-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.select-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.select-item:last-child {
  border-bottom: none;
}

.select-item:hover {
  background-color: #f5f5f5;
}

/* 新增外框樣式 */
.requester-department-section {
  border: 1px solid #e0e0e0; /* 淺灰色邊框 */
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px; /* 與其他 form-group 間距一致 */
  background-color: #fafafa; /* 輕微背景色區分 */
}

/* 移除外框內 form-group 的底部間距，避免雙重間距 */
.requester-department-section .form-group {
  margin-bottom: 10px; /* 調整內部間距 */
}
.requester-department-section .form-group:last-child {
  margin-bottom: 0; /* 最後一個元素無底部間距 */
}

/* 倉庫數量容器樣式 */
.warehouse-quantity-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
}

.warehouse-quantity-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.warehouse-label {
  min-width: 150px;
  font-weight: 500;
}

.warehouse-quantity-row input {
  flex: 1;
}

/* 圖片上傳相關樣式 */
.image-upload-container {
  width: 100%;
  max-width: 500px;
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview {
  width: 100%;
  height: 200px;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.upload-placeholder {
  width: 100%;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  cursor: pointer;
  transition: border-color 0.3s, color 0.3s;
}

.upload-placeholder:hover {
  border-color: #2196f3;
  color: #2196f3;
}

.upload-placeholder i {
  font-size: 48px;
  margin-bottom: 10px;
}

.remove-image-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ef4444;
  transition: background-color 0.3s;
}

.remove-image-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #dc2626;
}

/* 倉庫數量相關樣式 */
.warehouse-quantity-container {
  margin-top: 10px;
}

.warehouse-quantity-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.warehouse-label {
  width: 150px;
  font-weight: 500;
}

.quantity-info {
  margin-top: 5px;
  font-size: 14px;
  color: #666;
}

/* 廠務部倉庫相關樣式 */
.warehouse-section, .leather-section {
  margin-top: 10px;
}

.leather-entry-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  gap: 10px;
}

.leather-inputs {
  display: flex;
  flex: 1;
  gap: 10px;
}

.leather-input-group {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.leather-input-group label {
  font-size: 12px;
  margin-bottom: 4px;
  color: #666;
}

.warehouse-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.warehouse-entry {
  margin-bottom: 10px;
}

.warehouse-select {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.add-warehouse-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-warehouse-btn:hover {
  background-color: #0d8aee;
}

.remove-warehouse-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-warehouse-btn:hover {
  background-color: #d32f2f;
}

/* 皮料入庫相關樣式 */
.leather-entry {
  margin-bottom: 12px;
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #eee;
}

.taipei-leather-entry {
  margin-bottom: 20px;
  background-color: #f5f5f5;
}

.leather-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.warehouse-label-fixed {
  min-width: 80px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.leather-inputs {
  display: flex;
  flex: 1;
  gap: 10px;
  flex-wrap: wrap;
}

.leather-input-group {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 150px;
}

.leather-input-group label {
  margin-right: 5px;
  white-space: nowrap;
  font-size: 0.9em;
  color: #666;
  min-width: 80px;
}

.leather-input-group input {
  flex: 1;
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.warehouse-select {
  min-width: 100px;
  max-width: 120px;
}

/* 禁用狀態的樣式 */
.disabled-input,
.disabled-select,
.disabled-btn,
.disabled-upload,
.disabled-arrow {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f0f0f0;
  border-color: #ddd;
  color: #999;
}

.disabled-upload {
  pointer-events: none;
}

.disabled-select {
  background-color: #f0f0f0;
  border-color: #ddd;
  color: #999;
}

/* 增強禁用狀態的視覺效果 */
.form-group:has(input:disabled),
.form-group:has(select:disabled),
.form-group:has(textarea:disabled),
.form-group:has(.disabled-select),
.form-group:has(.disabled-input),
.form-group:has(.disabled-btn) {
  opacity: 0.7;
}

/* 添加提示文字 */
.form-group label::after {
  content: attr(data-hint);
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}
</style>
