<template>
  <div v-if="show" class="toast-notification" :class="notificationType">
    <div class="toast-content">
      <i class="toast-icon" :class="iconClass"></i>
      <div class="toast-message">
        <div class="toast-title">{{ title }}</div>
        <div class="toast-text">{{ message }}</div>
      </div>
      <button class="toast-close" @click="closeNotification">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  show: Boolean,
  type: {
    type: String,
    default: 'info', // 'success', 'error', 'warning', 'info'
  },
  title: {
    type: String,
    default: '通知',
  },
  message: {
    type: String,
    required: true,
  },
  duration: {
    type: Number,
    default: 3000
  }
});

const emit = defineEmits(['update:show']);

const iconClass = computed(() => {
  switch(props.type) {
    case 'success':
      return 'fas fa-check-circle';
    case 'error':
      return 'fas fa-times-circle';
    case 'warning':
      return 'fas fa-exclamation-triangle';
    default:
      return 'fas fa-info-circle';
  }
});

const notificationType = computed(() => `toast-${props.type}`);

const closeNotification = () => {
  emit('update:show', false);
};

// 自動關閉
if (props.duration > 0) {
  setTimeout(() => {
    closeNotification();
  }, props.duration);
}
</script>

<style scoped>
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  min-width: 300px;
  animation: slideIn 0.3s ease-out;
}

.toast-content {
  display: flex;
  align-items: flex-start;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  border-left: 4px solid;
}

.toast-icon {
  margin-right: 12px;
  font-size: 20px;
  margin-top: 2px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #374151;
}

.toast-text {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  word-break: break-word;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  margin-left: 12px;
  color: #9ca3af;
  border-radius: 4px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.toast-close:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 通知類型樣式 */
.toast-success {
  border-left-color: #10b981;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-error {
  border-left-color: #ef4444;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-warning {
  border-left-color: #f59e0b;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info {
  border-left-color: #3b82f6;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

/* 動畫 */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast-notification.leaving {
  animation: slideOut 0.3s ease-in;
}
</style> 