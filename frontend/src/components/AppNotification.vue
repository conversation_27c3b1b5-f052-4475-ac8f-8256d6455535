<template>
  <div v-if="show" class="notification-container" :class="notificationType">
    <div class="notification-content">
      <div class="notification-header">
        <i class="notification-icon" :class="iconClass"></i>
        <span class="notification-title">{{ title }}</span>
        <button v-if="!isConfirm" class="close-btn" @click="closeNotification">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="notification-body">
        <p>{{ message }}</p>
      </div>
      <div v-if="isConfirm" class="notification-footer">
        <button class="btn confirm-btn" @click="confirmAction">確定</button>
        <button class="btn cancel-btn" @click="cancelAction">取消</button>
      </div>
      <div v-else class="notification-footer">
        <button class="btn ok-btn" @click="closeNotification">確定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  show: Boolean,
  type: {
    type: String,
    default: 'info', // 'success', 'error', 'warning', 'info'
  },
  title: {
    type: String,
    default: '通知',
  },
  message: {
    type: String,
    required: true,
  },
  isConfirm: {
    type: Boolean,
    default: false
  },
  onConfirm: {
    type: Function,
    default: () => {}
  },
  onCancel: {
    type: Function,
    default: () => {}
  }
});

const emit = defineEmits(['update:show', 'confirm', 'cancel']);

const iconClass = computed(() => {
  switch(props.type) {
    case 'success':
      return 'fas fa-check-circle';
    case 'error':
      return 'fas fa-times-circle';
    case 'warning':
      return 'fas fa-exclamation-triangle';
    default:
      return 'fas fa-info-circle';
  }
});

const notificationType = computed(() => `notification-${props.type}`);

const closeNotification = () => {
  emit('update:show', false);
};

const confirmAction = () => {
  emit('confirm');
  if (props.onConfirm) {
    props.onConfirm();
  }
  closeNotification();
};

const cancelAction = () => {
  emit('cancel');
  if (props.onCancel) {
    props.onCancel();
  }
  closeNotification();
};
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

.notification-content {
  width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.notification-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.notification-icon {
  margin-right: 10px;
  font-size: 20px;
}

.notification-title {
  flex: 1;
  font-weight: bold;
  font-size: 18px;
}

.notification-body {
  padding: 16px;
  min-height: 60px;
  display: flex;
  align-items: center;
}

.notification-body p {
  margin: 0;
  line-height: 1.5;
  word-break: break-word;
}

.notification-footer {
  display: flex;
  justify-content: flex-end;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.confirm-btn {
  background-color: #4CAF50;
  color: white;
  margin-right: 10px;
}

.confirm-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
}

.cancel-btn:hover {
  background-color: #d32f2f;
}

.ok-btn {
  background-color: #2196F3;
  color: white;
}

.ok-btn:hover {
  background-color: #0b7dda;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  margin-left: 10px;
  color: #6c757d;
}

.close-btn:hover {
  color: #343a40;
}

/* 通知類型樣式 */
.notification-success .notification-icon {
  color: #4CAF50;
}

.notification-error .notification-icon {
  color: #f44336;
}

.notification-warning .notification-icon {
  color: #ff9800;
}

.notification-info .notification-icon {
  color: #2196F3;
}
</style> 