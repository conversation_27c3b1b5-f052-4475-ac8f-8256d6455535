<template>
  <div class="dashboard-announcement">
    <table class="announcement-table">
      <thead>
        <tr>
          <th>公告日期</th>
          <th>公告類型</th>
          <th>公告單位</th>
          <th>主旨</th>
          <th>公告人</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in displayedAnnouncements" :key="item.id" @click="viewAnnouncement(item)" style="cursor: pointer">
          <td>{{ formatDate(item.createdAt) }}</td>
          <td>{{ item.type }}</td>
          <td>
            <div class="unit-tags">
              <span v-for="(unit, index) in getUnitArray(item.announcementUnit)" :key="index" class="unit-tag">
                {{ unit }}
              </span>
            </div>
          </td>
          <td>{{ item.title }}</td>
          <td>{{ item.informer }}</td>
        </tr>
      </tbody>
    </table>

    <div v-if="loading" class="loading-indicator">
      <i class="fas fa-spinner fa-spin"></i> 載入中...
    </div>

    <div v-if="!loading && announcementList.length === 0" class="empty-state">
      暫無公告資料
    </div>

    <div class="pagination">
      <button 
        @click="currentPage > 1 && (currentPage--)" 
        :disabled="currentPage === 1" 
        class="pagination-btn"
      >
        <i class="fas fa-chevron-left"></i>
      </button>
      <span class="page-number">{{ currentPage }}</span>
      <button 
        @click="currentPage < totalPages && (currentPage++)" 
        :disabled="currentPage >= totalPages" 
        class="pagination-btn"
      >
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>

    <AnnouncementForm
      :visible="showAnnouncementForm"
      :edit-data="currentEditAnnouncement"
      :edit-mode="isEditMode"
      @close="closeAnnouncementForm"
      @save="saveAnnouncement"
      @delete="deleteAnnouncement"
      :hideButtons="true"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import AnnouncementForm from './AnnouncementForm.vue';
import api from '../services/api';

const announcementList = ref([]);
const loading = ref(false);
const showAnnouncementForm = ref(false);
const currentEditAnnouncement = ref(null);
const isEditMode = ref(false);
const currentPage = ref(1);
const itemsPerPage = 5; // 儀表板上顯示較少項目

// 計算當前頁面顯示的公告
const displayedAnnouncements = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return announcementList.value.slice(start, end);
});

// 總頁數
const totalPages = computed(() => 
  Math.ceil(announcementList.value.length / itemsPerPage) || 1
);

onMounted(async () => {
  await fetchAnnouncementList();
});

const fetchAnnouncementList = async () => {
  try {
    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    try {
      const response = await api.announcement.getAll();

      if (response.status === 'success' && response.data && response.data.length > 0) {
        // 篩選 display 欄位為 1 的公告
        announcementList.value = response.data.filter(item => item.display === 1);
      } else {
        announcementList.value = [];
      }
    } catch (error) {
      console.error('API請求失敗:', error);
      announcementList.value = [];
    }
  } catch (error) {
    console.error('獲取公告列表錯誤:', error);
  } finally {
    loading.value = false;
  }
};

const viewAnnouncement = async (item) => {
  if (!item.id) {
    console.error('無法查看：缺少公告ID');
    return;
  }

  try {
    loading.value = true;
    const response = await api.announcement.getById(item.id);

    if (response.status === 'success' && response.data) {
      currentEditAnnouncement.value = response.data;
      isEditMode.value = true;
      showAnnouncementForm.value = true;
    }
  } catch (error) {
    console.error('查看公告時發生錯誤:', error);
  } finally {
    loading.value = false;
  }
};

const closeAnnouncementForm = () => {
  showAnnouncementForm.value = false;
  isEditMode.value = false;
};

const saveAnnouncement = async () => {
  closeAnnouncementForm();
  await fetchAnnouncementList();
};

const deleteAnnouncement = async () => {
  closeAnnouncementForm();
  await fetchAnnouncementList();
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 將公告單位字符串轉換為數組
const getUnitArray = (unitString) => {
  if (!unitString) return [];
  return unitString.split(',').map(unit => unit.trim());
};
</script>

<style scoped>
.dashboard-announcement {
  width: 100%;
  overflow-x: auto;
}

.announcement-table {
  width: 100%;
  border-collapse: collapse;
}

.announcement-table th,
.announcement-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.announcement-table th {
  background-color: #f5f7fa;
  color: #666;
  font-weight: 500;
}

.unit-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.unit-tag {
  background-color: #e5e7eb;
  color: #4b5563;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  color: #666;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 15px 0;
  color: #666;
  font-size: 14px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 15px;
  gap: 8px;
}

.pagination-btn {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.page-number {
  font-size: 12px;
  padding: 0 8px;
}
</style>
