<template>
  <div class="bom-form-overlay" v-if="visible">
    <div class="bom-form-container">
      <div class="bom-form-header">
        <h3>{{ editMode ? (readOnly ? '查看BOM' : '編輯BOM') : '新增BOM' }}</h3>
        <button class="close-btn" @click="cancel">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="bom-form-content">
        <div class="form-tabs">
          <div class="tab" :class="{ active: activeTab === 'basic' }" @click="activeTab = 'basic'">
            BOM表
          </div>
          <div class="tab" :class="{ active: activeTab === 'design' }" @click="activeTab = 'design'">
            設計
          </div>
          <div class="tab" :class="{ active: activeTab === 'details' }" @click="activeTab = 'details'">
            包款細節
          </div>
          <button
            class="detail-btn"
            @click="navigateToMaterialView"
            :disabled="Number(formData.confirmationStatus) !== 0"
            :title="Number(formData.confirmationStatus) !== 0 ? '只有未確認狀態的BOM才能查看材料用量表' : ''"
          >
            <i class="fas fa-clipboard-list"></i>
            材料用量表
          </button>
          <button
            class="detail-btn"
            @click="navigateToProcessView"
            :disabled="Number(formData.confirmationStatus) !== 0"
            :title="Number(formData.confirmationStatus) !== 0 ? '只有未確認狀態的BOM才能查看備料工序' : ''"
          >
            <i class="fas fa-list-ul"></i>
            備料工序
          </button>
          <div class="save-buttons">
            <button class="confirm-btn" @click="confirmBom" :disabled="isSubmitting"
              v-if="shouldShowConfirmButton">
              {{ isSubmitting ? '處理中...' : '確認完成' }}
            </button>
            <button class="save-btn" @click="saveBom" :disabled="isSubmitting || readOnly" v-if="!readOnly">
              {{ isSubmitting ? '儲存中...' : '儲存/草稿' }}
            </button>
            <button class="cancel-btn" @click="cancel" :disabled="isSubmitting">{{ readOnly ? '關閉' : '取消' }}</button>
          </div>
        </div>

        <div class="tab-content" v-if="activeTab === 'basic'">
          <div class="form-section">
            <h4>商品基本資料</h4>
            <div class="form-group">
              <label>產品編號：</label>
              <input type="text" v-model="formData.productCode" placeholder="請輸入材料編號" :disabled="readOnly" />
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>產品類別：</label>
                <input type="text" v-model="formData.category" placeholder="請輸入產品類別" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>單位：</label>
                <input type="text" v-model="formData.unit" placeholder="請輸入單位" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>版本：</label>
                <input type="text" v-model="formData.version" placeholder="請輸入版本" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-group">
              <label>產品商標：</label>
              <input type="text" v-model="formData.trademark" placeholder="請輸入產品商標" :disabled="readOnly" />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>中文品名：</label>
                <input type="text" v-model="formData.cName" placeholder="請輸入中文品名" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>顏色：</label>
                <input type="text" v-model="formData.cColor" placeholder="請輸入顏色" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>中文大類：</label>
                <input type="text" v-model="formData.cType" placeholder="請輸入中文大類" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>英文品名：</label>
                <input type="text" v-model="formData.eName" placeholder="請輸入英文品名" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>顏色：</label>
                <input type="text" v-model="formData.eColor" placeholder="請輸入英文顏色" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>英文大類：</label>
                <input type="text" v-model="formData.eType" placeholder="請輸入英文大類" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>規格：</label>
                <input type="text" v-model="formData.format" placeholder="請輸入規格" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>條碼編號：</label>
                <input type="text" v-model="formData.barcode" placeholder="請輸入條碼編號" :disabled="readOnly" />
              </div>
            </div>
          </div>
        </div>

        <div class="tab-content" v-if="activeTab === 'design'">
          <div class="form-section">
            <h4>商品圖片</h4>
            <div class="image-upload-container">
              <div v-if="imagePreview" class="image-preview">
                <img :src="imagePreview" alt="商品圖片預覽" />
                <button class="remove-image-btn" @click="removeImage" v-if="!readOnly">
                  <i class="fas fa-trash"></i>
                </button>
              </div>
              <div v-else class="upload-placeholder" @click="!readOnly && triggerUpload()">
                <i class="fas fa-plus"></i>
                <span>{{ readOnly ? '無圖片' : '上傳圖片' }}</span>
              </div>
              <input
                type="file"
                ref="fileInput"
                @change="handleImageUpload"
                accept="image/*"
                style="display: none"
                :disabled="readOnly"
              />
            </div>
          </div>
        </div>

        <div class="tab-content" v-if="activeTab === 'details'">
          <div class="form-section">
            <h4>包款細節</h4>

            <div class="form-group">
              <label>五金顏色：</label>
              <div class="select-with-setting">
                <select v-model="formData.hardwareColor" :disabled="readOnly">
                  <option value="">請選擇</option>
                  <option v-for="color in hardwareColors" :key="color.id" :value="color.name">{{ color.name }}</option>
                </select>
                <button
                  class="setting-btn"
                  type="button"
                  @click="openSettingManager('hardwareColor', '五金顏色管理')"
                  v-if="!readOnly && isTaipeiAdmin"
                >
                  <i class="fas fa-cog"></i>
                </button>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>長度cm：</label>
                <input type="number" v-model="formData.height" placeholder="請輸入長度" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>面寬cm：</label>
                <input type="number" v-model="formData.width" placeholder="請輸入面寬" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>深度cm：</label>
                <input type="number" v-model="formData.deep" placeholder="請輸入深度" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>淨重g：</label>
                <input type="number" v-model="formData.weight" placeholder="請輸入淨重" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>提把垂高cm：</label>
                <input type="number" v-model="formData.cm1" placeholder="請輸入提把垂高" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>背帶最長垂高cm：</label>
                <input type="number" v-model="formData.cm2" placeholder="請輸入背帶最長垂高" :disabled="readOnly" />
              </div>

              <div class="form-group">
                <label>背帶最短垂高cm：</label>
                <input type="number" v-model="formData.cm3" placeholder="請輸入背帶最短垂高" :disabled="readOnly" />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>防塵袋規格：</label>
                <div class="select-with-setting">
                  <select v-model="formData.format1" :disabled="readOnly">
                    <option value="">請選擇</option>
                    <option v-for="spec in dustBagSpecs" :key="spec.id" :value="spec.name">{{ spec.name }}</option>
                  </select>
                  <button
                    class="setting-btn"
                    type="button"
                    @click="openSettingManager('dustBagSpec', '防塵袋規格管理')"
                    v-if="!readOnly && isTaipeiAdmin"
                  >
                    <i class="fas fa-cog"></i>
                  </button>
                </div>
              </div>

              <div class="form-group">
                <label>出貨箱規格：</label>
                <input type="text" v-model="formData.format2" placeholder="請輸入出貨箱規格" :disabled="readOnly" />
              </div>
            </div>
            <div class="form-group">
              <label>備註：</label>
              <textarea v-model="formData.aboutBom" placeholder="請輸入備註" :disabled="readOnly"></textarea>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 設定管理彈窗 -->
  <SettingManager
    :visible="settingManagerVisible"
    :settingType="currentSettingType"
    :title="currentSettingTitle"
    @close="settingManagerVisible = false"
    @updated="handleSettingUpdated"
  />

  <!-- 材料用量表彈窗 -->
  <MaterialUsageView
    :visible="materialUsageVisible"
    :bomId="formData.id"
    @close="closeMaterialUsageView"
  />
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import api from '../services/api';
import { useNotification } from '../services/notificationService';
import SettingManager from './SettingManager.vue';
import MaterialUsageView from '../views/design/MaterialUsageView.vue';

const router = useRouter();
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  },
  editMode: {
    type: Boolean,
    default: false
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  userDepartment: {
    type: Number,
    default: -1
  }
});

const emit = defineEmits(['close', 'save']);

const activeTab = ref('basic');
const imagePreview = ref('');
const fileInput = ref(null);
const isSubmitting = ref(false);
const notification = useNotification();

// 計算是否為台北部管理員
const isTaipeiAdmin = computed(() => props.userDepartment === 0 && props.isAdmin);

// 計算確認按鈕是否應該顯示
const shouldShowConfirmButton = computed(() => {
  // 台北部管理員，確認狀態為0或2時可以確認
  const isTaipeiAdminCanConfirm =
    props.editMode && props.userDepartment === 0 && props.isAdmin &&
    (Number(formData.confirmationStatus) === 0 || Number(formData.confirmationStatus) === 2);

  // 廠務部管理員，確認狀態為0或1時可以確認
  const isFactoryAdminCanConfirm =
    props.editMode && props.userDepartment === 1 && props.isAdmin &&
    (Number(formData.confirmationStatus) === 0 || Number(formData.confirmationStatus) === 1);

  // 其他部門管理員，確認狀態為0或1時可以確認 (與廠務部邏輯相同)
  const isOtherAdminCanConfirm =
    props.editMode && props.userDepartment !== 0 && props.userDepartment !== 1 && props.isAdmin &&
    (Number(formData.confirmationStatus) === 0 || Number(formData.confirmationStatus) === 1);

  const result = isTaipeiAdminCanConfirm || isFactoryAdminCanConfirm || isOtherAdminCanConfirm;

  console.log('確認按鈕顯示條件檢查:', {
    editMode: props.editMode,
    userDepartment: props.userDepartment,
    isAdmin: props.isAdmin,
    confirmationStatus: formData.confirmationStatus,
    confirmationStatusAsNumber: Number(formData.confirmationStatus),
    isTaipeiAdminCanConfirm,
    isFactoryAdminCanConfirm,
    isOtherAdminCanConfirm,
    result
  });

  return result;
});

// 設定管理相關
const settingManagerVisible = ref(false);
const currentSettingType = ref('');
const currentSettingTitle = ref('');

// 材料用量表相關
const materialUsageVisible = ref(false);

// 各種設定的數據
const settings = reactive({
  hardwareColor: [],
  dustBagSpec: [] // 新增：防塵袋規格
  // 可以添加更多設定類型
  // bagSize: [],
  // 等等
});

// 初始化表單數據
const formData = reactive({
  id: null,
  productCode: '',
  category: '',
  trademark: '',
  unit: '',
  version: '',
  cName: '',
  eName: '',
  cColor: '',
  eColor: '',
  cType: '',
  eType: '',
  format: '',
  barcode: '',
  pics: '',
  hardwareColor: '',
  height: '',
  width: '',
  deep: '',
  weight: '',
  cm1: '',
  cm2: '',
  cm3: '',
  format1: '', // 防塵袋規格
  format2: '',
  aboutBom: '',
  status: 0, // 預設狀態為0，使用數字而非字符串
  confirmationStatus: 0, // 預設確認狀態為0，未確認
});

// 重置表單數據的函數
const resetFormData = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 0; // 確保狀態始終為0，使用數字
    } else if (key === 'confirmationStatus') {
      formData[key] = 0; // 確保確認狀態始終為0，使用數字
    } else {
      formData[key] = '';
    }
  });
  imagePreview.value = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  activeTab.value = 'basic';
};

// 監聽 visible 屬性變化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetFormData();
    initSettings(); // 每次打開時重新加載設定

    if (props.editMode && props.editData) {
      // 填充表單
      Object.keys(props.editData).forEach(key => {
        if (key in formData) {
          // 特殊處理確認狀態，確保它始終是數字
          if (key === 'confirmationStatus') {
            formData[key] = Number(props.editData[key]) || 0;
          } else {
            formData[key] = props.editData[key];
          }
        }
      });

      // 處理圖片
      if (props.editData.pics) {
        formData.pics = props.editData.pics;
        imagePreview.value = props.editData.pics;
      }
    }
  }
});

// 觸發文件上傳
const triggerUpload = () => {
  fileInput.value.click();
};

// 處理圖片上傳 - 簡化為僅本地預覽
const handleImageUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target.result;
      imagePreview.value = result;
      formData.pics = result;
    };
    reader.readAsDataURL(file);
  }
};

// 移除圖片
const removeImage = () => {
  imagePreview.value = '';
  formData.pics = '';
  fileInput.value.value = '';
};

// 驗證表單
const validateForm = () => {
  // if (!formData.productCode) {
  //   notification.error('請輸入材料編號');
  //   return false;
  // }

  // if (!formData.cName) {
  //   notification.error('請輸入中文品名');
  //   return false;
  // }

  return true;
};

// 保存BOM
const saveBom = async () => {
  if (!validateForm()) return;

  try {
    isSubmitting.value = true;

    // 確保狀態設置為0
    formData.status = 0;

    let response;
    if (props.editMode) {
      // 更新現有BOM
      response = await api.bom.update(formData.id, formData);
    } else {
      // 創建新BOM
      response = await api.bom.create(formData);
    }
    window.location.reload();
    // 處理成功回應
    if (response) {
      emit('save', response);
      emit('close');
      setTimeout(() => {
        window.location.reload();
      }, 1200);
    }
  } catch (error) {
    console.error('BOM保存錯誤:', error);
  } finally {
    isSubmitting.value = false;
  }
};

// 確認完成BOM
const confirmBom = () => { // 改為非 async，因為確認框本身是異步的
  // 根據部門設置確認訊息
  let confirmationMessage = '您確定要確認完成此BOM嗎？';
  if (props.userDepartment === 0) {
    confirmationMessage = '您確定要進行台北部門確認嗎？';
  } else if (props.userDepartment === 1) {
    confirmationMessage = '您確定要進行廠務部門確認嗎？';
  } else {
    confirmationMessage = '您確定要進行部門確認嗎？';
  }

  notification.confirm(
    confirmationMessage, // 根據部門顯示不同確認訊息
    async () => { // 確認後執行的回調 (async)
      try {
        isSubmitting.value = true;

        // 確保有ID
        if (!formData.id) {
          notification.error('無法確認：找不到BOM ID');
          isSubmitting.value = false; // 錯誤時也要重置狀態
          return;
        }

        // 呼叫完成修改API
        const response = await api.bom.completeModification(formData.id, formData);

        if (response && response.status === 'success') {
          // 根據確認狀態顯示不同的成功訊息
          const statusNum = Number(response.data.confirmationStatus || 0);
          let successMessage = 'BOM確認完成';

          if (statusNum === 1) {
            successMessage = '台北部確認完成，等待廠務部確認';
          } else if (statusNum === 2) {
            successMessage = '廠務部確認完成，等待台北部確認';
          } else if (statusNum === 3) {
            successMessage = 'BOM雙方確認完成';
          }

          notification.success(successMessage);
          emit('save', response);
          emit('close');
          setTimeout(() => {
            window.location.reload(); // 考慮是否真的需要重新載入頁面
          }, 1200);
        } else {
           // 如果 API 回應不是 success，也需要處理
           notification.error(response?.message || '確認操作失敗');
        }
      } catch (error) {
        notification.error(error.message || '確認操作失敗，請稍後再試');
        console.error('BOM確認錯誤:', error);
      } finally {
        isSubmitting.value = false;
      }
    },
    () => { // 取消後執行的回調 (可選)
      // console.log('使用者取消了確認');
      // 如果需要在取消時做什麼，可以在這裡添加代碼
    },
    '確認操作' // 對話框標題 (可選)
  );
};

// 取消並關閉表單
const cancel = () => {
  emit('close');
};

// 打開設定管理器
const openSettingManager = (type, title) => {
  currentSettingType.value = type;
  currentSettingTitle.value = title;
  settingManagerVisible.value = true;
};

// 處理設定更新
const handleSettingUpdated = (type) => {
  // 重新加載已更新的設定類型
  fetchSettings(type);
};

// 獲取指定類型的設定
const fetchSettings = async (type) => {
  try {
    const response = await api.settingRecord.getAll({
      type: type
    });

    if (response && response.data && response.data.length > 0) {
      // 根據 sort_order 排序
      settings[type] = response.data.sort((a, b) => a.sort_order - b.sort_order);
    }
  } catch (error) {
    console.error(`獲取${type}設定失敗:`, error);
    // 出錯時使用默認值
    if (type === 'hardwareColor') {
      settings[type] = [
        { id: 'default-1', name: '金色', sort_order: 0 },
        { id: 'default-2', name: '銀色', sort_order: 1 },
        { id: 'default-3', name: '黑色', sort_order: 2 },
        { id: 'default-4', name: '其他', sort_order: 3 }
      ];
    } else if (type === 'dustBagSpec') { // 新增：處理防塵袋規格錯誤
      settings[type] = [
        { id: 'default-db-1', name: '小', sort_order: 0 },
        { id: 'default-db-2', name: '中', sort_order: 1 },
        { id: 'default-db-3', name: '大', sort_order: 2 },
        { id: 'default-db-4', name: '特大', sort_order: 3 }
      ];
    }
  }
};

// 初始化設定數據
const initSettings = async () => {
  // 獲取所有需要的設定類型
  await fetchSettings('hardwareColor');
  await fetchSettings('dustBagSpec'); // 新增：獲取防塵袋規格
  // 可以添加更多類型
  // await fetchSettings('bagSize');
};

// 元件掛載時獲取設定
onMounted(() => {
  // 移除這裡的 initSettings()，改在 watch visible 時調用
});

// 將舊的 hardwareColors 修改為使用新的設定系統
const hardwareColors = computed(() => settings.hardwareColor);
const dustBagSpecs = computed(() => settings.dustBagSpec); // 新增：防塵袋規格計算屬性

// 導航到備料工序頁面
const navigateToProcessView = () => {
  if (Number(formData.confirmationStatus) !== 0) {
    notification.warning('只有未確認狀態的BOM才能查看備料工序');
    return;
  }

  if (!formData.id) {
    notification.warning('請先儲存BOM資料');
    return;
  }

  // 儲存當前編輯中的BOM ID
  localStorage.setItem('currentBomId', formData.id);

  // 導航到備料工序頁面
  router.push({
    name: 'ProcessPreparation',
    params: { bomId: formData.id }
  });
};

// 顯示材料用量表彈窗
const navigateToMaterialView = () => {
  if (Number(formData.confirmationStatus) !== 0) {
    notification.warning('只有未確認狀態的BOM才能查看材料用量表');
    return;
  }

  if (!formData.id) {
    notification.warning('請先儲存BOM資料');
    return;
  }

  // 顯示材料用量表彈窗
  materialUsageVisible.value = true;
};

// 關閉材料用量表彈窗
const closeMaterialUsageView = () => {
  materialUsageVisible.value = false;
};
</script>

<style scoped>
.bom-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.bom-form-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.bom-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.bom-form-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.bom-form-content {
  display: flex;
  height: 80vh;
  max-height: 700px;
}

.form-tabs {
  width: 180px;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #ddd;
}

.tab {
  padding: 14px 16px;
  cursor: pointer;
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.tab:hover {
  background-color: #e9e9e9;
}

.tab.active {
  background-color: #e1f5fe;
  border-left-color: #2196f3;
  color: #2196f3;
}

.save-buttons {
  margin-top: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.save-btn, .cancel-btn {
  padding: 10px 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.confirm-btn {
  background-color: #3b82f6;
  color: white;
  padding: 10px 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 10px;
  transition: background-color 0.3s;
}

.confirm-btn:hover {
  background-color: #2563eb;
}

.confirm-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  background-color: #059669;
}

.cancel-btn {
  background-color: #9ca3af;
  color: white;
}

.cancel-btn:hover {
  background-color: #d5d5d5;
}

.tab-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-section {
  background-color: #fff;
  margin-bottom: 20px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
  flex: 1;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #2196f3;
  outline: none;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #bbb;
  opacity: 1;
}

.form-group input::-webkit-input-placeholder,
.form-group textarea::-webkit-input-placeholder {
  color: #bbb;
}
.form-group input::-moz-placeholder,
.form-group textarea::-moz-placeholder {
  color: #bbb;
  opacity: 1;
}
.form-group input:-ms-input-placeholder,
.form-group textarea:-ms-input-placeholder {
  color: #bbb;
}
.form-group input::-ms-input-placeholder,
.form-group textarea::-ms-input-placeholder {
  color: #bbb;
}


.form-group textarea {
  height: 120px;
  resize: vertical;
}

.image-upload-container {
  width: 100%;
  max-width: 500px;
  margin: 20px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview {
  width: 100%;
  height: 300px;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.upload-placeholder {
  width: 100%;
  height: 300px;
  border: 2px dashed #ddd;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  transition: border-color 0.3s, color 0.3s;
}

.upload-placeholder:hover {
  border-color: #2196f3;
  color: #2196f3;
}

.upload-placeholder i {
  font-size: 48px;
  margin-bottom: 10px;
}

.remove-image-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ef4444;
  transition: background-color 0.3s;
}

.remove-image-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #dc2626;
}

.form-hint {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

.form-group input::-webkit-input-placeholder,
.form-group textarea::-webkit-input-placeholder {
  color: #bbb;
}

.select-with-setting {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-with-setting select {
  flex: 1;
}

.setting-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #555;
  transition: all 0.2s;
}

.setting-btn:hover {
  background-color: #e3e3e3;
  color: #333;
}

.form-buttons {
  display: flex;
  justify-content: center;
  margin-top: 10px;

}

.detail-btn {
  padding: 14px 16px;
  background-color: #f5f5f5;
  color: #333;
  border: none;
  border-left: 3px solid transparent;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
  text-align: left;
  transition: all 0.3s;
}

.detail-btn:hover {
  background-color: #e9e9e9;
}

.detail-btn:disabled {
  background-color: #f5f5f5;
  color: #aaa;
  cursor: not-allowed;
  opacity: 0.6;
}

.detail-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: flex-start;
}
</style>
