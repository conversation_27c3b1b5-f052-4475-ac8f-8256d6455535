import { API_URL } from './api/config';

// 獲取JWT令牌
const getToken = () => localStorage.getItem('token');

// 獲取帶有認證的請求頭
const getHeaders = () => ({
  'Authorization': `Bearer ${getToken()}`,
  'Content-Type': 'application/json'
});

// API請求封裝
const api = {
  // 登入
  login: async (username, password) => {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password })
    });

    return await response.json();
  },

  // 獲取當前用戶信息
  getCurrentUser: async () => {
    const response = await fetch(`${API_URL}/auth/me`, {
      method: 'GET',
      headers: getHeaders()
    });

    return await response.json();
  },

  // BOM相關API
  bom: {
    // 獲取所有BOM
    getAll: async () => {
      const response = await fetch(`${API_URL}/bom`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取BOM列表失敗');
      }

      return await response.json();
    },

    // 獲取單個BOM
    getById: async (id) => {
      const response = await fetch(`${API_URL}/bom/${id}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取BOM詳情失敗');
      }

      return await response.json();
    },

    // 創建BOM
    create: async (bomData) => {
      const response = await fetch(`${API_URL}/bom`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(bomData)
      });

      return await response.json();
    },

    // 更新BOM
    update: async (id, bomData) => {
      const response = await fetch(`${API_URL}/bom/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(bomData)
      });

      if (!response.ok) {
        throw new Error('更新BOM失敗');
      }

      return await response.json();
    },

    // 完成修改 BOM
    completeModification: async (id, bomData) => {
      const response = await fetch(`${API_URL}/bom/${id}/complete`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(bomData)
      });

      if (!response.ok) {
        throw new Error('完成修改BOM失敗');
      }

      return await response.json();
    },

    // 搜尋BOM
    search: async (query) => {
      const response = await fetch(`${API_URL}/bom/search?query=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('搜尋BOM失敗');
      }

      return await response.json();
    }
  },

  // 備料工序相關API
  processPreparation: {
    // 獲取特定BOM的備料工序
    getByBomId: async (bomId) => {
      const response = await fetch(`${API_URL}/process-preparation/bom/${bomId}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取備料工序失敗');
      }

      return await response.json();
    },

    // 創建備料工序
    create: async (processData) => {
      const response = await fetch(`${API_URL}/process-preparation`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(processData)
      });

      if (!response.ok) {
        throw new Error('創建備料工序失敗');
      }

      return await response.json();
    },

    // 更新備料工序
    update: async (id, processData) => {
      const response = await fetch(`${API_URL}/process-preparation/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(processData)
      });

      if (!response.ok) {
        throw new Error('更新備料工序失敗');
      }

      return await response.json();
    },

    // 刪除備料工序
    delete: async (id) => {
      const response = await fetch(`${API_URL}/process-preparation/${id}`, {
        method: 'DELETE',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('刪除備料工序失敗');
      }

      return await response.json();
    },

    // 獲取工具列表
    getTools: async () => {
      const response = await fetch(`${API_URL}/process-preparation/tools`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取工具列表失敗');
      }

      return await response.json();
    },

    // 獲取耗材列表
    getConsumables: async () => {
      const response = await fetch(`${API_URL}/process-preparation/consumables`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取耗材列表失敗');
      }

      return await response.json();
    },

    // 合併工序
    mergeProcess: async (sourceProcessId, targetProcessId) => {
      const response = await fetch(`${API_URL}/process-preparation/merge`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ sourceProcessId, targetProcessId })
      });

      if (!response.ok) {
        throw new Error('合併工序失敗');
      }

      return await response.json();
    },

    // 取消工序合併
    unmergeProcess: async (sourceProcessId) => {
      const response = await fetch(`${API_URL}/process-preparation/unmerge`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ sourceProcessId })
      });

      if (!response.ok) {
        throw new Error('取消工序合併失敗');
      }

      return await response.json();
    },

    // 獲取可合併的工序列表
    getMergeableProcesses: async (bomId, excludeProcessId = null) => {
      let url = `${API_URL}/process-preparation/mergeable/${bomId}`;
      if (excludeProcessId) {
        url += `?excludeProcessId=${excludeProcessId}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取可合併工序列表失敗');
      }

      return await response.json();
    }
  },

  // BOM 材料用量相關 API (替換舊的 materialUsage)
  bomMaterialUsage: {
    // 獲取指定 BOM 的材料用量列表
    getByBomId: async (bomId) => {
      const response = await fetch(`${API_URL}/bom/${bomId}/material-usage`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '獲取 BOM 材料用量失敗' }));
        throw new Error(errorData.message || '獲取 BOM 材料用量失敗');
      }

      return await response.json();
    },

    // 為指定 BOM 添加材料用量記錄
    create: async (bomId, usageData) => {
      const response = await fetch(`${API_URL}/bom/${bomId}/material-usage`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(usageData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '添加 BOM 材料用量失敗' }));
        throw new Error(errorData.message || '添加 BOM 材料用量失敗');
      }

      return await response.json();
    },

    // 上傳 BOM 材料用量圖片
    uploadImage: async (bomId, imageData) => {
      const response = await fetch(`${API_URL}/bom/${bomId}/material-usage/upload-image`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify({ image: imageData })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '上傳圖片失敗' }));
        throw new Error(errorData.message || '上傳圖片失敗');
      }

      return await response.json();
    },

    // 更新指定 BOM 的材料用量記錄
    update: async (bomId, usageId, updateData) => {
      // 如果後端沒有實現更新功能，我們使用刪除和新增的組合來模擬更新
      try {
        // 先獲取原有數據
        const getResponse = await fetch(`${API_URL}/bom/${bomId}/material-usage`, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!getResponse.ok) {
          throw new Error('獲取原有材料用量失敗');
        }

        const result = await getResponse.json();
        const existingItem = result.data.find(item => item.id === usageId);

        if (!existingItem) {
          throw new Error('找不到要更新的材料用量記錄');
        }

        // 合併原有數據和更新數據
        const updatedData = {
          ...existingItem,
          ...updateData,
          id: usageId, // 確保 ID 不變
          bom_id: bomId // 確保 BOM ID 不變
        };

        // 先刪除原有記錄
        const deleteResponse = await fetch(`${API_URL}/bom/${bomId}/material-usage/${usageId}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!deleteResponse.ok) {
          throw new Error('刪除原有材料用量記錄失敗');
        }

        // 創建新記錄
        const createResponse = await fetch(`${API_URL}/bom/${bomId}/material-usage`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(updatedData)
        });

        if (!createResponse.ok) {
          throw new Error('創建更新後的材料用量記錄失敗');
        }

        return await createResponse.json();
      } catch (error) {
        console.error('更新 BOM 材料用量失敗:', error);
        throw new Error(`更新 BOM 材料用量失敗: ${error.message}`);
      }
    },

    // 刪除指定 BOM 的材料用量記錄
    delete: async (bomId, usageId) => {
      const response = await fetch(`${API_URL}/bom/${bomId}/material-usage/${usageId}`, {
        method: 'DELETE',
        headers: getHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: '刪除 BOM 材料用量失敗' }));
        throw new Error(errorData.message || '刪除 BOM 材料用量失敗');
      }

      return await response.json();
    }
  },

  // 公告相關API
  announcement: {
    // 獲取所有公告
    getAll: async () => {
      const response = await fetch(`${API_URL}/announcement`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取公告列表失敗');
      }

      return await response.json();
    },

    // 獲取單個公告
    getById: async (id) => {
      const response = await fetch(`${API_URL}/announcement/${id}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取公告詳情失敗');
      }

      return await response.json();
    },

    // 創建公告
    create: async (announcementData) => {
      const response = await fetch(`${API_URL}/announcement`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(announcementData)
      });

      if (!response.ok) {
        throw new Error('創建公告失敗');
      }

      return await response.json();
    },

    // 更新公告
    update: async (id, announcementData) => {
      const response = await fetch(`${API_URL}/announcement/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(announcementData)
      });

      if (!response.ok) {
        throw new Error('更新公告失敗');
      }

      return await response.json();
    },

    // 刪除公告
    delete: async (id) => {
      const response = await fetch(`${API_URL}/announcement/${id}`, {
        method: 'DELETE',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('刪除公告失敗');
      }

      return await response.json();
    },

    // 搜尋公告
    search: async (query) => {
      const response = await fetch(`${API_URL}/announcement/search?query=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('搜尋公告失敗');
      }

      return await response.json();
    }
  },

  // 員工相關API
  employee: {
    // 獲取所有員工
    getAll: async () => {
      const response = await fetch(`${API_URL}/employee`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取員工列表失敗');
      }

      const data = await response.json();
      console.log('獲取員工列表響應:', data);
      return data;
    },

    // 獲取單個員工
    getById: async (id) => {
      const response = await fetch(`${API_URL}/employee/${id}`, {
        method: 'GET',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('獲取員工詳情失敗');
      }

      return await response.json();
    },

    // 創建員工
    create: async (employeeData) => {
      console.log('創建員工請求數據:', employeeData);
      const response = await fetch(`${API_URL}/employee`, {
        method: 'POST',
        headers: getHeaders(),
        body: JSON.stringify(employeeData)
      });

      const responseText = await response.text();
      console.log('創建員工原始響應:', responseText);

      if (!response.ok) {
        throw new Error(`創建員工失敗: ${responseText}`);
      }

      try {
        const data = JSON.parse(responseText);
        console.log('創建員工響應數據:', data);
        return data;
      } catch (error) {
        console.error('解析創建員工響應失敗:', error);
        throw new Error(`創建員工響應解析失敗: ${responseText}`);
      }
    },

    // 更新員工
    update: async (id, employeeData) => {
      console.log('更新員工請求數據:', id, employeeData);
      const response = await fetch(`${API_URL}/employee/${id}`, {
        method: 'PUT',
        headers: getHeaders(),
        body: JSON.stringify(employeeData)
      });

      const responseText = await response.text();
      console.log('更新員工原始響應:', responseText);

      if (!response.ok) {
        throw new Error(`更新員工失敗: ${responseText}`);
      }

      try {
        const data = JSON.parse(responseText);
        console.log('更新員工響應數據:', data);
        return data;
      } catch (error) {
        console.error('解析更新員工響應失敗:', error);
        throw new Error(`更新員工響應解析失敗: ${responseText}`);
      }
    },

    // 刪除員工
    delete: async (id) => {
      const response = await fetch(`${API_URL}/employee/${id}`, {
        method: 'DELETE',
        headers: getHeaders()
      });

      if (!response.ok) {
        throw new Error('刪除員工失敗');
      }

      return await response.json();
    }
  },

  // 設定記錄相關API
  settingRecord: {
    // 獲取所有設定記錄
    getAll: async (params = {}) => {
      try {
        let url = `${API_URL}/settingRecord`;

        // 添加查詢參數
        if (Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(params)) {
            queryParams.append(key, value);
          }
          url += `?${queryParams.toString()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取設定記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取設定記錄失敗:', error);
        throw new Error('獲取設定記錄失敗');
      }
    },

    // 獲取單個設定記錄
    getById: async (id) => {
      try {
        const response = await fetch(`${API_URL}/settingRecord/${id}`, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取設定記錄詳情失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取設定記錄詳情失敗:', error);
        throw new Error('獲取設定記錄詳情失敗');
      }
    },

    // 創建設定記錄
    create: async (settingData) => {
      try {
        const response = await fetch(`${API_URL}/settingRecord`, {
          method: 'POST',
          headers: {
            ...getHeaders(),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(settingData)
        });

        if (!response.ok) {
          throw new Error('創建設定記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建設定記錄失敗:', error);
        throw new Error('創建設定記錄失敗');
      }
    },

    // 更新設定記錄
    update: async (id, settingData) => {
      try {
        const response = await fetch(`${API_URL}/settingRecord/${id}`, {
          method: 'PUT',
          headers: {
            ...getHeaders(),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(settingData)
        });

        if (!response.ok) {
          throw new Error('更新設定記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('更新設定記錄失敗:', error);
        throw new Error('更新設定記錄失敗');
      }
    },

    // 刪除設定記錄
    delete: async (id) => {
      try {
        const response = await fetch(`${API_URL}/settingRecord/${id}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('刪除設定記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('刪除設定記錄失敗:', error);
        throw new Error('刪除設定記錄失敗');
      }
    }
  },

  // 原物料相關API
  materials: {
    // 獲取所有原物料
    getAll: async (params = {}) => {
      try {
        let url = `${API_URL}/inventory/materials`;

        // 添加查詢參數
        if (Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
              queryParams.append(key, value);
            }
          }
          url += `?${queryParams.toString()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取原物料列表失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取原物料列表失敗:', error);
        throw new Error('獲取原物料列表失敗');
      }
    },

    // 獲取單個原物料
    getById: async (id) => {
      try {
        const response = await fetch(`${API_URL}/inventory/materials/${id}`, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取原物料詳情失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取原物料詳情失敗:', error);
        throw new Error('獲取原物料詳情失敗');
      }
    },

    // 創建原物料
    create: async (materialData) => {
      try {
        // 如果有圖片數據，則直接包含在請求中
        // 後端會處理 base64 圖片數據
        const response = await fetch(`${API_URL}/inventory/materials`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(materialData)
        });

        // 先獲取原始響應文本
        const responseText = await response.text();

        if (!response.ok) {
          throw new Error(`創建原物料失敗: ${responseText}`);
        }

        // 嘗試解析 JSON
        try {
          const data = JSON.parse(responseText);
          return data;
        } catch (parseError) {
          throw new Error(`創建原物料響應解析失敗: ${responseText}`);
        }
      } catch (error) {
        console.error('創建原物料失敗:', error);
        throw error;
      }
    },

    // 更新原物料
    update: async (id, materialData) => {
      try {
        // 如果有圖片數據，則直接包含在請求中
        // 後端會處理 base64 圖片數據
        const response = await fetch(`${API_URL}/inventory/materials/${id}`, {
          method: 'PUT',
          headers: getHeaders(),
          body: JSON.stringify(materialData)
        });

        if (!response.ok) {
          throw new Error('更新原物料失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('更新原物料失敗:', error);
        throw new Error('更新原物料失敗');
      }
    },

    // 刪除原物料
    delete: async (id) => {
      try {
        const response = await fetch(`${API_URL}/inventory/materials/${id}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('刪除原物料失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('刪除原物料失敗:', error);
        throw new Error('刪除原物料失敗');
      }
    },

    // 創建原物料領用記錄
    createUsage: async (usageData) => {
      try {
        const response = await fetch(`${API_URL}/inventory/materials/usage`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(usageData)
        });

        if (!response.ok) {
          throw new Error('創建領用記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建領用記錄失敗:', error);
        throw new Error('創建領用記錄失敗');
      }
    },

    // 獲取領用記錄
    getUsage: async (params = {}) => {
      try {
        let url = `${API_URL}/inventory/materials/usage`;

        // 添加查詢參數
        if (Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
              queryParams.append(key, value);
            }
          }
          url += `?${queryParams.toString()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取領用記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取領用記錄失敗:', error);
        throw new Error('獲取領用記錄失敗');
      }
    },

    // 創建原物料交易記錄 (入庫或領用)
    createTransaction: async (transactionData) => {
      try {
        const response = await fetch(`${API_URL}/inventory/materials/transaction`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(transactionData)
        });

        if (!response.ok) {
          throw new Error('創建交易記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建交易記錄失敗:', error);
        throw new Error('創建交易記錄失敗');
      }
    },

    // 創建原物料調撥記錄
    createTransfer: async (transferData) => {
      try {
        const response = await fetch(`${API_URL}/inventory/materials/transfer`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(transferData)
        });

        if (!response.ok) {
          throw new Error('創建調撥記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建調撥記錄失敗:', error);
        throw new Error('創建調撥記錄失敗');
      }
    },

    // 獲取交易記錄
    getTransactions: async (params = {}) => {
      try {
        let url = `${API_URL}/inventory/materials/transactions`;

        // 添加查詢參數
        if (Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
              queryParams.append(key, value);
            }
          }
          url += `?${queryParams.toString()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取交易記錄失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取交易記錄失敗:', error);
        throw new Error('獲取交易記錄失敗');
      }
    },

    // 獲取指定部門的原物料
    getByDepartment: async (department, search = '') => {
      try {
        let url = `${API_URL}/inventory/materials/department/${encodeURIComponent(department)}`;

        // 添加搜索參數（如果有）
        if (search) {
          url += `?search=${encodeURIComponent(search)}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`獲取部門原物料列表失敗: ${errorText}`);
        }

        return await response.json();
      } catch (error) {
        console.error('獲取部門原物料失敗:', error);
        throw new Error('獲取部門原物料失敗');
      }
    },

    // 皮料詳情相關方法
    leatherMaterials: {
      // 獲取皮料詳情列表
      getAll: async (params = {}) => {
        try {
          let url = `${API_URL}/inventory/leather-materials`;

          // 添加查詢參數
          if (Object.keys(params).length > 0) {
            const queryParams = new URLSearchParams();
            for (const [key, value] of Object.entries(params)) {
              if (value !== undefined) {
                queryParams.append(key, value);
              }
            }
            url += `?${queryParams.toString()}`;
          }

          const response = await fetch(url, {
            method: 'GET',
            headers: getHeaders()
          });

          if (!response.ok) {
            throw new Error('獲取皮料詳情列表失敗');
          }

          return await response.json();
        } catch (error) {
          console.error('獲取皮料詳情列表失敗:', error);
          throw new Error('獲取皮料詳情列表失敗');
        }
      },

      // 獲取單個皮料詳情
      getById: async (id) => {
        try {
          const response = await fetch(`${API_URL}/inventory/leather-materials/${id}`, {
            method: 'GET',
            headers: getHeaders()
          });

          if (!response.ok) {
            throw new Error('獲取皮料詳情失敗');
          }

          return await response.json();
        } catch (error) {
          console.error('獲取皮料詳情失敗:', error);
          throw new Error('獲取皮料詳情失敗');
        }
      },

      // 創建皮料詳情
      create: async (detailData) => {
        try {
          const response = await fetch(`${API_URL}/inventory/leather-materials`, {
            method: 'POST',
            headers: getHeaders(),
            body: JSON.stringify(detailData)
          });

          if (!response.ok) {
            throw new Error('創建皮料詳情失敗');
          }

          return await response.json();
        } catch (error) {
          console.error('創建皮料詳情失敗:', error);
          throw new Error('創建皮料詳情失敗');
        }
      },

      // 更新皮料詳情
      update: async (id, detailData) => {
        try {
          const response = await fetch(`${API_URL}/inventory/leather-materials/${id}`, {
            method: 'PUT',
            headers: getHeaders(),
            body: JSON.stringify(detailData)
          });

          if (!response.ok) {
            throw new Error('更新皮料詳情失敗');
          }

          return await response.json();
        } catch (error) {
          console.error('更新皮料詳情失敗:', error);
          throw new Error('更新皮料詳情失敗');
        }
      },

      // 刪除皮料詳情
      delete: async (id) => {
        try {
          const response = await fetch(`${API_URL}/inventory/leather-materials/${id}`, {
            method: 'DELETE',
            headers: getHeaders()
          });

          if (!response.ok) {
            throw new Error('刪除皮料詳情失敗');
          }

          return await response.json();
        } catch (error) {
          console.error('刪除皮料詳情失敗:', error);
          throw new Error('刪除皮料詳情失敗');
        }
      }
    }
  },

  // 產品用料明細表相關 API
  materialDetailSheet: {
    // 獲取產品用料明細表項目
    getItems: async (params) => {
      try {
        let url = `${API_URL}/material-detail-sheet`;

        // 添加查詢參數
        if (Object.keys(params).length > 0) {
          const queryParams = new URLSearchParams();
          for (const [key, value] of Object.entries(params)) {
            if (value !== undefined) {
              queryParams.append(key, value);
            }
          }
          url += `?${queryParams.toString()}`;
        }

        const response = await fetch(url, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取產品用料明細表項目失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('獲取產品用料明細表項目失敗:', error);
        throw new Error('獲取產品用料明細表項目失敗');
      }
    },

    // 匯入Excel檔案
    importExcel: async (formData) => {
      try {
        const response = await fetch(`${API_URL}/material-detail-sheet/import`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${getToken()}`
            // 不設置 Content-Type，讓瀏覽器自動設置為 multipart/form-data
          },
          body: formData
        });

        if (!response.ok) {
          throw new Error('匯入Excel檔案失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('匯入Excel檔案失敗:', error);
        throw new Error('匯入Excel檔案失敗');
      }
    },

    // 創建明細表項目
    create: async (data) => {
      try {
        const response = await fetch(`${API_URL}/material-detail-sheet`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          throw new Error('創建明細表項目失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建明細表項目失敗:', error);
        throw new Error('創建明細表項目失敗');
      }
    },

    // 更新明細表項目
    update: async (id, data) => {
      try {
        const response = await fetch(`${API_URL}/material-detail-sheet/${id}`, {
          method: 'PUT',
          headers: getHeaders(),
          body: JSON.stringify(data)
        });

        if (!response.ok) {
          throw new Error('更新明細表項目失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('更新明細表項目失敗:', error);
        throw new Error('更新明細表項目失敗');
      }
    },

    // 刪除明細表項目
    delete: async (id) => {
      try {
        const response = await fetch(`${API_URL}/material-detail-sheet/${id}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('刪除明細表項目失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('刪除明細表項目失敗:', error);
        throw new Error('刪除明細表項目失敗');
      }
    },

    // 刪除所有明細表項目
    deleteAll: async (params) => {
      try {
        // 建立查詢參數
        const queryParams = new URLSearchParams();
        if (params.bomId) queryParams.append('bomId', params.bomId);
        if (params.sheetType) queryParams.append('sheetType', params.sheetType);
        // materialId 現在是可選的
        if (params.materialId) queryParams.append('materialId', params.materialId);

        const response = await fetch(`${API_URL}/material-detail-sheet/delete-all?${queryParams.toString()}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('刪除所有明細表項目失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('刪除所有明細表項目失敗:', error);
        throw new Error('刪除所有明細表項目失敗');
      }
    }
  },

  // 皮料相關 API
  leather: {
    // 獲取指定原物料的皮料詳情
    getDetailsByMaterialId: async (materialId) => {
      try {
        const response = await fetch(`${API_URL}/inventory/leather-materials?material_id=${materialId}`, {
          method: 'GET',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('獲取皮料詳情失敗');
        }

        // 處理響應格式轉換
        const data = await response.json();
        // 將API資料轉換為期望的格式
        return {
          material: data.material || {},
          details: data.data || []
        };
      } catch (error) {
        console.error('獲取皮料詳情失敗:', error);
        throw new Error('獲取皮料詳情失敗');
      }
    },

    // 創建皮料詳情
    createDetail: async (detailData) => {
      try {
        const response = await fetch(`${API_URL}/inventory/leather-materials`, {
          method: 'POST',
          headers: getHeaders(),
          body: JSON.stringify(detailData)
        });

        if (!response.ok) {
          throw new Error('創建皮料詳情失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('創建皮料詳情失敗:', error);
        throw new Error('創建皮料詳情失敗');
      }
    },

    // 更新皮料詳情
    updateDetail: async (id, detailData) => {
      try {
        const response = await fetch(`${API_URL}/inventory/leather-materials/${id}`, {
          method: 'PUT',
          headers: getHeaders(),
          body: JSON.stringify(detailData)
        });

        if (!response.ok) {
          throw new Error('更新皮料詳情失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('更新皮料詳情失敗:', error);
        throw new Error('更新皮料詳情失敗');
      }
    },

    // 刪除皮料詳情
    deleteDetail: async (id) => {
      try {
        const response = await fetch(`${API_URL}/inventory/leather-materials/${id}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        if (!response.ok) {
          throw new Error('刪除皮料詳情失敗');
        }

        return await response.json();
      } catch (error) {
        console.error('刪除皮料詳情失敗:', error);
        throw new Error('刪除皮料詳情失敗');
      }
    }
  }
};

export default api;
