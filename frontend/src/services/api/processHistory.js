import axios from 'axios';
import { API_BASE_URL } from './config';

// 獲取JWT令牌
const getToken = () => localStorage.getItem('token');

// 獲取帶有認證的請求頭
const getHeaders = () => ({
  'Authorization': `Bearer ${getToken()}`,
  'Content-Type': 'application/json'
});

// 創建 axios 實例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: getHeaders()
});

// 請求攔截器，動態添加認證頭
api.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const processHistoryApi = {
  // 保存歷史記錄
  saveHistory: async (bomId, description, stateData) => {
    try {
      const response = await api.post('/api/process-history/save', {
        bomId,
        description,
        stateData
      });
      return response.data;
    } catch (error) {
      console.error('保存歷史記錄錯誤:', error);
      throw error;
    }
  },

  // 獲取歷史記錄列表
  getHistoryList: async (bomId, limit = 20) => {
    try {
      const response = await api.get(`/api/process-history/list/${bomId}`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('獲取歷史記錄列表錯誤:', error);
      throw error;
    }
  },

  // 獲取歷史記錄詳情
  getHistoryDetail: async (historyId) => {
    try {
      const response = await api.get(`/api/process-history/detail/${historyId}`);
      return response.data;
    } catch (error) {
      console.error('獲取歷史記錄詳情錯誤:', error);
      throw error;
    }
  },

  // 清除歷史記錄
  clearHistory: async (bomId) => {
    try {
      const response = await api.delete(`/api/process-history/clear/${bomId}`);
      return response.data;
    } catch (error) {
      console.error('清除歷史記錄錯誤:', error);
      throw error;
    }
  },

  // 批量保存歷史記錄
  saveHistoryBatch: async (bomId, historyList) => {
    try {
      const response = await api.post('/api/process-history/batch', {
        bomId,
        historyList
      });
      return response.data;
    } catch (error) {
      console.error('批量保存歷史記錄錯誤:', error);
      throw error;
    }
  }
}; 