<template>
  <div class="layout">
    <!-- 頂部導航欄 -->
    <header class="header">
      <div class="user-info">
        <span>員工編號：{{ user?.employeeId || '-' }}</span>
        <span>姓名：{{ user?.name || '-' }}</span>
        <span>單位：{{ departmentName(user?.department) || '-' }}</span>
        <span>職稱：{{ user?.position || '-' }} ({{ getRoleName(user?.role) }})</span>
      </div>
      <button @click="handleLogout" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i>
        登出
      </button>
    </header>

    <!-- 側邊欄 -->
    <aside class="sidebar">
      <div class="logo-container" @click="router.push('/')" style="cursor: pointer;">
        <img src="../assets/logo.png" alt="Logo" class="logo" />
        <h1 class="system-name">華辰MES管理系統</h1>
      </div>
      
      <nav class="menu">
        <div class="menu-item" :class="{ active: currentRoute.startsWith('/design') }" @click="toggleSubmenu('design')">
          <i class="fas fa-drafting-compass"></i>
          <span>設計開發</span>
          <i class="fas fa-chevron-down expand-icon" :class="{ 'rotated': openSubmenu === 'design' }"></i>
        </div>
        
        <div class="submenu" v-show="openSubmenu === 'design'">
          <router-link to="/design/bom" class="submenu-item" :class="{ active: currentRoute === '/design/bom' }">
            <i class="fas fa-list"></i>
            <span>BOM</span>
          </router-link>
          <router-link to="/design/order" class="submenu-item" :class="{ active: currentRoute === '/design/order' }">
            <i class="fas fa-shopping-cart"></i>
            <span>訂單</span>
          </router-link>
          <router-link to="/design/change" class="submenu-item" :class="{ active: currentRoute === '/design/change' }">
            <i class="fas fa-exchange-alt"></i>
            <span>設計變更單</span>
          </router-link>
          <router-link to="/design/process" class="submenu-item" :class="{ active: currentRoute === '/design/process' }">
            <i class="fas fa-tasks"></i>
            <span>加工指示單</span>
          </router-link>
        </div>
        
        <div class="menu-item" :class="{ active: currentRoute === '/report' }">
          <i class="fas fa-tasks"></i>
          <span>報工作業</span>
          <i class="fas fa-chevron-down expand-icon"></i>
        </div>
        
        <div class="menu-item" :class="{ active: currentRoute.startsWith('/management') }" @click="toggleSubmenu('management')">
          <i class="fas fa-cogs"></i>
          <span>管理</span>
          <i class="fas fa-chevron-down expand-icon" :class="{ 'rotated': openSubmenu === 'management' }"></i>
        </div>

        <div class="submenu" v-show="openSubmenu === 'management'">
          <router-link to="/management/announcement" class="submenu-item" :class="{ active: currentRoute === '/management/announcement' }">
            <i class="fas fa-bullhorn"></i>
            <span>公告欄</span>
          </router-link>
        </div>
        
        <div class="menu-item" :class="{ active: currentRoute.startsWith('/system') }" @click="toggleSubmenu('system')">
          <i class="fas fa-sliders-h"></i>
          <span>系統選單</span>
          <i class="fas fa-chevron-down expand-icon" :class="{ 'rotated': openSubmenu === 'system' }"></i>
        </div>
        
        <div class="submenu" v-show="openSubmenu === 'system'">
          <router-link to="/system/employee" class="submenu-item" :class="{ active: currentRoute === '/system/employee' }">
            <i class="fas fa-user-tie"></i>
            <span>人員管理</span>
          </router-link>
        </div>
        
        <div class="menu-item" :class="{ active: currentRoute.startsWith('/inventory') }" @click="toggleSubmenu('inventory')">
          <i class="fas fa-warehouse"></i>
          <span>庫存</span>
          <i class="fas fa-chevron-down expand-icon" :class="{ 'rotated': openSubmenu === 'inventory' }"></i>
        </div>
        
        <div class="submenu" v-show="openSubmenu === 'inventory'">
          <router-link to="/inventory/materials" class="submenu-item" :class="{ active: currentRoute === '/inventory/materials' }">
            <i class="fas fa-boxes"></i>
            <span>原物料庫存</span>
          </router-link>
        </div>
      </nav>
    </aside>

    <!-- 主要內容區域 -->
    <main class="main-content">
      <router-view></router-view>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const user = ref(null);

const currentRoute = computed(() => route.path);

onMounted(() => {
  // 從本地存儲獲取用戶資訊
  const userStr = localStorage.getItem('user');
  if (userStr) {
    try {
      user.value = JSON.parse(userStr);
    } catch (error) {
      console.error('解析用戶資訊出錯:', error);
    }
  }
  
  // 檢查是否已登入
  const token = localStorage.getItem('token');
  if (!token) {
    router.push('/login');
  }
});

const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  router.push('/login');
};

const openSubmenu = ref(null);

const toggleSubmenu = (item) => {
  openSubmenu.value = openSubmenu.value === item ? null : item;
};

const departmentName = (department) => {
  if (department === 0) {
    return '台北部';
  } else if (department === 1) {
    return '廠務部';
  } else {
    return '未知部門';
  }
};

// 新增：根據 role 代碼獲取權限名稱
const getRoleName = (role) => {
  if (role === 0) {
    return '管理員';
  } else if (role === 1) {
    return '組員';
  } else {
    return '-'; // 如果沒有對應的 role 或 role 為空，顯示 '-'
  }
};
</script>

<style scoped>
.layout {
  display: grid;
  grid-template-areas:
    "sidebar header"
    "sidebar main";
  grid-template-columns: 250px 1fr;
  grid-template-rows: 60px 1fr;
  min-height: 100vh;
}

.header {
  grid-area: header;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #333;
}

.logout-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px;
  font-size: 14px;
  transition: color 0.3s;
}

.logout-btn:hover {
  color: #f44336;
}

.sidebar {
  grid-area: sidebar;
  background-color: #0c0c20;
  color: white;
  padding: 20px 0;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
  padding: 0 20px;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 40px;
  height: 40px;
}

.system-name {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.menu {
  display: flex;
  flex-direction: column;
}

.menu-item {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
}

.menu-item i:first-child {
  width: 20px;
  text-align: center;
}

.expand-icon {
  position: absolute;
  right: 15px;
  transition: transform 0.3s;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.submenu {
  background-color: rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.submenu-item {
  padding: 12px 20px 12px 50px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s;
}

.submenu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.submenu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #4caf50;
}

.submenu-item i {
  width: 16px;
  text-align: center;
  font-size: 14px;
}

.main-content {
  grid-area: main;
  background-color: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
}
</style>
