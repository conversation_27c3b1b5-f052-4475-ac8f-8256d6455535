import { createRouter, createWebHistory } from 'vue-router';
import LoginView from '../views/LoginView.vue';
import MainLayout from '../layouts/MainLayout.vue';
import EmployeeView from '../views/management/EmployeeView.vue'
import AnnouncementView from '../views/management/AnnouncementView.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('../views/DashboardView.vue')
      },
      {
        path: 'report',
        name: 'report',
        component: () => import('../views/ReportView.vue')
      },
      {
        path: 'design',
        name: 'design',
        component: () => import('../views/DesignView.vue'),
        children: [
          {
            path: 'bom',
            name: 'design-bom',
            component: () => import('../views/design/BomView.vue')
          },
          {
            path: 'order',
            name: 'design-order',
            component: () => import('../views/design/OrderView.vue')
          },
          {
            path: 'change',
            name: 'design-change',
            component: () => import('../views/design/ChangeView.vue')
          },
          {
            path: 'process',
            name: 'design-process',
            component: () => import('../views/design/ProcessView.vue')
          },
          {
            path: 'process-preparation/:bomId',
            name: 'ProcessPreparation',
            component: () => import('../views/design/ProcessPreparationView.vue'),
            props: true
          },
          {
            path: 'material-usage/:bomId',
            name: 'MaterialUsage',
            component: () => import('../views/design/MaterialUsageView.vue'),
            props: true
          },
          {
            path: '',
            redirect: 'bom'
          }
        ]
      },
      {
        path: 'management',
        name: 'management',
        component: () => import('../views/ManagementView.vue'),
        children: [
          {
            path: 'announcement',
            name: 'management-announcement',
            component: AnnouncementView
          }
        ]
      },
      {
        path: 'system',
        name: 'system',
        component: () => import('../views/SystemView.vue'),
        children: [
          {
            path: 'employee',
            name: 'system-employee',
            component: EmployeeView
          }
        ]
      },
      {
        path: 'inventory',
        name: 'inventory',
        component: () => import('../views/InventoryView.vue'),
        children: [
          {
            path: 'materials',
            name: 'inventory-materials',
            component: () => import('../views/inventory/MaterialsView.vue')
          },
          {
            path: 'leather/:id',
            name: 'inventory-leather-detail',
            component: () => import('../views/inventory/LeatherDetailView.vue'),
            props: true
          },
          {
            path: '',
            redirect: 'materials'
          }
        ]
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 導航守衛
router.beforeEach((to, from, next) => {
  const isAuthenticated = !!localStorage.getItem('token');

  if (to.meta.requiresAuth && !isAuthenticated) {
    // 需要登入但未登入，重定向到登入頁面
    next('/login');
  } else if (to.path === '/login' && isAuthenticated) {
    // 已登入但試圖訪問登入頁面，重定向到儀表板
    next('/dashboard');
  } else {
    // 其他情況正常繼續
    next();
  }
});

export default router;
