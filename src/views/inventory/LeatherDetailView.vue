<template>
  <div class="leather-detail-container">
    <div class="leather-detail-header">
      <h2>皮料詳情</h2>
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i> 返回
      </button>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <span>載入中...</span>
      </div>
    </div>

    <div v-else-if="!material" class="not-found-container">
      <div class="not-found-message">
        <i class="fas fa-exclamation-triangle"></i>
        <span>找不到指定的皮料</span>
      </div>
    </div>

    <div v-else class="leather-detail-content">
      <div class="material-info-card">
        <div class="material-header">
          <h3>基本信息</h3>
        </div>
        <div class="material-info">
          <div class="info-row">
            <div class="info-label">材料編號:</div>
            <div class="info-value">{{ material.code }}</div>
          </div>

          <div class="info-row">
            <div class="info-label">材料名稱:</div>
            <div class="info-value">{{ material.name }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">規格:</div>
            <div class="info-value">{{ material.specification || '無' }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">單位:</div>
            <div class="info-value">m²</div>
          </div>
          <div class="info-row">
            <div class="info-label">顏色:</div>
            <div class="info-value">{{ material.color || '無' }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">分類:</div>
            <div class="info-value">{{ material.category || '無' }}</div>
          </div>
          <div class="info-row">
            <div class="info-label">總面積:</div>
            <div class="info-value">{{ formatNumber(material.leather_total_area || 0) }} m²</div>
          </div>
        </div>
      </div>

      <div class="leather-details-card">
        <div class="details-header">
          <h3>皮料詳情</h3>
          <button class="add-btn" @click="openAddDetailModal">
            <i class="fas fa-plus"></i> 新增詳情
          </button>
        </div>

        <div class="details-table-container">
          <table class="details-table">
            <thead>
              <tr>
                <th>皮料編碼</th>
                <th>倉庫位置</th>
                <th>面積 (m²)</th>
                <th>數量 (張)</th>
                <th>創建時間</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="details.length === 0">
                <td colspan="6" class="no-data">暫無詳情數據</td>
              </tr>
              <tr v-for="detail in details" :key="detail.id">
                <td>{{ detail.leather_code || '無' }}</td>
                <td>{{ detail.warehouse }}</td>
                <td>{{ formatNumber(detail.area) }}</td>
                <td>{{ formatNumber(detail.quantity) }}</td>
                <td>{{ formatDate(detail.createdAt) }}</td>
                <td>
                  <div class="actions">
                    <button class="action-btn edit" @click="openEditDetailModal(detail)">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" @click="confirmDeleteDetail(detail)">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="warehouse-summary-card">
        <div class="summary-header">
          <h3>倉庫分布</h3>
        </div>
        <div class="summary-content">
          <div class="summary-row">
            <div class="summary-label">台北部:</div>
            <div class="summary-value">{{ formatNumber(material.taipei_area || 0) }} m²</div>
          </div>
          <div class="summary-row">
            <div class="summary-label">廠務部A:</div>
            <div class="summary-value">{{ formatNumber(material.factory_a_area || 0) }} m²</div>
          </div>
          <div class="summary-row">
            <div class="summary-label">廠務部B:</div>
            <div class="summary-value">{{ formatNumber(material.factory_b_area || 0) }} m²</div>
          </div>
          <div class="summary-row">
            <div class="summary-label">廠務部C:</div>
            <div class="summary-value">{{ formatNumber(material.factory_c_area || 0) }} m²</div>
          </div>
          <div class="summary-row">
            <div class="summary-label">廠務部D:</div>
            <div class="summary-value">{{ formatNumber(material.factory_d_area || 0) }} m²</div>
          </div>
          <div class="summary-row">
            <div class="summary-label">廠務部E:</div>
            <div class="summary-value">{{ formatNumber(material.factory_e_area || 0) }} m²</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/編輯詳情彈窗 -->
    <div class="modal" v-if="detailModalVisible">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ isEditMode ? '編輯詳情' : '新增詳情' }}</h3>
          <button class="close-btn" @click="closeDetailModal">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitDetailForm">
            <div class="form-group">
              <label>倉庫位置</label>
              <select v-model="detailForm.warehouse">
                <option value="台北部">台北部</option>
                <option value="廠務部A">廠務部A</option>
                <option value="廠務部B">廠務部B</option>
                <option value="廠務部C">廠務部C</option>
                <option value="廠務部D">廠務部D</option>
                <option value="廠務部E">廠務部E</option>
                <option value="總倉庫">總倉庫</option>
              </select>
            </div>
            <div class="form-group">
              <label>面積 (m²)</label>
              <input type="number" v-model.number="detailForm.area" min="0" step="0.01">
            </div>
            <div class="form-group">
              <label>數量 (張)</label>
              <input type="number" v-model.number="detailForm.quantity" min="1" step="1">
            </div>
            <div class="form-buttons">
              <button type="button" class="cancel-btn" @click="closeDetailModal">取消</button>
              <button type="submit" class="submit-btn">提交</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 確認刪除彈窗 -->
    <div class="modal" v-if="confirmDeleteVisible">
      <div class="modal-content">
        <div class="modal-header">
          <h3>確認刪除</h3>
          <button class="close-btn" @click="cancelDelete">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>確定要刪除此皮料詳情嗎？此操作無法撤銷。</p>
          <div class="form-buttons">
            <button type="button" class="cancel-btn" @click="cancelDelete">取消</button>
            <button type="button" class="delete-btn" @click="deleteDetail">確認刪除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import api from '@/services/api';
import { useNotification } from '@/services/notificationService';

const route = useRoute();
const router = useRouter();
const notification = useNotification();

// 響應式變量
const material = ref(null);
const details = ref([]);
const loading = ref(true);

// 詳情表單相關
const detailModalVisible = ref(false);
const isEditMode = ref(false);
const detailForm = ref({
  id: null,
  material_id: '',
  warehouse: '廠務部A',
  area: 0,
  quantity: 0
});

// 刪除相關
const confirmDeleteVisible = ref(false);
const detailToDelete = ref(null);

// 獲取皮料詳情
const fetchLeatherDetail = async () => {
  try {
    loading.value = true;
    const materialId = route.params.id;

    if (!materialId) {
      notification.error('缺少必要參數');
      return;
    }

    // 先獲取原物料基本信息
    const materialResponse = await api.materials.getById(materialId);
    if (materialResponse) {
      material.value = materialResponse;
    }

    // 調用API獲取皮料詳情
    const response = await api.materials.leatherMaterials.getAll({ material_id: materialId });

    if (response && response.data) {
      // 直接使用返回的數據陣列
      details.value = response.data;

      // 計算各倉庫的面積
      const warehouseSummary = {
        taipei_area: 0,
        factory_a_area: 0,
        factory_b_area: 0,
        factory_c_area: 0,
        factory_d_area: 0,
        factory_e_area: 0
      };

      // 遍歷所有皮料詳情，計算各倉庫的面積
      details.value.forEach(detail => {
        const area = Number(detail.area) || 0;
        const quantity = Number(detail.quantity) || 0;
        const totalArea = area * quantity;

        // 根據倉庫位置累加面積
        switch (detail.warehouse) {
          case '台北部':
            warehouseSummary.taipei_area += totalArea;
            break;
          case '廠務部A':
            warehouseSummary.factory_a_area += totalArea;
            break;
          case '廠務部B':
            warehouseSummary.factory_b_area += totalArea;
            break;
          case '廠務部C':
            warehouseSummary.factory_c_area += totalArea;
            break;
          case '廠務部D':
            warehouseSummary.factory_d_area += totalArea;
            break;
          case '廠務部E':
            warehouseSummary.factory_e_area += totalArea;
            break;
        }
      });

      // 將計算結果添加到 material 對象中
      material.value = {
        ...material.value,
        ...warehouseSummary
      };
    } else {
      details.value = [];
    }
  } catch (error) {
    console.error('獲取皮料詳情失敗:', error);
    notification.error('獲取皮料詳情失敗');
  } finally {
    loading.value = false;
  }
};

// 返回上一頁
const goBack = () => {
  router.back();
};

// 格式化數字
const formatNumber = (value) => {
  const num = Number(value);
  if (isNaN(num)) {
    return '0.00';
  }
  return num.toFixed(2);
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 打開新增詳情彈窗
const openAddDetailModal = () => {
  isEditMode.value = false;
  detailForm.value = {
    id: null,
    material_id: material.value.id,
    warehouse: '廠務部A',
    area: 0,
    quantity: 0
  };
  detailModalVisible.value = true;
};

// 打開編輯詳情彈窗
const openEditDetailModal = (detail) => {
  isEditMode.value = true;
  detailForm.value = {
    id: detail.id,
    material_id: detail.material_id,
    warehouse: detail.warehouse,
    area: detail.area,
    quantity: detail.quantity
  };
  detailModalVisible.value = true;
};

// 關閉詳情彈窗
const closeDetailModal = () => {
  detailModalVisible.value = false;
};

// 提交詳情表單
const submitDetailForm = async () => {
  try {
    if (isEditMode.value) {
      // 編輯模式
      await api.materials.leatherMaterials.update(detailForm.value.id, detailForm.value);
      notification.success('更新皮料詳情成功');
    } else {
      // 新增模式
      await api.materials.leatherMaterials.create(detailForm.value);
      notification.success('新增皮料詳情成功');
    }

    // 重新獲取詳情
    await fetchLeatherDetail();
    closeDetailModal();
  } catch (error) {
    console.error('提交皮料詳情失敗:', error);
    notification.error('提交皮料詳情失敗');
  }
};

// 確認刪除詳情
const confirmDeleteDetail = (detail) => {
  detailToDelete.value = detail;
  confirmDeleteVisible.value = true;
};

// 取消刪除
const cancelDelete = () => {
  detailToDelete.value = null;
  confirmDeleteVisible.value = false;
};

// 刪除詳情
const deleteDetail = async () => {
  try {
    if (!detailToDelete.value) return;

    await api.materials.leatherMaterials.delete(detailToDelete.value.id);
    notification.success('刪除皮料詳情成功');

    // 重新獲取詳情
    await fetchLeatherDetail();
    cancelDelete();
  } catch (error) {
    console.error('刪除皮料詳情失敗:', error);
    notification.error('刪除皮料詳情失敗');
  }
};

// 組件掛載時獲取數據
onMounted(() => {
  fetchLeatherDetail();
});
</script>

<style scoped>
.leather-detail-container {
  padding: 20px;
}

.leather-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.leather-detail-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.back-btn:hover {
  background-color: #e0e0e0;
}

.loading-container,
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner,
.not-found-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #666;
}

.loading-spinner i,
.not-found-message i {
  font-size: 2rem;
}

.leather-detail-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

.material-info-card,
.leather-details-card,
.warehouse-summary-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.material-info-card,
.warehouse-summary-card {
  grid-column: 1;
}

.leather-details-card {
  grid-column: 2;
  grid-row: 1 / span 2;
}

.material-header,
.details-header,
.summary-header {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.material-header h3,
.details-header h3,
.summary-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.material-info,
.summary-content {
  padding: 16px;
}

.info-row,
.summary-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label,
.summary-label {
  width: 120px;
  font-weight: 500;
  color: #666;
}

.info-value,
.summary-value {
  flex: 1;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-btn:hover {
  background-color: #388e3c;
}

.details-table-container {
  padding: 16px;
  overflow-x: auto;
}

.details-table {
  width: 100%;
  border-collapse: collapse;
}

.details-table th,
.details-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.details-table th {
  background-color: #f9f9f9;
  font-weight: 500;
  color: #333;
}

.details-table tr:hover {
  background-color: #f5f5f5;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn.edit {
  color: #2196f3;
}

.action-btn.delete {
  color: #f44336;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 彈窗樣式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  background-color: #f5f5f5;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn,
.delete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.submit-btn {
  background-color: #4caf50;
  color: white;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.submit-btn:hover {
  background-color: #388e3c;
}

.delete-btn:hover {
  background-color: #d32f2f;
}
</style>
