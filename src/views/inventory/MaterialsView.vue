<template>
  <div class="materials-container">

    <div class="materials-header">
      <h2>原物料庫存</h2>
      <div class="materials-actions">
        <button class="action-btn primary-btn" @click="openMaterialForm()">
          <i class="fas fa-plus-circle"></i>
          原物料入庫
        </button>
        <button class="action-btn secondary-btn" @click="openUsageForm()">
          <i class="fas fa-file-import"></i>
          新增領用單
        </button>
        <button class="action-btn tertiary-btn" @click="openTransferForm()">
          <i class="fas fa-exchange-alt"></i>
          調撥
        </button>
        <button class="action-btn info-btn" @click="openUsageRecords()">
          <i class="fas fa-list-alt"></i>
          查看歷史紀錄
        </button>
      </div>
    </div>

    <div class="search-area">
      <div class="search-input">
        <input type="text" placeholder="材料編號或名稱搜尋" v-model="searchQuery">
        <!-- Removed search hint and keyup.enter -->
      </div>
    </div>

    <div class="materials-table-container">
      <table class="materials-table">
        <thead>
          <tr>
            <th style="width: 40px;">
              <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
            </th>
            <th>圖片</th>
            <th>分類</th>
            <th>材料編號</th>
            <th>材料名稱</th>
            <th>規格</th>
            <th>單位</th>
            <th>顏色</th>
            <th>庫存數量</th>
            <th>可用數量</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="loading">
            <td colspan="12" class="loading-data">
              <div class="loading-content">
                <i class="fas fa-spinner fa-spin"></i>
                <span>載入中...</span>
              </div>
            </td>
          </tr>
          <tr v-else-if="materials.length === 0">
            <td colspan="12" class="no-data">
              <div class="no-data-content">
                <img src="@/assets/no-data.svg" alt="No data" class="no-data-icon">
                <span>No data</span>
              </div>
            </td>
          </tr>
          <tr v-for="(group, index) in groupedMaterials" :key="index">
            <td>
              <input type="checkbox" v-model="selectedItems" :value="group.items[0].id">
            </td>
            <td class="type-cell">
              <div class="type-icon-container">
                <img v-if="group.image_url" :src="getImageUrl(group.image_url)" :alt="group.type" class="type-icon"
                  @error="handleImageError($event, group)"
                  @mouseenter="showZoomedImage($event, group.image_url)"
                  @mouseleave="hideZoomedImage()" />
                <div v-else class="type-placeholder">{{ group.type ? group.type.charAt(0) : '?' }}</div>
                <!-- 移除 type-tooltip -->
                <!-- 圖片放大區域 -->
                <div class="image-zoom-container" v-if="zoomedImage && currentZoomImageId === group.code">
                  <img :src="zoomedImage" alt="放大圖片" class="zoomed-image" />
                </div>
              </div>
            </td>
            <td>{{ group.category }}</td>
            <td>
              <span v-if="group.category === '皮料'" class="clickable-code" @click="viewLeatherDetail(group.items[0].id)" :title="'點擊查看皮料詳情: ' + group.code">
                {{ group.code }}
                <i class="fas fa-external-link-alt"></i>
              </span>
              <span v-else>{{ group.code }}</span>
            </td>
            <td>{{ group.name }}</td>
            <td>{{ group.specification }}</td>
            <td>{{ group.unit }}</td>
            <td>{{ group.color }}</td>
            <td class="quantity-cell">
              <!-- 皮料的庫存數量顯示台北部和廠務部的面積 -->
              <template v-if="group.is_leather">
                <div class="location-row">
                  <span class="location-name">台北部:</span>
                  <span class="quantity-value">{{ formatNumber(group.taipei_stock || 0) }} m²</span>
                </div>
                <div class="location-row">
                  <span class="location-name">廠務部:</span>
                  <span class="quantity-value">{{ formatNumber(group.factory_stock || 0) }} m²</span>
                </div>
              </template>
              <!-- 非皮料的庫存數量顯示台北部和廠務部數量 -->
              <template v-else>
                <div class="location-row"><span class="location-name">台北部:</span> <span class="quantity-value">{{ formatNumber(group.taipei_stock || 0) }}</span></div>
                <div class="location-row"><span class="location-name">廠務部:</span> <span class="quantity-value">{{ formatNumber(group.factory_stock || 0) }}</span></div>
              </template>
            </td>
            <td class="quantity-cell">
              <!-- 皮料的可用數量顯示台北部和廠務部的面積 -->
              <template v-if="group.is_leather">
                <div class="location-row">
                  <span class="location-name">台北部:</span>
                  <span class="quantity-value">{{ formatNumber(group.taipei_available || 0) }} m²</span>
                </div>
                <div class="location-row">
                  <span class="location-name">廠務部:</span>
                  <span class="quantity-value">{{ formatNumber(group.factory_available || 0) }} m²</span>
                </div>
              </template>
              <!-- 非皮料的可用數量顯示台北部和廠務部數量 -->
              <template v-else>
                <div class="location-row"><span class="location-name">台北部:</span> <span class="quantity-value">{{ formatNumber(group.taipei_available || 0) }}</span></div>
                <div class="location-row"><span class="location-name">廠務部:</span> <span class="quantity-value">{{ formatNumber(group.factory_available || 0) }}</span></div>
              </template>
            </td>
            <td>
              <div class="actions">
                <button class="action-icon edit" title="編輯" @click="openMaterialForm(group.items[0])">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="action-icon delete" title="刪除" @click="deleteMaterial(group.items[0])">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 原物料表單彈窗 -->
    <MaterialFormDialog
      :visible="materialFormVisible"
      :material="currentMaterial"
      :isEdit="isEditMode"
      :userDepartment="userDepartment"
      @close="closeMaterialForm"
      @submit-success="handleFormSuccess"
    />

    <!-- 原物料領用彈窗 -->
    <MaterialUsageDialog
      :visible="usageFormVisible"
      :userDepartment="userDepartment"
      @close="closeUsageForm"
      @submit-success="handleFormSuccess"
    />

    <!-- 原物料調撥彈窗 -->
    <MaterialTransferDialog
      :visible="transferFormVisible"
      :userDepartment="userDepartment"
      @close="closeTransferForm"
      @submit-success="handleFormSuccess"
    />

    <!-- 歷史記錄彈窗 -->
    <MaterialUsageRecords
      :visible="usageRecordsVisible"
      @close="closeUsageRecords"
    />

    <!-- 確認刪除的彈窗 -->
    <div class="confirmation-modal" v-if="confirmDeleteVisible">
      <div class="confirmation-content">
        <h3>確認刪除</h3>
        <p>確定要刪除 <strong>{{ materialToDelete?.name || '此項目' }}</strong> 嗎？此操作無法撤銷。</p>
        <div class="confirmation-buttons">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="delete-btn" @click="confirmDelete" :disabled="isDeleting">
            {{ isDeleting ? '刪除中...' : '確認刪除' }}
          </button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { debounce } from 'lodash-es';
import api from '@/services/api';
import { API_BASE_URL } from '@/services/api/config';
import { useNotification } from '@/services/notificationService';
import MaterialFormDialog from '@/components/inventory/MaterialFormDialog.vue';
import MaterialUsageDialog from '@/components/inventory/MaterialUsageDialog.vue';
import MaterialTransferDialog from '@/components/inventory/MaterialTransferDialog.vue';
import MaterialUsageRecords from '@/components/inventory/MaterialUsageRecords.vue';

// 數據和響應式變量
const materials = ref([]);
const searchQuery = ref('');
const categoryFilter = ref('all');
// 已移除倉庫篩選變量
const selectedItems = ref([]);
const selectAll = ref(false);
const loading = ref(false);
const userDepartment = ref(null);

// 圖片放大相關
const zoomedImage = ref(null);
const currentZoomImageId = ref(null);

// 表單相關
const materialFormVisible = ref(false);
const currentMaterial = ref(null);
const isEditMode = ref(false);

// 領用單相關
const usageFormVisible = ref(false);

// 領用記錄相關
const usageRecordsVisible = ref(false);

// 刪除相關
const confirmDeleteVisible = ref(false);
const materialToDelete = ref(null);
const isDeleting = ref(false);

// 調撥彈窗的顯示狀態
const transferFormVisible = ref(false);

// 通知服務
const notification = useNotification();

// 路由
const router = useRouter();

// 獲取圖片URL
const getImageUrl = (url) => {
  if (!url) return '';

  console.log('原始圖片URL:', url);

  // 如果是相對路徑，添加基本 URL
  if (url.startsWith('/database/')) {
    // 將 /database/ 路徑轉換為直接訪問 uploads 目錄
    const newUrl = url.replace('/database/uploads', '/api/database/uploads');
    console.log('轉換後的URL:', newUrl);
    // 如果是相對路徑，添加 API_BASE_URL
    return `${API_BASE_URL}${newUrl}`;
  } else if (url.startsWith('/')) {
    // 其他相對路徑，也添加 API_BASE_URL
    return `${API_BASE_URL}${url}`;
  }

  // 如果是完整URL，直接返回
  return url;
};

// 處理圖片加載錯誤
const handleImageError = (_, group) => {
  console.error('圖片加載失敗:', group.image_url);
  // 將圖片設置為空，使用占位符顯示
  group.image_url = '';
};

// 顯示放大圖片
const showZoomedImage = (_, imageUrl) => {
  if (!imageUrl) return;
  // 設置放大圖片的URL
  zoomedImage.value = getImageUrl(imageUrl);

  // 設置目前放大圖片的ID
  const group = groupedMaterials.value.find(g => getImageUrl(g.image_url) === getImageUrl(imageUrl));
  if (group) {
    currentZoomImageId.value = group.code;
  }
};

// 隱藏放大圖片
const hideZoomedImage = () => {
  zoomedImage.value = null;
  currentZoomImageId.value = null;
};

// 格式化數字到小數點後兩位
const formatNumber = (value) => {
  const num = Number(value);
  if (isNaN(num)) {
    return value; // 如果不是有效數字，返回原值
  }
  // 使用 toFixed(2) 確保始終顯示兩位小數
  return num.toFixed(2);
};

// 查看皮料詳情
const viewLeatherDetail = (materialId) => {
  router.push({ name: 'inventory-leather-detail', params: { id: materialId } });
};

// 將原物料按材料編號分組
const groupedMaterials = computed(() => {
  const groups = {};

  // 先按材料編號分組
  materials.value.forEach(material => {
    const code = material.code || '';
    if (!groups[code]) {
      // 判斷是否為皮料類別
      const isLeather = material.category === '皮料';

      // 如果是皮料，使用皮料詳情的面積和數量
      let taipeiStock = Number(material.taipei_stock_quantity) || 0;
      let factoryStock = Number(material.factory_stock_quantity) || 0;
      let taipeiAvailable = Number(material.taipei_available_quantity) || 0;
      let factoryAvailable = Number(material.factory_available_quantity) || 0;

      // 如果是皮料且有皮料詳情數據
      if (isLeather && material.leather_total_area !== undefined) {
        // 使用皮料詳情的面積作為庫存數量
        // 台北部和廠務部的面積分別計算
        // 預設將所有面積都顯示在廠務部
        taipeiStock = 0; // 台北部預設為 0
        factoryStock = material.leather_total_area || 0; // 廠務部顯示總面積
        taipeiAvailable = 0; // 台北部預設為 0
        factoryAvailable = material.leather_total_area || 0; // 廠務部顯示總面積
      }

      groups[code] = {
        code: material.code,
        name: material.name,
        specification: material.specification,
        unit: isLeather ? 'm²' : material.unit, // 皮料單位統一為平方米
        color: material.color,
        type: material.type,
        category: material.category,
        image_url: material.image_url,
        // 直接使用新的台北部和廠務部數量欄位
        taipei_stock: taipeiStock,
        factory_stock: factoryStock,
        taipei_available: taipeiAvailable,
        factory_available: factoryAvailable,

        // 廠務部A-E的庫存數量
        factory_a_stock: Number(material.factory_a_stock_quantity) || 0,
        factory_b_stock: Number(material.factory_b_stock_quantity) || 0,
        factory_c_stock: Number(material.factory_c_stock_quantity) || 0,
        factory_d_stock: Number(material.factory_d_stock_quantity) || 0,
        factory_e_stock: Number(material.factory_e_stock_quantity) || 0,

        // 廠務部A-E的可用數量
        factory_a_available: Number(material.factory_a_available_quantity) || 0,
        factory_b_available: Number(material.factory_b_available_quantity) || 0,
        factory_c_available: Number(material.factory_c_available_quantity) || 0,
        factory_d_available: Number(material.factory_d_available_quantity) || 0,
        factory_e_available: Number(material.factory_e_available_quantity) || 0,

        // 皮料相關信息
        is_leather: isLeather,
        leather_total_area: material.leather_total_area,
        leather_total_quantity: material.leather_total_quantity,

        items: [material] // 直接將原物料加入項目列表
      };
    }
  });

  // 轉換為數組並按材料編號排序
  return Object.values(groups).sort((a, b) => {
    if (a.code < b.code) return -1;
    if (a.code > b.code) return 1;
    return 0;
  });
});

// 獲取原物料數據
const fetchMaterials = async () => {
  try {
    loading.value = true;

    const response = await api.materials.getAll({
      search: searchQuery.value,
      category: categoryFilter.value !== 'all' ? categoryFilter.value : undefined
    });

    // 排序處理
    if (response && Array.isArray(response)) {
      // 排序結果
      materials.value = response.sort((a, b) => {
        // 先按代號排序
        if (a.code && b.code) {
          if (a.code < b.code) return -1;
          if (a.code > b.code) return 1;
        }

        // 若代號相同或為空，則按創建時間降序排序（最新的在上方）
        return new Date(b.createdAt) - new Date(a.createdAt);
      });
    } else {
      materials.value = [];
    }
  } catch (error) {
    console.error('獲取原物料數據失敗:', error);
    notification.error('獲取原物料數據失敗');
  } finally {
    loading.value = false;
  }
};



// 全選/取消全選
const toggleSelectAll = () => {
  if (selectAll.value) {
    // 從分組材料中取出第一個項目的 ID
    selectedItems.value = groupedMaterials.value.map(group => group.items[0].id);
  } else {
    selectedItems.value = [];
  }
};

// 獲取當前用戶資訊
const fetchCurrentUser = async () => {
  try {
    const response = await api.getCurrentUser();
    if (response && response.status === 'success' && response.data && response.data.employee) {
      userDepartment.value = response.data.employee.department;
    }
  } catch (error) {
    console.error('獲取用戶資訊失敗:', error);
  }
};

// 打開表單
const openMaterialForm = (material = null) => {
  if (material) {
    // 編輯模式
    currentMaterial.value = { ...material };
    isEditMode.value = true;
  } else {
    // 新增模式
    currentMaterial.value = null;
    isEditMode.value = false;
  }
  materialFormVisible.value = true;
};

// 關閉表單
const closeMaterialForm = () => {
  materialFormVisible.value = false;
  currentMaterial.value = null;
};

// 處理表單提交成功
const handleFormSuccess = () => {
  fetchMaterials();
};

// 刪除原物料
const deleteMaterial = (material) => {
  materialToDelete.value = material;
  confirmDeleteVisible.value = true;
};

// 取消刪除
const cancelDelete = () => {
  confirmDeleteVisible.value = false;
  materialToDelete.value = null;
};

// 確認刪除
const confirmDelete = async () => {
  if (!materialToDelete.value) return;

  try {
    isDeleting.value = true;
    await api.materials.delete(materialToDelete.value.id);

    notification.success('原物料已成功刪除');
    fetchMaterials();
    cancelDelete();
  } catch (error) {
    console.error('刪除原物料失敗:', error);
    notification.error('刪除原物料失敗');
  } finally {
    isDeleting.value = false;
  }
};

// 打開領用單表單
const openUsageForm = () => {
  usageFormVisible.value = true;
};

// 關閉領用單表單
const closeUsageForm = () => {
  usageFormVisible.value = false;
};

// 打開調撥彈窗
const openTransferForm = () => {
  transferFormVisible.value = true;
};

// 關閉調撥彈窗
const closeTransferForm = () => {
  transferFormVisible.value = false;
};

// 打開歷史記錄
const openUsageRecords = () => {
  usageRecordsVisible.value = true;
};

// 關閉歷史記錄
const closeUsageRecords = () => {
  usageRecordsVisible.value = false;
};

// 組件掛載時獲取數據
onMounted(async () => {
  await fetchCurrentUser();
  await fetchMaterials();

  // Watch for changes in searchQuery and debounce the fetchMaterials call
  watch(
    () => searchQuery.value,
    debounce(async () => {
      await fetchMaterials();
    }, 300)
  );

  // 已移除倉庫篩選變化的監聽
});
</script>

<style scoped>
.materials-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.materials-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.materials-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.materials-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.primary-btn {
  background-color: #2196F3;
  color: white;
}

.primary-btn:hover {
  background-color: #1976D2;
}

.secondary-btn {
  background-color: #4CAF50;
  color: white;
}

.secondary-btn:hover {
  background-color: #388E3C;
}

.tertiary-btn {
  background-color: #9C27B0;
  color: white;
}

.tertiary-btn:hover {
  background-color: #7B1FA2;
}

.info-btn {
  background-color: #FF9800;
  color: white;
}

.info-btn:hover {
  background-color: #F57C00;
}

.search-area {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.search-input {
  position: relative;
  flex: 1;
}

.search-input input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Removed search-hint style */

/* 已移除倉庫篩選相關樣式 */

.materials-table-container {
  overflow-x: auto;
}

.materials-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #eee;
}

.materials-table th,
.materials-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  text-align: left;
}



.materials-table td div {
  margin-bottom: 4px;
}

.materials-table td div:last-child {
  margin-bottom: 0;
}

.quantity-cell {
  min-width: 150px;
}

.location-row {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.location-row:last-child {
  margin-bottom: 0;
}

.location-name {
  font-weight: normal;
}

.quantity-value {
  font-weight: bold;
}

.clickable-code {
  color: #2196f3;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.clickable-code:hover {
  text-decoration: underline;
}

.clickable-code i {
  font-size: 12px;
}

.materials-table th {
  background-color: #f7f7f7;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  vertical-align: middle;
}

.materials-table tr:hover {
  background-color: #f9f9f9;
}

.actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: color 0.3s;
}

.action-icon.edit:hover {
  color: #2196F3;
}

.action-icon.delete:hover {
  color: #F44336;
}

.no-data, .loading-data {
  text-align: center;
  padding: 40px 0;
}

.no-data-content, .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #999;
}

.no-data-icon {
  width: 60px;
  height: 60px;
  opacity: 0.5;
}

.loading-content i {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 確認刪除彈窗 */
.confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1010;
}

.confirmation-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 400px;
  padding: 20px;
}

.confirmation-content h3 {
  margin-top: 0;
  color: #333;
}

.confirmation-content p {
  margin-bottom: 20px;
  color: #666;
}

.confirmation-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.confirmation-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.delete-btn {
  background-color: #F44336;
  color: white;
}

.delete-btn:disabled {
  background-color: #e57373;
  cursor: not-allowed;
}

/* 類型圖示相關樣式 */
.type-cell {
  position: relative;
  text-align: center;
}

.type-icon-container {
  display: inline-block;
  position: relative;
  cursor: pointer;
}

.type-icon {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e0e0e0;
  transition: transform 0.2s, box-shadow 0.2s;
}

.type-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.type-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  border: 1px solid #e0e0e0;
}

/* 移除 tooltip 相關樣式 */

/* 圖片放大區域樣式 */
.image-zoom-container {
  position: absolute;
  bottom: 0; /* 與原圖底部對齊 */
  left: 60px; /* 在原圖右側顯示 */
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 10px;
  width: 200px;
  height: auto;
  overflow: hidden;
}

.zoomed-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 4px;
}
</style>
