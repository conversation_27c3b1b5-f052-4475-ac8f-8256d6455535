<template>
  <div class="employee-container">
    <div class="employee-header">
      <h2>人員管理</h2>
      <div class="employee-actions">
        <div class="filter-container">
          <button class="add-btn" @click="openEmployeeForm()">
          <i class="fas fa-plus"></i>
          新增人員
        </button>
          <label>選擇部門</label>
          <select v-model="selectedDepartment" class="department-select">
            <option value="全部">全部</option>
            <option :value="0">台北部</option>
            <option :value="1">廠務部</option>
          </select>
        </div>
        <div class="search-box">
          <span>搜尋 (輸入完請按Enter)</span>
          <input
            type="text"
            class="search-input"
            placeholder="請輸入姓名或工號"
            v-model="searchQuery"
            @keyup.enter="handleSearch"
          />
        </div>

      </div>
    </div>

    <div class="employee-table-container">
      <table class="employee-table">
        <thead>
          <tr>
            <th>部門</th>
            <th>職稱</th>
            <th>工號</th>
            <th>姓名</th>
            <th>權限</th>
            <th>備註</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="employee in filteredEmployees" :key="employee.id">
            <td>{{ departmentName(employee.department) }}</td>
            <td>{{ employee.position }}</td>
            <td>{{ employee.employeeId }}</td>
            <td>{{ employee.name }}</td>
            <td>{{ employeeRole(employee.role) }}</td>
            <td>{{ employee.notes }}</td>
            <td>
              <button class="edit-btn" @click="openEmployeeForm(employee)">編輯</button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 載入指示器 -->
      <div v-if="loading" class="loading-indicator">
        <i class="fas fa-spinner fa-spin"></i> 載入中...
      </div>

      <!-- 空狀態 -->
      <div v-if="!loading && filteredEmployees.length === 0" class="empty-state">
        暫無員工資料，請點擊「新增人員」添加
      </div>
    </div>

    <!-- 員工表單彈出窗口 -->
    <div class="employee-form-overlay" v-if="showEmployeeForm">
      <div class="employee-form-container">
        <div class="employee-form-header">
          <h3>{{ isEditMode ? '編輯人員' : '新增人員' }}</h3>
          <button class="close-btn" @click="closeEmployeeForm">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="employee-form-content">
          <div class="form-row">
            <div class="form-group">
              <label>部門 <span class="required">*</span></label>
              <select v-model.number="formData.department" class="form-select" required>
                <option :value="null" disabled>請選擇部門</option>
                <option :value="0">台北部</option>
                <option :value="1">廠務部</option>
              </select>
            </div>
            <div class="form-group">
              <label>權限</label>
              <select v-model.number="formData.role" class="form-select">
                <option :value="0">管理員</option>
                <option :value="1">組員</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>職稱</label>
              <input type="text" v-model="formData.position" placeholder="請輸入職稱" />
            </div>
            <div class="form-group">
              <label>等級</label>
              <select v-model="formData.level" class="form-select">
                <option value="A">A</option>
                <option value="B">B</option>
                <option value="C">C</option>
                <option value="D">D</option>
                <option value="E">E</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>工號 <span class="required">*</span></label>
              <input type="text" v-model="formData.employeeId" placeholder="請輸入工號" required />
            </div>
            <div class="form-group">
              <label>姓名 <span class="required">*</span></label>
              <input type="text" v-model="formData.name" placeholder="請輸入姓名" required />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Email</label>
              <input type="email" v-model="formData.email" placeholder="請輸入Email" />
            </div>
            <div class="form-group">
              <label>手機</label>
              <input type="tel" v-model="formData.phone" placeholder="請輸入手機號碼" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>帳號 <span class="required">*</span></label>
              <input type="text" v-model="formData.username" placeholder="請輸入帳號" required />
            </div>
            <div class="form-group">
              <label>密碼 <span class="required">*</span></label>
              <div class="password-input-container">
                <input :type="showPassword ? 'text' : 'password'" v-model="formData.password" placeholder="請輸入密碼" required :disabled="isEditMode && !changePassword" />
                <button type="button" class="toggle-password-btn" @click="togglePassword">
                  <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                </button>
              </div>
              <div v-if="isEditMode" class="change-password-checkbox">
                <input type="checkbox" id="changePassword" v-model="changePassword" />
                <label for="changePassword">修改密碼</label>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>備註</label>
            <textarea v-model="formData.notes" placeholder="請輸入備註"></textarea>
          </div>

          <div class="form-actions">
            <button class="save-btn" @click="saveEmployee">保存</button>
            <button v-if="isEditMode" class="delete-btn" @click="confirmDeleteEmployee">刪除</button>
            <button class="cancel-btn" @click="closeEmployeeForm">取消</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import api from '../../services/api';
import { showError, showSuccess, showWarning, showConfirm } from '../../services/notificationService';

const employeeRole = (role) => {
  if (role === 0) {
    return '管理員';
  } else if (role === 1) {
    return '組員';
  } else {
    return '未知';
  }
};

const departmentName = (department) => {
  // 確保部門值轉換為數字
  const deptNumber = Number(department);

  if (deptNumber === 0) {
    return '台北部';
  } else if (deptNumber === 1) {
    return '廠務部';
  } else {
    return '未知部門';
  }
};

const employees = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedDepartment = ref('全部');
const showEmployeeForm = ref(false);
const isEditMode = ref(false);
const showPassword = ref(false);
const changePassword = ref(false);

// 初始化表單數據
const formData = ref({
  department: 1, // 使用數字類型
  role: 1, // 使用數字類型
  position: '',
  level: 'A',
  employeeId: '',
  name: '',
  email: '',
  phone: '',
  username: '',
  password: '',
  notes: ''
});

// 獲取員工列表
const fetchEmployees = async () => {
  try {
    loading.value = true;
    const response = await api.employee.getAll();

    if (response.status === 'success') {
      console.log('獲取的員工數據:', response.data); // 日誌輸出獲取的員工數據
      employees.value = response.data;
    }
  } catch (error) {
    console.error('獲取員工列表錯誤:', error);
    showError('獲取員工列表失敗');
  } finally {
    loading.value = false;
  }
};

// 根據部門和搜索條件過濾員工
const filteredEmployees = computed(() => {
  if (!employees.value) return [];

  let result = [...employees.value];

  // 按部門過濾
  if (selectedDepartment.value !== '全部') {
    // 確保做一個明確的類型轉換
    const deptNumber = Number(selectedDepartment.value);
    result = result.filter(emp => {
      const empDept = Number(emp.department);
      console.log(`比較: 員工部門 ${empDept} (${typeof empDept}) === 篩選部門 ${deptNumber} (${typeof deptNumber}): ${empDept === deptNumber}`);
      return empDept === deptNumber;
    });
  }

  // 按搜索條件過濾
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(emp =>
      (emp.name && emp.name.toLowerCase().includes(query)) ||
      (emp.employeeId && emp.employeeId.toLowerCase().includes(query))
    );
  }

  return result;
});

// 處理搜索
const handleSearch = () => {
  // 在這裡不需要特別處理，因為我們使用的是計算屬性
};

// 打開員工表單
const openEmployeeForm = (employee = null) => {
  resetForm();

  if (employee) {
    console.log('開始編輯員工:', employee);
    console.log('員工部門原始值:', employee.department, typeof employee.department);

    isEditMode.value = true;

    // 確保所有欄位值的格式正確
    formData.value = {
      id: employee.id,
      department: employee.department !== null && employee.department !== undefined ? Number(employee.department) : null,
      role: employee.role !== null && employee.role !== undefined ? Number(employee.role) : 1,
      position: employee.position || '',
      level: employee.level || 'A',
      employeeId: employee.employeeId || '',
      name: employee.name || '',
      email: employee.email || '',
      phone: employee.phone || '',
      username: employee.username || '',
      password: '',
      notes: employee.notes || ''
    };

    console.log('表單數據部門值:', formData.value.department, typeof formData.value.department);

    changePassword.value = false;
  } else {
    isEditMode.value = false;
    changePassword.value = true;
  }

  showEmployeeForm.value = true;
};

// 關閉員工表單
const closeEmployeeForm = () => {
  showEmployeeForm.value = false;
  resetForm();
};

// 重置表單
const resetForm = () => {
  formData.value = {
    department: 1, // 使用數字類型
    role: 1, // 使用數字類型
    position: '',
    level: 'A',
    employeeId: '',
    name: '',
    email: '',
    phone: '',
    username: '',
    password: '',
    notes: ''
  };
  showPassword.value = false;
  changePassword.value = false;
};

// 切換密碼顯示/隱藏
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 保存員工
const saveEmployee = async () => {
  try {
    console.log('表單數據:', formData.value);
    console.log('department 類型:', typeof formData.value.department);
    console.log('department 值:', formData.value.department);
    console.log('role 類型:', typeof formData.value.role);
    console.log('role 值:', formData.value.role);

    // 驗證必填項
    if (formData.value.department === null || formData.value.department === undefined || formData.value.department === '' || Number.isNaN(Number(formData.value.department))) {
      showWarning('請選擇部門');
      return;
    }

    if (!formData.value.employeeId) {
      showWarning('請輸入工號');
      return;
    }

    if (!formData.value.name) {
      showWarning('請輸入姓名');
      return;
    }

    if (!formData.value.username) {
      showWarning('請輸入帳號');
      return;
    }

    if (!isEditMode.value && !formData.value.password) {
      showWarning('請填寫密碼');
      return;
    }

    if (isEditMode.value && changePassword.value && !formData.value.password) {
      showWarning('請填寫新密碼');
      return;
    }

    loading.value = true;

    // 確保數據類型正確
    const saveData = {
      id: formData.value.id,
      department: Number(formData.value.department),
      role: Number(formData.value.role),
      position: formData.value.position,
      level: formData.value.level,
      employeeId: formData.value.employeeId,
      name: formData.value.name,
      email: formData.value.email,
      phone: formData.value.phone,
      username: formData.value.username,
      notes: formData.value.notes
    };

    // 只有在需要密碼時添加
    if (!isEditMode.value || (isEditMode.value && changePassword.value)) {
      saveData.password = formData.value.password;
    }

    console.log('要發送的數據:', saveData);

    let response;
    if (isEditMode.value) {
      response = await api.employee.update(formData.value.id, saveData);
      showSuccess('員工更新成功');
    } else {
      response = await api.employee.create(saveData);
      showSuccess('員工創建成功');
    }

    console.log('API 響應:', response);

    closeEmployeeForm();
    fetchEmployees();
  } catch (error) {
    console.error('保存員工錯誤:', error);
    showError(error.message || '保存員工失敗');
  } finally {
    loading.value = false;
  }
};

// 確認刪除員工
const confirmDeleteEmployee = () => {
  showConfirm(
    `確定要刪除 ${formData.value.name} 嗎？`,
    deleteEmployee,
    null,
    '確認刪除',
    'warning'
  );
};

// 刪除員工
const deleteEmployee = async () => {
  try {
    if (!formData.value.id) return;

    loading.value = true;
    await api.employee.delete(formData.value.id);

    showSuccess('員工刪除成功');
    closeEmployeeForm();
    fetchEmployees();
  } catch (error) {
    console.error('刪除員工錯誤:', error);
    showError(error.message || '刪除員工失敗');
  } finally {
    loading.value = false;
  }
};

// 在組件掛載時獲取員工列表
onMounted(() => {
  fetchEmployees();
});
</script>

<style scoped>
.employee-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.employee-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.employee-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.employee-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-container label {
  font-size: 14px;
  color: #666;
}

.department-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 120px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-box span {
  font-size: 14px;
  color: #666;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.add-btn {
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-btn:hover {
  background-color: #1765cc;
}

.employee-table-container {
  overflow-x: auto;
}

.employee-table {
  width: 100%;
  border-collapse: collapse;
}

.employee-table th,
.employee-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.employee-table th {
  background-color: #f5f7fa;
  color: #666;
  font-weight: 500;
}

.edit-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #1a73e8;
  color: white;
}

.edit-btn:hover {
  background-color: #1765cc;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
  font-style: italic;
}

/* 員工表單樣式 */
.employee-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.employee-form-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.employee-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.employee-form-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.employee-form-content {
  padding: 20px;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
  flex: 1;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #1a73e8;
  outline: none;
}

.form-group textarea {
  height: 120px;
  resize: vertical;
}

.required {
  color: #f44336;
}

.password-input-container {
  position: relative;
}

.toggle-password-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
}

.change-password-checkbox {
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.change-password-checkbox input {
  width: auto;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-btn,
.delete-btn,
.cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.save-btn {
  background-color: #1a73e8;
  color: white;
}

.save-btn:hover {
  background-color: #1765cc;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.delete-btn:hover {
  background-color: #d32f2f;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d5d5d5;
}
</style>
