.process-preparation-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.process-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.process-header h2 {
  flex-grow: 1;
  margin: 0;
  font-size: 20px;
  color: #333;
}

.back-to-bom {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #3b82f6;
  font-weight: 500;
}

.back-to-bom:hover {
  text-decoration: underline;
}

.add-btn, .import-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #3b82f6;
  color: white;
}

.add-btn:hover, .import-btn:hover {
  background-color: #2563eb;
}

.add-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.6;
}

.clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #ef4444;
  color: white;
  margin-right: 10px;
}

.clear-btn:hover {
  background-color: #dc2626;
}

.read-only-notice {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.read-only-notice p {
  color: #92400e;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.read-only-notice p::before {
  content: '\f071'; /* 警告圖標的 Unicode */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 8px;
  color: #f59e0b;
}

/* 流程圖樣式 */
.process-flow-container {
  margin-top: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.process-flow-header {
  display: flex;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.process-column {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-right: 1px dashed #cbd5e1;
}

.material-column {
  flex: 1;
  /* 移除特殊背景色，保持與其他欄一致 */
}

.process-column:last-child {
  border-right: none;
}

.process-column h3 {
  margin: 0;
  font-size: 16px;
  color: #334155;
  text-align: center;
}

.process-column h3 span {
  display: block;
  margin-top: 4px;
}

.process-flow-body {
  position: relative;
  min-height: 600px;
  display: flex;
}

.position-import-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.position-import-hint p {
  margin-bottom: 16px;
  color: #64748b;
  font-size: 16px;
}

.process-flow-content {
  display: flex;
  width: 100%;
  position: relative;
  min-height: 500px;
}

.position-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px 5px;
}

.position-item {
  background-color: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 8px 6px;
  text-align: center;
  cursor: pointer;
  position: relative;
  min-width: 50px;
  max-width: 120px;
  font-size: 0.9em;
  color: #64748b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  margin: 0 auto;
}

.material-item {
  /* 移除特殊樣式，保持與其他項目一致 */
  max-width: 120px;
  text-align: center;
}

.position-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #e2e8f0;
}

.position-item.selected {
  background-color: #cbd5e1;
  border-color: #94a3b8;
  color: #334155;
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.5);
}

/* 工序項目樣式 */
.process-item {
  background-color: #e0f2fe;
  border: 1px solid #7dd3fc;
  border-radius: 4px;
  padding: 12px 6px 8px;
  text-align: center;
  position: relative;
  min-width: 50px;
  max-width: 120px;
  font-size: 0.9em;
  color: #0369a1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  margin: 0 auto;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 工序項目順序號碼 */
.process-item-order {
  position: absolute;
  top: -8px;
  left: -8px;
  background-color: #0b0b0bb9;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 工序項目高亮樣式 */
.process-item.highlighted {
  background-color: #bae6fd;
  border-color: #0284c7;
  box-shadow: 0 0 0 2px rgba(2, 132, 199, 0.4);
  transform: translateY(-2px);
  z-index: 11 !important; /* 高亮時提升層級，但低於連接線的 30 */
  opacity: 1 !important; /* 確保高亮工序項目不透明 */
}

/* 半透明狀態樣式 */
.process-item.faded,
.process-connection-line.faded {
  opacity: 0.5; /* 50% 透明度 */
  transition: opacity 0.3s ease;
}

/* 已完成工序樣式 */
.process-item.completed {
  background-color: #bbf7d0; /* 淡綠色背景 */
  border-color: #4ade80; /* 綠色邊框 */
  color: #166534; /* 深綠色文字 */
}

/* 工序項目合併狀態 */
.process-item.merged {
  background-color: #86efac; /* 更深的綠色背景 */
  border-color: #22c55e; /* 更深的綠色邊框 */
  color: #14532d; /* 更深的綠色文字 */
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.4); /* 綠色陰影 */
}

.process-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #bae6fd;
}

.process-item-name {
  font-weight: 500;
  font-size: 0.9em;
  color: #0369a1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.process-item-code {
  font-size: 0.75em;
  color: #64748b;
  font-weight: normal;
}

/* 連接線樣式 */
.process-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5; /* 預設將連接線放在工序下方 */
}

/* 工序項目樣式 */
.process-item {
  position: relative;
  z-index: 10; /* 讓工序項目預設顯示在連接線上方 */
}

/* 連接線的高亮狀態 - 現在在 JavaScript 中直接設置 */

/* 確保連接線容器有正確的定位 */
.process-flow-content {
  position: relative;
}

.process-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 確保 SVG marker 元素可以正確顯示 z-index */
marker {
  overflow: visible;
}

/* 確保高亮的連接線和箭頭總是顯示在最上層 */
.process-connection-line.highlighted {
  z-index: 30 !important; /* 高亮時顯示在工序項目上方 */
}

/* 確保 SVG 容器中的元素可以正確堆疊 */
.process-connections {
  isolation: isolate;
  overflow: visible !important;
}

/* 確保高亮的連接線和箭頭總是顯示在最上層 */
.process-connection-line path {
  pointer-events: none;
}

/* 確保高亮的連接線和箭頭總是顯示在最上層 */
.process-connection-line.highlighted path {
  stroke: #475569 !important;
  stroke-width: 3 !important;
}

/* 工序模態對話框樣式 */
.process-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.process-modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.process-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.process-modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #334155;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
}

.close-btn:hover {
  color: #334155;
}

.process-modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #334155;
}

.form-group .help-text {
  font-size: 0.85em;
  color: #64748b;
  margin-bottom: 5px;
  font-style: italic;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

/* 序號顯示樣式 */
.sequence-number {
  padding: 8px 12px;
  background-color: #edf2f7;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  color: #4a5568;
  height: 38px;
  display: flex;
  align-items: center;
}

/* 表單列樣式 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* 階段完成按鈕樣式 */
.stage-complete-btn, .stage-incomplete-btn {
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: auto; /* 將按鈕推到左側 */
}

.stage-complete-btn {
  background-color: #fbbf24; /* 黃色背景 */
  color: #7c2d12; /* 深色文字 */
}

.stage-complete-btn:hover {
  background-color: #f59e0b; /* 深黃色背景 */
}

.stage-incomplete-btn {
  background-color: #94a3b8; /* 灰藍色背景 */
  color: #1e293b; /* 深色文字 */
}

.stage-incomplete-btn:hover {
  background-color: #64748b; /* 深灰藍色背景 */
}

.save-btn,
.cancel-btn,
.delete-btn,
.unmerge-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.save-btn {
  background-color: #3b82f6;
  color: white;
}

.save-btn:hover {
  background-color: #2563eb;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.unmerge-btn {
  background-color: #f59e0b;
  color: white;
}

.unmerge-btn:hover {
  background-color: #d97706;
}

.cancel-btn {
  background-color: #e2e8f0;
  color: #334155;
}

.cancel-btn:hover {
  background-color: #cbd5e1;
}
