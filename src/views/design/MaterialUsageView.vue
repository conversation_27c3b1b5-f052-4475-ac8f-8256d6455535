<template>
  <div class="material-usage-overlay" v-if="visible">
    <div class="material-usage-container">
      <div class="material-header">
        <h2>材料用量表</h2>
        <button class="close-btn" @click="closeDialog">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 搜尋原物料區域 -->
      <div class="search-section">
        <h3>搜尋原物料</h3>
        <div class="search-bar">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="輸入關鍵字搜尋原物料"
            @input="searchMaterials"
          />
          <button class="search-btn" @click="fetchMaterials">
            <i class="fas fa-search"></i>
          </button>
        </div>

        <!-- 搜尋結果表格 -->
        <div class="search-results">
          <table>
            <thead>
              <tr>
                <th style="width: 10%">分類</th>
                <th style="width: 8%">圖示</th>
                <th style="width: 20%">材料編號</th>
                <th style="width: 40%">材料名稱</th>
                <th style="width: 8%">單位</th>
                <th style="width: 14%">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="loading">
                <td colspan="6" class="loading-message">載入中...</td>
              </tr>
              <tr v-else-if="materials.length === 0">
                <td colspan="6" class="no-data-message">無符合條件的原物料</td>
              </tr>
              <tr v-for="material in materials" :key="material.id">
                <td>{{ material.category || '-' }}</td>
                <td>
                  <div class="material-image">
                    <img
                      v-if="material.image_url"
                      :src="getImageUrl(material.image_url)"
                      alt="原物料圖片"
                      @mouseover="showZoomedImage(material.image_url)"
                      @mouseout="hideZoomedImage()"
                      @error="handleImageError"
                    />
                    <span v-else>無<br>圖</span>
                  </div>
                </td>
                <td>{{ material.code }}</td>
                <td>{{ material.name }}</td>
                <td>{{ material.unit }}</td>
                <td>
                  <button class="add-btn" @click="selectMaterial(material)">
                    <i class="fas fa-plus"></i> 新增
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 新增材料表單 -->
      <div class="add-material-form" v-if="selectedMaterial">
        <h3>新增材料</h3>
        <div class="material-info">
          <div class="info-item">
            <span class="info-label">分類:</span>
            <span class="info-value">{{ formData.category }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">材料編號:</span>
            <span class="info-value">{{ formData.code }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">材料名稱:</span>
            <span class="info-value">{{ formData.name }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">單位:</span>
            <span class="info-value">{{ formData.unit }}</span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label>項次</label>
            <div class="select-with-setting">
              <div class="custom-select">
                <div class="select-input-container">
                  <input
                    type="text"
                    v-model="formData.itemNumber"
                    placeholder="請輸入或選擇項次"
                    @input="searchItemNumbers"
                    @focus="toggleItemNumberDropdown"
                    @blur="handleItemNumberBlur"
                  />
                  <i class="fas fa-chevron-down select-arrow" @click="toggleItemNumberDropdown"></i>
                </div>
                <div class="select-items" v-if="showItemNumberDropdown && itemNumberOptions.length">
                  <div
                    v-for="(option, index) in itemNumberOptions"
                    :key="index"
                    class="select-item"
                    @mousedown="selectItemNumber(option)"
                  >
                    {{ option }}
                  </div>
                </div>
              </div>
              <button class="setting-btn" type="button" disabled style="visibility: hidden;">
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>

          <!-- 材料類別下拉選單 -->
          <div class="form-group">
            <label>材料類別</label>
            <div class="select-with-setting">
              <div class="custom-select">
                <div class="select-input-container">
                  <input
                    type="text"
                    v-model="formData.materialCategory"
                    placeholder="請選擇材料類別"
                    @click="showMaterialCategoryDropdown = !showMaterialCategoryDropdown"
                    @blur="handleMaterialCategoryBlur"
                  />
                  <i class="fas fa-chevron-down select-arrow" @click="showMaterialCategoryDropdown = !showMaterialCategoryDropdown"></i>
                </div>
                <div class="select-items" v-if="showMaterialCategoryDropdown">
                  <div
                    v-for="(option, index) in materialCategoryOptions"
                    :key="index"
                    class="select-item"
                    @mousedown="selectMaterialCategory(option)"
                  >
                    {{ option }}
                  </div>
                </div>
              </div>
              <button class="setting-btn" type="button" disabled style="visibility: hidden;">
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>
          <div class="form-group">
            <label>使用部位</label>
            <div class="select-with-setting">
              <div class="custom-select">
                <div class="select-input-container">
                  <input
                    type="text"
                    v-model="formData.usagePosition"
                    placeholder="請輸入或選擇使用部位"
                    @input="searchUsagePositions"
                    @focus="searchUsagePositions"
                    @blur="handleUsagePositionBlur"
                  />
                  <i class="fas fa-chevron-down select-arrow" @click="toggleUsagePositionDropdown"></i>
                </div>
                <div class="select-items" v-if="showUsagePositionDropdown && filteredUsagePositions.length">
                  <div
                    v-for="(item, index) in filteredUsagePositions"
                    :key="index"
                    class="select-item"
                    @mousedown="selectUsagePosition(item)"
                  >
                    {{ item }}
                  </div>
                </div>
              </div>
              <button
                class="setting-btn"
                type="button"
                @click="openSettingManager('usagePosition', '使用部位管理')"
                title="管理常用使用部位"
              >
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>
          <div class="form-group">
            <label>用量</label>
            <div class="select-with-setting">
              <input
                type="number"
                v-model.number="formData.quantity"
                min="0"
                step="1"
                placeholder="請輸入用量"
                class="full-width-input"
                @input="formatQuantity"
              />
              <button class="setting-btn" type="button" disabled style="visibility: hidden;">
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>
          <div class="form-group">
            <label>備註</label>
            <div class="select-with-setting">
              <input type="text" v-model="formData.remark" placeholder="請輸入備註" class="full-width-input" />
              <button class="setting-btn" type="button" disabled style="visibility: hidden;">
                <i class="fas fa-cog"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-group form-actions">
            <button class="cancel-form-btn" @click="cancelAddMaterial">取消</button>
            <button class="confirm-form-btn" @click="addMaterial">確認新增</button>
          </div>
        </div>
      </div>

      <!-- 圖片上傳區域 -->
      <div class="image-upload-section">
        <div class="image-upload-header">
          <h3>上傳圖片</h3>
          <div class="image-upload-actions">
            <button class="select-images-btn" @click="triggerUpload">
              <i class="fas fa-images"></i> 選擇圖片
            </button>
            <button class="upload-btn" @click="uploadImages" :disabled="selectedFiles.length === 0 || isUploading">
              <i class="fas fa-upload"></i> 上傳 <span v-if="selectedFiles.length > 0">({{ selectedFiles.length }})</span>
            </button>
            <input
              type="file"
              ref="fileInput"
              @change="handleImageUpload"
              accept="image/*"
              multiple
              style="display: none"
            />
          </div>
        </div>

        <!-- 預覽區域 -->
        <div v-if="selectedFiles.length > 0" class="compact-preview-area">
          <div class="selected-files-info">
            <span>已選擇 {{ selectedFiles.length }} 張圖片</span>
            <button class="clear-all-btn" @click="clearSelectedFiles">清除全部</button>
          </div>
          <div class="compact-preview-grid">
            <div v-for="(file, index) in selectedFiles" :key="index" class="compact-preview-item">
              <img :src="file.preview" alt="圖片預覽" @error="handleImageError" />
              <button type="button" class="remove-preview-btn" @click="removeSelectedFile(index)">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 上傳進度 -->
        <div v-if="isUploading" class="upload-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
          </div>
          <div class="progress-text">{{ uploadProgress }}%</div>
        </div>
      </div>

      <!-- 已上傳圖片區域 -->
      <div v-if="uploadedImages.length > 0" class="uploaded-images-section">
        <div class="uploaded-images-header">
          <h4>已上傳圖片</h4>
          <button class="toggle-images-btn" @click="toggleImagesCollapsed">
            <i :class="isImagesCollapsed ? 'fas fa-chevron-down' : 'fas fa-chevron-up'"></i>
          </button>
        </div>
        <div v-show="!isImagesCollapsed" class="uploaded-images-grid">
          <div v-for="(image, index) in uploadedImages" :key="image.id" class="uploaded-image-item">
            <div class="uploaded-image-preview" @mouseover="showZoomedImage($event, image.url)" @mouseleave="hideZoomedImage">
              <img :src="getImageUrl(image.url)" alt="已上傳圖片" @error="handleImageError" style="display: block; width: 100%; height: 100%; object-fit: contain;" />
              <button type="button" class="delete-image-btn" @click="deleteUploadedImage(index, image.id)">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 已新增材料列表 -->
      <div class="material-list-section">
        <h3>已新增材料列表</h3>
        <div class="material-list">
          <table>
            <thead>
              <tr>
                <th>項次</th>
                <th>類別</th>
                <th>分類</th>
                <th>使用部位</th>
                <th>圖示</th>
                <th>材料編號</th>
                <th>材料名稱</th>
                <th>用量</th>
                <th>單位</th>
                <th>備註</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="materialList.length === 0">
                <td colspan="11" class="no-data-message">尚未新增任何材料</td>
              </tr>
              <tr v-for="(item, index) in materialList" :key="item.id || index">
                <td>{{ item.item_number || '-' }}</td>
                <td
                  @click="openMaterialDetailSheet(item)"
                  :class="{ 'clickable': isItemClickable(item) }"
                  :title="isItemClickable(item) ? '點擊查看產品用料明細表' : ''"
                >
                  {{ item.material_category || '主料' }}
                </td>
                <td>{{ item.material_type || item.category }}</td>
                <td>
                  {{ item.usage_position || '-' }}
                </td>
                <td>
                  <div class="material-image">
                    <img
                      v-if="item.image_url"
                      :src="getImageUrl(item.image_url)"
                      alt="原物料圖片"
                      @mouseover="(e) => showZoomedImage(e, item.image_url)"
                      @mouseout="hideZoomedImage"
                      @error="handleImageError"
                      style="display: block; width: 100%; height: 100%; object-fit: contain;"
                    />
                    <span v-else>無<br>圖</span>
                  </div>
                </td>
                <td>{{ item.code }}</td>
                <td>{{ item.material_name || item.name }}</td>
                <td>{{ shouldShowQuantity(item.quantity) ? formatNumber(item.quantity) : '-' }}</td>
                <td>{{ item.unit }}</td>
                <td>{{ item.remark || '-' }}</td>
                <td>
                  <button class="delete-btn" @click="deleteMaterial(index, item.id)">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="dialog-actions">
        <button class="cancel-btn" @click="closeDialog">關閉</button>
      </div>

      <!-- 圖片放大顯示 -->
      <div class="zoomed-image-container" v-if="zoomedImage" :style="zoomedImageStyle">
        <img
          :src="getImageUrl(zoomedImage)"
          alt="放大圖片"
          @error="handleImageError"
          style="display: block; width: 100%; height: 100%; object-fit: contain;"
        />
      </div>

      <!-- 設定管理器 -->
      <SettingManager
        v-if="settingManagerVisible"
        :visible="settingManagerVisible"
        :setting-type="currentSettingType"
        :title="currentSettingTitle"
        @close="settingManagerVisible = false"
        @updated="handleSettingUpdated"
      />

      <!-- 產品用料明細表 -->
      <MaterialDetailSheet
        v-if="materialDetailSheetVisible"
        :visible="materialDetailSheetVisible"
        :material-item="selectedDetailMaterial"
        :bom-id="props.bomId"
        :sheet-type="materialDetailSheetType"
        @close="closeMaterialDetailSheet"
        @updated="handleMaterialDetailUpdated"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { debounce } from 'lodash-es';
import api from '../../services/api';
import { API_BASE_URL } from '@/services/api/config';
import { useNotification } from '../../services/notificationService';
import SettingManager from '../../components/SettingManager.vue';
import MaterialDetailSheet from '../../components/MaterialDetailSheet.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  bomId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['close']);
const notification = useNotification();

// 原物料相關資料
const materials = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedMaterial = ref(null);

// 已新增的材料列表
const materialList = ref([]);

// 表單數據
const formData = ref({
  itemNumber: 'X',
  materialCategory: '主料', // 預設為主料
  category: '',
  code: '',
  name: '',
  unit: '',
  usagePosition: '',
  quantity: null,
  remark: ''
});

// 材料類別選項
const materialCategoryOptions = ['主料', '副料', '五金', '其他'];
const showMaterialCategoryDropdown = ref(false);

// 圖片上傳相關
const fileInput = ref(null);
const imagePreview = ref(null);
const isUploading = ref(false);
const selectedFiles = ref([]);
const uploadProgress = ref(0);

// 圖片放大相關
const zoomedImage = ref(null);
const zoomedImagePosition = ref({ x: 0, y: 0 });

// 已上傳圖片區域相關
const isImagesCollapsed = ref(false);

// 使用部位下拉選單相關
const showUsagePositionDropdown = ref(false);
const usagePositions = ref([]);
const filteredUsagePositions = ref([]);

// 設定管理相關
const settingManagerVisible = ref(false);
const currentSettingType = ref('');
const currentSettingTitle = ref('');
const managedUsagePositions = ref([]);

// 產品用料明細表相關
const materialDetailSheetVisible = ref(false);
const selectedDetailMaterial = ref(null);
const materialDetailSheetType = ref('主料'); // 預設為主料

// 項次下拉選單相關
const showItemNumberDropdown = ref(false);
const itemNumberOptions = ref([]);

// 計算圖片放大容器的樣式
const zoomedImageStyle = computed(() => {
  return {
    left: `${zoomedImagePosition.value.x}px`,
    top: `${zoomedImagePosition.value.y}px`
  };
});

// 獲取圖片URL
const getImageUrl = (url) => {
  // 確保 url 是字符串
  if (!url || typeof url !== 'string') {
    // 只在開發模式下記錄警告
    if (process.env.NODE_ENV === 'development') {
      console.warn('無效的圖片URL:', url);
    }
    return '';
  }

  // 如果是相對路徑，添加基本 URL
  if (url.startsWith('/database/')) {
    // 將 /database/ 路徑轉換為直接訪問 uploads 目錄
    const newUrl = url.replace('/database/uploads', '/api/database/uploads');
    // 如果是相對路徑，添加 API_BASE_URL
    return `${API_BASE_URL}${newUrl}`;
  } else if (url.startsWith('/')) {
    // 其他相對路徑，也添加 API_BASE_URL
    return `${API_BASE_URL}${url}`;
  }

  // 如果是完整URL，直接返回
  return url;
};

// 處理圖片加載錯誤
// 注意：已在下方定義了更完整的 handleImageError 函數

// 顯示放大圖片
const showZoomedImage = (event, imageUrl) => {
  // 確保 imageUrl 是有效的
  if (!imageUrl || typeof imageUrl !== 'string') {
    // 只在開發模式下記錄警告
    if (process.env.NODE_ENV === 'development') {
      console.warn('無效的圖片URL用於放大:', imageUrl);
    }
    return;
  }

  // 即使圖片加載失敗，仍然將URL設置到放大圖片中
  zoomedImage.value = imageUrl;

  // 取得滑鼠位置
  const updatePosition = (e) => {
    const mouseEvent = e || event;
    if (mouseEvent && mouseEvent.clientX) {
      // 確保放大圖片不會超出畫面
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const containerWidth = 300; // 容器寬度
      const containerHeight = 300; // 容器高度

      // 計算容器的位置，確保它不會超出畫面
      let x = mouseEvent.clientX + 20; // 預設在滑鼠右側
      let y = mouseEvent.clientY - 100; // 預設向上移動

      // 確保不會超出右邊緣
      if (x + containerWidth > windowWidth) {
        x = mouseEvent.clientX - containerWidth - 20; // 改為左側顯示
      }

      // 確保不會超出上邊緣
      if (y < 0) {
        y = 10; // 設置一個小的邊距
      }

      // 確保不會超出下邊緣
      if (y + containerHeight > windowHeight) {
        y = windowHeight - containerHeight - 10; // 留一個小的邊距
      }

      zoomedImagePosition.value = { x, y };
    }
  };

  // 初始化位置
  if (event && event.clientX) {
    updatePosition(event);
  }

  // 監聽滑鼠移動
  const handleMouseMove = (e) => {
    updatePosition(e);
  };

  document.addEventListener('mousemove', handleMouseMove);

  // 移除事件監聽器
  setTimeout(() => {
    document.removeEventListener('mousemove', handleMouseMove);
  }, 5000); // 5秒後移除事件監聽器
};

// 隱藏放大圖片
const hideZoomedImage = () => {
  zoomedImage.value = null;
};

// 切換已上傳圖片區域的展開/收起狀態
const toggleImagesCollapsed = () => {
  isImagesCollapsed.value = !isImagesCollapsed.value;
};

// 觸發文件選擇器
const triggerUpload = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 處理圖片加載錯誤
const handleImageError = (event) => {
  // 不顯示錯誤訊息，只在開發模式下記錄
  if (process.env.NODE_ENV === 'development') {
    console.warn('圖片加載失敗:', event.target.src);
  }

  // 將圖片容器標記為錯誤狀態，但不顯示錯誤文字
  event.target.parentElement.classList.add('image-error');

  // 不設置 data-error 屬性，避免顯示錯誤文字
  // event.target.parentElement.setAttribute('data-error', '圖片加載失敗');

  // 不將圖片設置為隱藏，以便在懸停時仍可以顯示
  // event.target.style.display = 'none';
};

// 處理圖片上傳
const handleImageUpload = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    // 清除現有選擇的檔案
    selectedFiles.value = [];

    // 處理每個選擇的檔案
    Array.from(files).forEach(file => {
      // 驗證檔案類型
      if (!file.type.startsWith('image/')) {
        notification.error(`檔案 ${file.name} 不是圖片格式`);
        return;
      }

      // 驗證檔案大小，限制為 5MB
      if (file.size > 5 * 1024 * 1024) {
        notification.error(`圖片 ${file.name} 大小超過 5MB`);
        return;
      }

      // 讀取檔案並建立預覽
      const reader = new FileReader();
      reader.onload = (e) => {
        selectedFiles.value.push({
          file: file,
          preview: e.target.result,
          name: file.name
        });
      };
      reader.readAsDataURL(file);
    });
  }
};

// 移除選擇的檔案
const removeSelectedFile = (index) => {
  selectedFiles.value.splice(index, 1);
};

// 清除所有選擇的檔案
const clearSelectedFiles = () => {
  selectedFiles.value = [];
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

// 已上傳圖片列表
const uploadedImages = ref([]);

// 獲取已上傳圖片列表
const fetchUploadedImages = async () => {
  try {
    // 從材料列表中篩選出只有圖片的記錄
    const materialListResponse = await api.bomMaterialUsage.getByBomId(props.bomId);
    if (materialListResponse && materialListResponse.status === 'success' && materialListResponse.data) {
      // 篩選出只有圖片的記錄（沒有材料名稱等其他欄位）
      // 確保只獲取圖片記錄，不包含材料記錄
      const images = materialListResponse.data
        .filter(item => {
          // 只選擇圖片記錄（有圖片URL但沒有材料名稱）
          return item.image_url &&
                 item.image_url.includes('/bom_images/') &&
                 (!item.material_name || item.material_name === '') &&
                 (!item.material_type || item.material_type === '');
        })
        .map(item => ({
          id: item.id,
          url: item.image_url
        }));

      // 按照創建時間降序排序，最新的在前面
      uploadedImages.value = images.sort((a, b) => {
        const itemA = materialListResponse.data.find(item => item.id === a.id);
        const itemB = materialListResponse.data.find(item => item.id === b.id);
        const dateA = itemA && itemA.createdAt ? new Date(itemA.createdAt) : new Date(0);
        const dateB = itemB && itemB.createdAt ? new Date(itemB.createdAt) : new Date(0);
        return dateB - dateA; // 降序排列，最新的在前面
      });

      // 只在開發模式下記錄訊息
      if (process.env.NODE_ENV === 'development') {
        console.log('已獲取', uploadedImages.value.length, '張已上傳圖片');
      }
    }
  } catch (error) {
    console.error('獲取已上傳圖片失敗:', error);
  }
};

// 上傳多張圖片
const uploadImages = async () => {
  if (selectedFiles.value.length === 0) {
    notification.warning('請先選擇圖片');
    return;
  }

  try {
    isUploading.value = true;
    uploadProgress.value = 0;

    // 計算每張圖片的進度百分比
    const progressIncrement = 100 / selectedFiles.value.length;

    // 依序上傳每張圖片
    for (let i = 0; i < selectedFiles.value.length; i++) {
      const imageData = selectedFiles.value[i].preview;

      try {
        const response = await api.bomMaterialUsage.uploadImage(props.bomId, imageData);

        if (response && response.status === 'success' && response.data) {
          // 將新上傳的圖片添加到已上傳圖片列表中
          if (response.data.image_url) {
            uploadedImages.value.push({
              id: response.data.id || Date.now().toString(),
              url: response.data.image_url
            });
          }
        }
      } catch (uploadError) {
        console.error(`上傳圖片 ${selectedFiles.value[i].name} 失敗:`, uploadError);
        notification.error(`圖片 ${selectedFiles.value[i].name} 上傳失敗`);
      }

      // 更新進度
      uploadProgress.value = Math.min(100, Math.round((i + 1) * progressIncrement));
    }

    // 全部上傳完成
    if (uploadProgress.value === 100) {
      notification.success(`成功上傳 ${selectedFiles.value.length} 張圖片`);
      // 清除已選擇的檔案
      clearSelectedFiles();
    }
  } catch (error) {
    console.error('上傳圖片失敗:', error);
    notification.error('上傳圖片失敗');
  } finally {
    isUploading.value = false;
    uploadProgress.value = 0;
  }
};

// 刪除已上傳圖片
const deleteUploadedImage = async (index, id) => {
  try {
    if (id) {
      // 如果有 ID，則向後端發送刪除請求
      await api.bomMaterialUsage.delete(props.bomId, id);
    }
    // 從列表中移除
    uploadedImages.value.splice(index, 1);
    // 清除放大圖片狀態，避免圖片被刪除後仍然顯示
    zoomedImage.value = null;
    notification.success('圖片刪除成功');

    // 不需要重新獲取材料列表，因為圖片記錄已經在 fetchMaterialList 中被過濾掉
  } catch (error) {
    console.error('刪除圖片失敗:', error);
    notification.error('刪除圖片失敗');
  }
};

// 搜尋原物料
const searchMaterials = debounce(() => {
  fetchMaterials();
}, 300);

// 獲取原物料數據
const fetchMaterials = async () => {
  try {
    loading.value = true;
    const response = await api.materials.getAll({
      search: searchQuery.value
    });

    if (response && Array.isArray(response)) {
      // 顯示所有結果，不限制數量
      // 按照材料編號排序
      materials.value = response.sort((a, b) => {
        // 如果沒有材料編號，則放到最後
        if (!a.code) return 1;
        if (!b.code) return -1;

        // 將材料編號轉換為小寫以進行比較
        const codeA = a.code.toLowerCase();
        const codeB = b.code.toLowerCase();

        // 如果材料編號是數字，則按照數字大小排序
        if (!isNaN(codeA) && !isNaN(codeB)) {
          return parseFloat(codeA) - parseFloat(codeB);
        }

        // 否則按照字母排序
        return codeA.localeCompare(codeB);
      });

      // 圖片路徑在顯示時使用 getImageUrl 函數處理，不需要在這裡修改
      console.log('原物料數據加載完成，共', materials.value.length, '條記錄');
    } else {
      materials.value = [];
    }
  } catch (error) {
    console.error('獲取原物料數據失敗:', error);
    notification.error('獲取原物料數據失敗');
    materials.value = [];
  } finally {
    loading.value = false;
  }
};

// 根據材料類型和已使用項次獲取項次選項
const getItemNumberOptions = (category) => {
  // 獲取已使用項次的集合
  const usedItemNumbers = new Set(materialList.value.map(item => item.item_number));
  console.log('已使用的項次:', Array.from(usedItemNumbers));

  // 根據不同的材料分類生成基本選項
  let baseOptions = [];
  if (category === '皮料') {
    baseOptions = ['A', 'B', 'C', 'D', 'E'];
  } else if (category === '五金') {
    // 生成1到20的數字選項
    baseOptions = Array.from({ length: 20 }, (_, i) => String(i + 1));
  } else {
    // 其他分類返回 'X'
    return ['X']; // X 可以重複使用，不需要過濾
  }

  // 過濾掉已使用的選項
  const filteredOptions = baseOptions.filter(option => !usedItemNumbers.has(option));
  console.log('過濾後的項次選項:', filteredOptions);

  // 如果過濾後沒有選項，則返回基本選項（避免選項為空）
  return filteredOptions.length > 0 ? filteredOptions : baseOptions;
};

// 選擇原物料
const selectMaterial = (material) => {
  selectedMaterial.value = material;

  formData.value = {
    category: material.category || '',
    code: material.code || '',
    name: material.name || '',
    unit: material.unit || '',
    usagePosition: '',
    quantity: null,
    remark: ''
  };

  // 根據分類設置項次選項和默認值
  const category = material.category || '';
  itemNumberOptions.value = getItemNumberOptions(category);

  // 如果有選項可用，設置為第一個選項；否則設置為X
  if (itemNumberOptions.value.length > 0) {
    formData.value.itemNumber = itemNumberOptions.value[0];
  } else {
    formData.value.itemNumber = 'X';
  }
};

// 取消新增材料
const cancelAddMaterial = () => {
  selectedMaterial.value = null;
  formData.value = {
    itemNumber: 'X',
    materialCategory: '主料',
    category: '',
    code: '',
    name: '',
    unit: '',
    usagePosition: '',
    quantity: null,
    remark: ''
  };
};

// 選擇材料類別
const selectMaterialCategory = (option) => {
  formData.value.materialCategory = option;
  showMaterialCategoryDropdown.value = false;
};

// 處理材料類別輸入框失焦
const handleMaterialCategoryBlur = () => {
  setTimeout(() => {
    showMaterialCategoryDropdown.value = false;
  }, 200);
};

// 新增材料
const addMaterial = async () => {
  try {
    // 準備要提交的數據，確保所有必填欄位都有有效的值
    const materialData = {
      materialType: formData.value.category || '其他', // 確保材料類型有值
      materialName: formData.value.name || '無名稱', // 確保材料名稱有值
      // 如果用戶沒有輸入用量，則設置為 0.01 (後端要求)
      // 但在顯示時不會顯示這個預設值
      quantity: formData.value.quantity ? parseFloat(formData.value.quantity) : 0.01,
      unit: formData.value.unit || '個', // 確保單位有值
      remark: formData.value.remark || '',
      code: formData.value.code || '',
      inventoryId: selectedMaterial.value.id,
      image_url: selectedMaterial.value.image_url
    };

    // 確保項次、材料類別和使用部位欄位有值
    // 即使是空字串也要設置，確保後端收到這些欄位
    materialData.item_number = formData.value.itemNumber || 'X';
    materialData.material_category = formData.value.materialCategory || '主料';
    materialData.usage_position = formData.value.usagePosition || '';



    // 向後端提交數據
    const response = await api.bomMaterialUsage.create(props.bomId, materialData);

    if (response && response.status === 'success' && response.data) {


      // 將新增的材料添加到列表中
      const newItem = response.data;

      // 確保圖片 URL 存在
      if (!newItem.image_url) {
        newItem.image_url = selectedMaterial.value.image_url;
      }

      // 直接設定屬性而不進行更新請求
      if (!newItem.item_number && newItem.item_number !== '') {
        newItem.item_number = 'X';
      }

      // 直接設定使用部位而不進行更新請求
      if (!newItem.usage_position && newItem.usage_position !== '') {
        newItem.usage_position = formData.value.usagePosition || '';
      }



      // 將新增的材料添加到列表中
      // 不需要手動添加，因為我們會重新獲取材料列表
      notification.success('材料新增成功');

      // 重新獲取材料列表，確保所有數據都是最新的
      // 列表會根據項次和創建時間排序，最新的材料會在相同項次的最前面
      await fetchMaterialList();

      // 更新下拉選單數據
      await fetchDropdownData();

      cancelAddMaterial(); // 清除表單
    }
  } catch (error) {
    console.error('新增材料失敗:', error);

    // 顯示友善的錯誤信息
    if (error.message && error.message.includes('必填欄位')) {
      notification.warning('系統要求填寫用量，請輸入一個數字後再試');
    } else {
      notification.error('新增材料失敗，請試著輸入用量或聯繫管理員');
    }
  }
};

// 刪除材料
const deleteMaterial = async (index, id) => {
  try {
    if (id) {
      // 如果有 ID，則向後端發送刪除請求
      await api.bomMaterialUsage.delete(props.bomId, id);
    }
    // 從列表中移除
    materialList.value.splice(index, 1);
    // 清除放大圖片狀態，避免圖片被刪除後仍然顯示
    zoomedImage.value = null;
    notification.success('材料刪除成功');
  } catch (error) {
    console.error('刪除材料失敗:', error);
    notification.error('刪除材料失敗');
  }
};

// 格式化數字到小數點後兩位
const formatNumber = (value) => {
  const num = Number(value);
  if (isNaN(num)) {
    return value; // 如果不是有效數字，返回原值
  }
  // 使用 toFixed(2) 確保始終顯示兩位小數
  return num.toFixed(2);
};

// 處理用量輸入，確保小數點後最多兩位
const formatQuantity = () => {
  // 如果輸入的值為空或不是數字，則不處理
  if (!formData.value.quantity && formData.value.quantity !== 0) {
    return;
  }

  // 將輸入的值轉換為字符串
  const valueStr = formData.value.quantity.toString();

  // 如果包含小數點
  if (valueStr.includes('.')) {
    const parts = valueStr.split('.');
    // 如果小數點後超過兩位，則截斷為兩位
    if (parts[1] && parts[1].length > 2) {
      formData.value.quantity = parseFloat(parts[0] + '.' + parts[1].substring(0, 2));
    }
  }
};

// 判斷是否應該顯示用量
const shouldShowQuantity = (value) => {
  // 如果用量為空或為預設值 0.01，則不顯示
  const num = Number(value);
  if (isNaN(num) || num === 0 || num === 0.01) {
    return false;
  }
  return true;
};

// 判斷項目是否可點擊（根據材料類別判斷）
const isItemClickable = (item) => {
  // 檢查材料類別
  const materialCategory = item.material_category || '';
  if (materialCategory === '主料' || materialCategory === '副料') {
    return true; // 如果材料類別是「主料」或「副料」，則可點擊
  }

  // 檢查項次是否為英文字母（保留原有邏輯作為備用）
  const isAlphaItemNumber = /^[A-Za-z]/.test(item.item_number || '');

  // 如果是英文字母項次，則可點擊
  return isAlphaItemNumber;
};

// 打開產品用料明細表
const openMaterialDetailSheet = (item) => {
  // 如果項目不可點擊，則不執行任何操作
  if (!isItemClickable(item)) return;

  // 根據材料類別決定顯示主料或副料明細表
  const materialCategory = item.material_category || '';

  if (materialCategory === '主料') {
    materialDetailSheetType.value = '主料';
  } else if (materialCategory === '副料') {
    materialDetailSheetType.value = '副料';
  } else {
    // 如果材料類別不是主料或副料，則使用預設值「主料」
    materialDetailSheetType.value = '主料';
  }

  // 設置選中的材料項目
  selectedDetailMaterial.value = item;

  // 顯示產品用料明細表
  materialDetailSheetVisible.value = true;
};

// 關閉產品用料明細表
const closeMaterialDetailSheet = () => {
  materialDetailSheetVisible.value = false;
};

// 處理產品用料明細表更新
const handleMaterialDetailUpdated = () => {
  // 重新獲取材料列表，確保所有數據都是最新的
  fetchMaterialList();
};

// 關閉彈窗
const closeDialog = () => {
  emit('close');
};

// 搜索使用部位
const searchUsagePositions = () => {
  const input = formData.value.usagePosition ? formData.value.usagePosition.toLowerCase() : '';
  if (!input) {
    filteredUsagePositions.value = usagePositions.value;
  } else {
    filteredUsagePositions.value = usagePositions.value.filter(item =>
      item.toLowerCase().includes(input)
    );
  }
  showUsagePositionDropdown.value = true;
};

// 切換使用部位下拉選單顯示狀態
const toggleUsagePositionDropdown = () => {
  showUsagePositionDropdown.value = !showUsagePositionDropdown.value;
  if (showUsagePositionDropdown.value) {
    filteredUsagePositions.value = usagePositions.value;
  }
};

// 選擇使用部位
const selectUsagePosition = (item) => {
  formData.value.usagePosition = item;
  showUsagePositionDropdown.value = false;
};

// 處理使用部位輸入框失焦
const handleUsagePositionBlur = () => {
  setTimeout(() => {
    showUsagePositionDropdown.value = false;
  }, 200);
};

// 獲取使用部位數據
const fetchDropdownData = async () => {
  try {
    // 從已新增的材料中提取不重複的使用部位
    const usagePositionsSet = new Set();
    const managedSet = new Set(managedUsagePositions.value); // 用於快速查找

    // 遍歷材料列表，提取使用部位
    materialList.value.forEach(item => {
      // 檢查使用部位屬性名稱
      if (item.usage_position && !managedSet.has(item.usage_position)) {
        usagePositionsSet.add(item.usage_position);
      }
    });

    // 先顯示管理的使用部位，再顯示其他使用部位
    usagePositions.value = [...managedUsagePositions.value, ...Array.from(usagePositionsSet).sort()];

    // 初始化過濾列表
    filteredUsagePositions.value = usagePositions.value;


  } catch (error) {
    console.error('獲取下拉選單數據失敗:', error);
  }
};

// 獲取使用部位設定
const fetchUsagePositionSettings = async () => {
  try {
    const response = await api.settingRecord.getAll({
      type: 'usagePosition'
    });

    if (response && response.data) {
      // 根據 sort_order 排序
      managedUsagePositions.value = response.data
        .sort((a, b) => a.sort_order - b.sort_order)
        .map(item => item.name); // 只取名稱
    } else {
      managedUsagePositions.value = [];
    }
    // 更新使用部位列表
    updateFilteredUsagePositions();
  } catch (error) {
    console.error('獲取使用部位設定失敗:', error);
    notification.error('獲取使用部位設定失敗');
    managedUsagePositions.value = []; // 出錯時清空
  }
};

// 更新過濾後的使用部位列表
const updateFilteredUsagePositions = () => {
  filteredUsagePositions.value = usagePositions.value;
};

// 開啟設定管理器
const openSettingManager = (type, title) => {
  currentSettingType.value = type;
  currentSettingTitle.value = title;
  settingManagerVisible.value = true;
};

// 處理設定更新
const handleSettingUpdated = async (updatedType) => {
  // 根據更新的設定類型重新獲取相應的設定
  if (updatedType === 'usagePosition') {
    await fetchUsagePositionSettings();
    await fetchDropdownData();
  }
};

// 獲取材料列表
const fetchMaterialList = async () => {
  try {
    loading.value = true;
    const response = await api.bomMaterialUsage.getByBomId(props.bomId);

    if (response && response.status === 'success' && Array.isArray(response.data)) {
      // 過濾掉只有圖片的記錄（沒有材料名稱等其他欄位）
      const filteredData = response.data.filter(item => {
        // 如果是圖片記錄（沒有材料名稱但有圖片URL），則過濾掉
        if (item.image_url && item.image_url.includes('/bom_images/') && !item.material_name) {
          return false;
        }
        return true;
      });

      // 先處理數據，確保每個材料都有項次和使用部位屬性
      const processedData = filteredData.map((material) => {
        // 如果沒有 item_number，則設置為 X
        if (!material.item_number && material.item_number !== '') {
          material.item_number = 'X';
        }

        // 如果沒有 usage_position，則設置為空字串
        if (!material.usage_position && material.usage_position !== '') {
          material.usage_position = '';
        }

        return material;
      });

      // 先按照項次排序：先字母 ABC，再數字 123，最後是 X 和空值
      // 然後在相同項次內按創建時間排序（最新的在前面）
      materialList.value = processedData.sort((a, b) => {
        const itemA = a.item_number || '';
        const itemB = b.item_number || '';

        // 如果項次相同，按創建時間排序（最新的在前面）
        if (itemA === itemB) {
          // 假設 createdAt 是 ISO 格式的日期字符串
          const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
          const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
          return dateB - dateA; // 降序排列，最新的在前面
        }

        // 如果一個是 X，則放在最後
        if (itemA === 'X' && itemB !== 'X') {
          return 1;
        }

        if (itemA !== 'X' && itemB === 'X') {
          return -1;
        }

        // 如果兩個都是字母，按字母排序
        const isLetterA = /^[A-Za-z]$/.test(itemA);
        const isLetterB = /^[A-Za-z]$/.test(itemB);

        if (isLetterA && isLetterB) {
          return itemA.localeCompare(itemB);
        }

        // 如果兩個都是數字，按數字排序
        const isNumberA = /^\d+$/.test(itemA);
        const isNumberB = /^\d+$/.test(itemB);

        if (isNumberA && isNumberB) {
          return parseInt(itemA) - parseInt(itemB);
        }

        // 如果一個是字母，一個是數字，字母在前
        if (isLetterA && isNumberB) {
          return -1;
        }

        if (isNumberA && isLetterB) {
          return 1;
        }

        // 其他情況，按字符串比較
        return itemA.localeCompare(itemB);
      });


    } else {
      materialList.value = [];
    }

    // 獲取完材料列表後，提取使用部位數據
    await fetchDropdownData();
  } catch (error) {
    console.error('獲取材料列表失敗:', error);
    notification.error('獲取材料列表失敗');
    materialList.value = [];
  } finally {
    loading.value = false;
  }
};

// 監聽彈窗顯示狀態
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 彈窗顯示時獲取數據
    fetchUsagePositionSettings();
    fetchMaterials();
    fetchMaterialList();
    fetchUploadedImages(); // 獲取已上傳圖片
  }
});

// 頁面加載時初始化
onMounted(() => {
  if (props.visible) {
    fetchUsagePositionSettings();
    fetchMaterials();
    fetchMaterialList();
    fetchUploadedImages(); // 獲取已上傳圖片
  }
});

// 搜尋項次
const searchItemNumbers = () => {
  // 項次選項由材料分類決定，不需要過濾，直接顯示全部選項
  showItemNumberDropdown.value = true;
};

// 切換項次下拉選單顯示狀態
const toggleItemNumberDropdown = () => {
  showItemNumberDropdown.value = !showItemNumberDropdown.value;
};

// 選擇項次
const selectItemNumber = (option) => {
  formData.value.itemNumber = option;
  showItemNumberDropdown.value = false;
};

// 處理項次輸入框失焦
const handleItemNumberBlur = () => {
  setTimeout(() => {
    showItemNumberDropdown.value = false;
  }, 200);
};

// 監聽材料列表變化，更新項次選項
watch(materialList, () => {
  if (selectedMaterial.value) {
    // 更新項次選項
    const category = selectedMaterial.value.category || '';
    itemNumberOptions.value = getItemNumberOptions(category);
  }
}, { deep: true });
</script>

<style scoped>
.material-usage-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.material-usage-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 24px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.material-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.close-btn:hover {
  color: #333;
}

/* 搜尋區域樣式 */
.search-section {
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
}

.search-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.search-bar input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.search-btn {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.search-btn:hover {
  background-color: #2563eb;
}

/* 搜尋結果表格 */
.search-results {
  height: 200px; /* 固定高度 */
  overflow-y: auto; /* 允許垂直滾動 */
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

.search-results table {
  width: 100%;
  border-collapse: collapse;
}

.search-results th,
.search-results td {
  padding: 6px 8px; /* 降低填充，使其更緊湊 */
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px; /* 降低字體大小 */
  white-space: nowrap; /* 防止文字換行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 文字過長時顯示省略號 */
}

.search-results th {
  background-color: #f3f4f6;
  font-weight: 500;
  color: #4b5563;
}

.search-results tr:hover {
  background-color: #f9fafb;
}

.material-image {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f3f4f6;
}

.material-image span {
  font-size: 10px;
  line-height: 1;
  text-align: center;
  color: #6b7280;
}

.material-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.add-btn {
  padding: 4px 8px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-btn:hover {
  background-color: #059669;
}

.loading-message,
.no-data-message {
  text-align: center;
  padding: 16px;
  color: #6b7280;
  font-style: italic;
}

/* 新增材料表單 */
.add-material-form {
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
}

.add-material-form h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

/* 材料信息區域 */
.material-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f0f4f8;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.info-label {
  font-weight: 500;
  color: #4b5563;
  font-size: 14px;
}

.info-value {
  color: #1f2937;
  font-size: 14px;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  width: 0; /* 確保所有 form-group 寬度相等 */
  min-width: 120px; /* 設置最小寬度，避免過度壓縮 */
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: #4b5563;
}

.form-group input {
  width: 100%;
  height: 38px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input[readonly] {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.required {
  color: #ef4444;
}

.hint {
  color: #6b7280;
  font-weight: normal;
  font-style: italic;
}

/* 自定義下拉選單樣式 */
.custom-select {
  position: relative;
  width: 100%;
}

.select-with-setting {
  display: flex;
  align-items: stretch;
  position: relative;
  width: 100%;
  height: 38px; /* 統一高度 */
}

.select-with-setting .custom-select {
  flex: 1;
}

.select-with-setting .select-input-container input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.setting-btn {
  width: 38px;
  height: 100%;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-left: none;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
  padding: 0;
}

.setting-btn:hover {
  background-color: #e5e7eb;
  color: #4b5563;
}

.select-input-container {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
}

.select-input-container input {
  width: 100%;
  height: 100%;
  padding: 8px 30px 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.select-arrow {
  position: absolute;
  right: 10px;
  color: #6b7280;
  cursor: pointer;
  pointer-events: none;
}

.select-items {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 4px;
}

.select-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
}

.select-item:hover {
  background-color: #f3f4f6;
}

.full-width-input {
  width: 100%;
  height: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  font-size: 14px;
  box-sizing: border-box;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  align-items: flex-end;
}

.cancel-form-btn {
  padding: 8px 16px;
  background-color: #e5e7eb;
  color: #4b5563;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-form-btn:hover {
  background-color: #d1d5db;
}

.confirm-form-btn {
  padding: 8px 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-form-btn:hover {
  background-color: #2563eb;
}

/* 圖片上傳區域 - 精簡版 */
.image-upload-section {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9fafb;
}

.image-upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.image-upload-section h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.image-upload-actions {
  display: flex;
  gap: 8px;
}

.select-images-btn {
  padding: 6px 12px;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.select-images-btn:hover {
  background-color: #e5e7eb;
}

/* 精簡預覽區域 */
.compact-preview-area {
  margin-bottom: 12px;
}

.selected-files-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #4b5563;
}

.clear-all-btn {
  background: none;
  border: none;
  color: #ef4444;
  font-size: 14px;
  cursor: pointer;
  padding: 0;
}

.clear-all-btn:hover {
  text-decoration: underline;
}

.compact-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: white;
}

.compact-preview-item {
  position: relative;
  width: 100%;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.compact-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-preview-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  padding: 0;
}

.remove-preview-btn:hover {
  background-color: rgba(220, 38, 38, 1);
}

.image-preview {
  width: 200px;
  height: 200px;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #ef4444;
}

.remove-image-btn:hover {
  background-color: rgba(255, 255, 255, 1);
  color: #dc2626;
}

.upload-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #9ca3af;
  transition: all 0.3s;
}

.upload-placeholder:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.upload-placeholder i {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 上傳進度條 */
.upload-progress {
  width: 100%;
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background-color: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
}

.upload-btn {
  padding: 6px 12px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.upload-btn:hover {
  background-color: #2563eb;
}

.upload-btn:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* 已上傳圖片列表 */
.uploaded-images-container {
  margin-top: 20px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.uploaded-images-container h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 14px;
  color: #4b5563;
}

.uploaded-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.uploaded-image-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.uploaded-image-preview {
  width: 100%;
  height: 100px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s;
}

.uploaded-image-preview:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.uploaded-image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.delete-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(239, 68, 68, 0.8);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.uploaded-image-preview:hover .delete-image-btn {
  opacity: 1;
}

.delete-image-btn:hover {
  background-color: rgba(220, 38, 38, 1);
}

.image-error {
  position: relative;
  background-color: #f9fafb;
  /* 不顯示錯誤文字，只改變背景色 */
}

/* 只在有 data-error 屬性時才顯示錯誤文字 */
.image-error[data-error]::after {
  content: attr(data-error);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9fafb;
  color: #ef4444;
  font-size: 12px;
  text-align: center;
  padding: 8px;
}

/* 材料列表 */
.material-list-section {
  margin-bottom: 24px;
}

.material-list-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.material-list {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow-x: auto;
}

.material-list table {
  width: 100%;
  border-collapse: collapse;
}

.material-list th,
.material-list td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.material-list th {
  background-color: #f3f4f6;
  font-weight: 500;
  color: #4b5563;
}

.material-list tr:hover {
  background-color: #f9fafb;
}

.delete-btn {
  padding: 4px 8px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.delete-btn:hover {
  background-color: #dc2626;
}

/* 對話框底部按鈕 */
.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  background-color: #e5e7eb;
  color: #4b5563;
}

.cancel-btn:hover {
  background-color: #d1d5db;
}

/* 已上傳圖片區域 - 固定在頂部 */
.uploaded-images-section {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.uploaded-images-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.uploaded-images-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.toggle-images-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.toggle-images-btn:hover {
  background-color: #f3f4f6;
  color: #4b5563;
}

.uploaded-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  padding: 8px 0;
  max-height: 150px;
  overflow-y: auto;
}

/* 圖片放大容器 */
.zoomed-image-container {
  position: fixed;
  z-index: 1100;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 8px;
  width: 300px; /* 增加寬度 */
  height: 300px; /* 增加高度 */
  overflow: hidden;
  pointer-events: none; /* 確保不會影響滑鼠事件 */
}

.zoomed-image-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block; /* 確保圖片正確顯示 */
}

/* 可點擊的類別樣式 */
.clickable {
  cursor: pointer;
  color: #1890ff;
  font-weight: bold;
}
</style>
