<template>
  <div class="bom-container">
    <div class="bom-header">
      <h2>BOM管理</h2>
      <div class="bom-actions">
        <button
          class="add-btn"
          @click="openBomForm"
          :disabled="isAddBomDisabled"
        >
          <i class="fas fa-plus"></i>
          新增BOM
        </button>
        <div class="search-container">
          <span>搜尋</span>
          <input type="text" class="search-input" placeholder="請輸入關鍵字" v-model="searchQuery" />
        </div>
      </div>
    </div>

    <div class="bom-table-container">
      <table class="bom-table">
        <thead>
          <tr>
            <th class="expand-col"></th>
            <th>屬性</th>
            <th>產品號碼</th>
            <th>材料名稱</th>
            <th>狀態</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in bomList" :key="item.id || item.code" @click="editBom(item)" style="cursor: pointer;">
            <td class="expand-col">
              <button class="expand-btn" @click.stop="toggleExpandRow(item)">
                <i class="fas" :class="expandedRows.includes(item.id || item.code) ? 'fa-minus' : 'fa-plus'"></i>
              </button>
            </td>
            <td>{{ item.productStatus }}</td>
            <td>{{ item.productCode || item.code }}</td>
            <td>{{ item.cName || item.name }}</td>
            <td>
              <span :class="getStatusClass(item.confirmationStatus)">
                {{ getConfirmationStatusText(item.confirmationStatus) }}
              </span>
            </td>
            <td>
              <button
                class="material-btn"
                @click.stop="viewProcessDetails(item)"
              >
                <i class="fas fa-list-ul"></i>
                備料工序
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="loading" class="loading-indicator">
        <i class="fas fa-spinner fa-spin"></i> 載入中...
      </div>

      <div v-if="!loading && bomList.length === 0" class="empty-state">
        暫無BOM資料，請點擊「新增BOM」添加
      </div>
    </div>

    <BomForm
      :visible="showBomForm"
      :edit-data="currentEditBom"
      :edit-mode="isEditMode"
      :read-only="isReadOnlyMode"
      :is-admin="isAdmin"
      :userDepartment="currentUserDepartment"
      @close="closeBomForm"
      @save="saveBom"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { debounce } from 'lodash-es';
import BomForm from '../../components/BomForm.vue';
import api from '../../services/api';
import { showSuccess, showError, showWarning, showConfirm } from '../../services/notificationService';
import { useRouter } from 'vue-router';

const searchQuery = ref('');
const bomList = ref([]);
const expandedRows = ref([]);
const loading = ref(false);
const showBomForm = ref(false);
const currentEditBom = ref(null);
const isEditMode = ref(false);
const isReadOnlyMode = ref(false);
const currentUserDepartment = ref(-1);
const isAdmin = ref(false);
const router = useRouter();

const isAddBomDisabled = computed(() => currentUserDepartment.value !== 0);

onMounted(async () => {
  await fetchCurrentUser();
  await fetchBomList();

  watch(
    () => searchQuery.value,
    debounce(async () => {
      await handleSearch();
    }, 300)
  );
});

const isConfirmationStatusIncludesDepartment = (confirmationStatus, department) => {
  if (!confirmationStatus) return false;

  const departments = confirmationStatus
    .split(',')
    .map(num => parseInt(num.trim()))
    .filter(num => !isNaN(num));

  return departments.includes(department);
};

const fetchCurrentUser = async () => {
  try {
    const response = await api.getCurrentUser();
    if (response.status === 'success' && response.data && response.data.employee) {
      currentUserDepartment.value = response.data.employee.department;
      isAdmin.value = response.data.employee.role === 0;
    } else {
      console.error('獲取使用者資訊失敗:', response.message);
    }
  } catch (error) {
    console.error('獲取使用者資訊時發生錯誤:', error);
  }
};

const fetchBomList = async () => {
  try {
    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    try {
      const response = await api.bom.getAll();

      if (response.status === 'success' && response.data && response.data.length > 0) {
        bomList.value = [...response.data].reverse();
      } else {
        bomList.value = [];
      }
    } catch (error) {
      console.error('API請求失敗:', error);
      bomList.value = [];
    }
  } catch (error) {
    console.error('獲取BOM列表錯誤:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = async () => {
  try {
    if (!searchQuery.value.trim()) {
      await fetchBomList();
      return;
    }

    loading.value = true;

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('使用者未登入');
      return;
    }

    try {
      const response = await api.bom.search(searchQuery.value);

      if (response.status === 'success' && response.data) {
        bomList.value = response.data;
      }
    } catch (error) {
      console.error('API搜尋請求失敗:', error);
    }
  } catch (error) {
    console.error('搜尋BOM錯誤:', error);
  } finally {
    loading.value = false;
  }
};

const toggleExpandRow = (item) => {
  const id = item.id || item.code;
  const index = expandedRows.value.indexOf(id);

  if (index === -1) {
    expandedRows.value.push(id);
  } else {
    expandedRows.value.splice(index, 1);
  }
};

const openBomForm = () => {
  isEditMode.value = false;
  isReadOnlyMode.value = false;
  currentEditBom.value = null;
  showBomForm.value = true;
};

const viewBomDetails = async (item) => {
  if (!item.id) {
    console.error('無法查看：缺少 BOM ID');
    showError('無法獲取此 BOM 的詳細資料');
    return;
  }

  try {
    loading.value = true;
    const response = await api.bom.getById(item.id);

    if (response.status === 'success' && response.data) {
      currentEditBom.value = response.data;
      isEditMode.value = false;
      isReadOnlyMode.value = true;
      showBomForm.value = true;
    } else {
      console.error('獲取 BOM 詳細資料失敗:', response.message);
      showError('獲取 BOM 詳細資料失敗');
    }
  } catch (error) {
    console.error('查看 BOM 詳細資料時發生錯誤:', error);
    showError('查看 BOM 詳細資料時出錯，請稍後再試');
  } finally {
    loading.value = false;
  }
};

const editBom = async (item) => {
  if (!item.id) {
    console.error('無法編輯：缺少 BOM ID');
    showError('無法獲取此 BOM 的詳細資料');
    return;
  }

  try {
    loading.value = true;
    const response = await api.bom.getById(item.id);

    if (response.status === 'success' && response.data) {
      currentEditBom.value = response.data;
      isEditMode.value = true;

      // 台北部管理員可以完全編輯，其他部門管理員只能確認但不能編輯內容
      if (currentUserDepartment.value === 0 && isAdmin.value) {
        isReadOnlyMode.value = false; // 台北部管理員可以完全編輯
      } else if (isAdmin.value) {
        isReadOnlyMode.value = true; // 其他部門管理員可以查看並確認，但不能編輯內容
      } else {
        isReadOnlyMode.value = true; // 非管理員只能查看
      }

      showBomForm.value = true;
    } else {
      console.error('獲取 BOM 詳細資料失敗:', response.message);
      showError('獲取 BOM 詳細資料失敗');
    }
  } catch (error) {
    console.error('編輯 BOM 時發生錯誤:', error);
    showError('編輯 BOM 時出錯，請稍後再試');
  } finally {
    loading.value = false;
  }
};

const closeBomForm = () => {
  showBomForm.value = false;
  isEditMode.value = false;
  isReadOnlyMode.value = false;
};

const saveBom = async (formData) => {
  try {
    loading.value = true;

    // 檢查用戶是否有權限保存BOM
    if (!(currentUserDepartment.value === 0 && isAdmin.value)) {

      return;
    }

    let response;

    if (isEditMode.value && currentEditBom.value && currentEditBom.value.id) {
      response = await api.bom.update(currentEditBom.value.id, formData);
    } else {
      response = await api.bom.create(formData);
    }

    if (response.status === 'success') {
      await fetchBomList();
      closeBomForm();
      showSuccess(isEditMode.value ? 'BOM更新成功' : 'BOM創建成功');
    }
  } catch (error) {
    console.error('保存BOM錯誤:', error);
    showError(error.message || '保存BOM時出錯，請稍後再試');
  } finally {
    loading.value = false;
  }
};

const getConfirmationStatusText = (status) => {
  const confirmStatus = Number(status);

  switch(confirmStatus) {
    case 0:
      return '未確認';
    case 1:
      return '台北部確認';
    case 2:
      return '廠務部確認';
    case 3:
      return '雙方確認';
    default:
      return '未確認';
  }
};

const getStatusClass = (status) => {
  const confirmStatus = Number(status);
  switch(confirmStatus) {
    case 0:
      return 'status-tag status-pending'; // 灰色，未確認
    case 1:
      return 'status-tag status-taipei'; // 藍色，台北部確認
    case 2:
      return 'status-tag status-factory'; // 綠色，廠務部確認
    case 3:
      return 'status-tag status-confirmed'; // 橘色，雙方確認
    default:
      return 'status-tag status-pending'; // 預設灰色
  }
};

// 查看備料工序
const viewProcessDetails = (item) => {
  if (!item.id) {
    showWarning('無法獲取BOM ID');
    return;
  }

  // 儲存當前編輯中的BOM ID
  localStorage.setItem('currentBomId', item.id);

  // 導航到備料工序頁面
  router.push({
    name: 'ProcessPreparation',
    params: { bomId: item.id }
  });
};
</script>

<style scoped>
.bom-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.bom-header {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 24px;
}

.bom-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.bom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-btn {
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-btn:hover {
  background-color: #1d4ed8;
}

.add-btn:disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
  opacity: 0.6;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-container span {
  font-size: 15px;
  color: #666;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 240px;
}

.bom-table-container {
  overflow-x: auto;
}

.bom-table {
  width: 100%;
  border-collapse: collapse;
}

.bom-table th,
.bom-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.bom-table th {
  background-color: #f5f7fa;
  color: #666;
  font-weight: 500;
}

.expand-col {
  width: 50px;
  text-align: center;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
}

.edit-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.edit-btn {
  background-color: #3b82f6;
  color: white;
}

.edit-btn:hover {
  background-color: #2563eb;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
  font-style: italic;
}

.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px; /* 圓角 */
  font-size: 13px;
  font-weight: 500;
  color: white;
  text-align: center;
  min-width: 60px; /* 確保寬度一致 */
}

.status-pending {
  background-color: #9ca3af; /* 灰色，未確認 */
}

.status-taipei {
  background-color: #3b82f6; /* 藍色，台北部確認 */
}

.status-factory {
  background-color: #10b981; /* 綠色，廠務部確認 */
}

.status-confirmed {
  background-color: #f97316; /* 橘色，雙方確認 */
}

.material-btn {
  padding: 6px 10px;
  background-color: #60a5fa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.material-btn:hover {
  background-color: #3b82f6;
}

.material-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.6;
}
</style>
