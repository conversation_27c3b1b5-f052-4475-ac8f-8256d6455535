<template>
  <div class="process-preparation-container">
    <div class="process-header">
      <button class="add-btn" @click="openMaterialUsage" :disabled="!canEditProcess">
        <i class="fas fa-plus"></i> 材料用量表
      </button>
      <h2>備料工序</h2>
      <button class="clear-btn" @click="clearAllProcesses" v-if="savedProcesses.length > 0">
        <i class="fas fa-trash"></i> 清除所有工序
      </button>
      <div class="back-to-bom" @click="backToBom">
        <i class="fas fa-arrow-left"></i>
        返回BOM
      </div>
    </div>

    <div v-if="!canEditProcess" class="read-only-notice">
      <p v-if="currentUserDepartment === 0">您所屬的部門僅可查看備料工序資料，無法進行編輯操作。</p>
      <p v-else-if="Number(bomConfirmationStatus) !== 0">此BOM已被確認，無法進行編輯操作。只有未確認狀態的BOM才能進行編輯。</p>
    </div>

    <!-- 流程圖區域 -->
    <div class="process-flow-container">
      <!-- 大工序標題列 -->
      <div class="process-flow-header">
        <!-- 材料標題列 -->
        <div class="process-column material-column">
          <h3>材料</h3>
        </div>

        <!-- 大工序標題列 -->
        <div class="process-column" v-for="(process, index) in majorProcesses" :key="index">
          <h3>{{ process.charAt(0) }}<span>{{ process.slice(1) }}</span></h3>
        </div>
      </div>

      <!-- 流程圖主體 -->
      <div class="process-flow-body">
        <!-- 部位名稱導入提示 -->
        <div class="position-import-hint" v-if="positionNames.length === 0">
          <p>部位名稱尚未導入</p>
          <button class="import-btn" @click="openMaterialUsage">
            <i class="fas fa-file-import"></i> 從材料用量表導入部位名稱
          </button>
        </div>

        <!-- 流程圖內容 -->
        <div class="process-flow-content" v-else>
          <!-- 連接線 -->
          <svg class="process-connections" ref="connectionsContainer" style="pointer-events: none; overflow: visible; position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
            <!-- 連接線將在 mounted 和更新時動態生成 -->
          </svg>

          <!-- 材料列 -->
          <div class="process-column material-column">
            <div class="position-items">
              <div
                v-for="(item, idx) in positionNames"
                :key="'material-' + idx"
                class="position-item"
                :class="{ 'selected': selectedPositions.includes(item) }"
                @click="toggleSelectPosition(item)"
              >
                {{ item }}
              </div>
            </div>
          </div>

          <!-- 大工序A列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('A選料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'A選料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序B列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('B開料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'B開料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>
              </div>
            </div>
          </div>

          <!-- 大工序C列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('C備料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'C備料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>

              </div>
            </div>
          </div>

          <!-- 大工序D列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('D塗邊')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'D塗邊') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>

              </div>
            </div>
          </div>

          <!-- 大工序E列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('E製作')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'E製作') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>

              </div>
            </div>
          </div>

          <!-- 大工序F列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('F包裝')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'F包裝') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>

              </div>
            </div>
          </div>

          <!-- 大工序G列 -->
          <div class="process-column">
            <div class="position-items">
              <div
                v-for="process in getProcessesByMajorProcess('G補料')"
                :key="process.id"
                class="process-item"
                :data-source="process.sourcePosition"
                :data-id="process.id"
                :class="{ 'completed': process.isCompleted }"
                @click="selectNextProcess(process)"
              >
                <div class="process-item-order">{{ getProcessIndex(process, 'G補料') }}</div>
                <div class="process-item-name">{{ process.pieceName }}</div>
                <div class="process-item-code">{{ process.sequenceNumber }}</div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <!-- 彈出材料用量表 -->
    <MaterialUsageView
      :visible="materialUsageVisible"
      :bomId="bomId"
      @close="closeMaterialUsage"
      @material-selected="handleMaterialSelected"
    />

    <!-- 工序選擇模態對話框 -->
    <div class="process-modal" v-if="processModalVisible">
      <div class="process-modal-content">
        <div class="process-modal-header">
          <h3>工序選擇 - {{ selectedPosition }}</h3>
          <button class="close-btn" @click="closeProcessModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="process-modal-body">
          <!-- 產生後序號 -->
          <div class="form-group">
            <label>產生後序號：</label>
            <div class="sequence-number">{{ generatedProcessCode }}</div>
          </div>

          <!-- 已完成工序選擇 -->
          <div class="form-group">
            <label>選擇已完成工序合併：</label>
            <div class="help-text">選擇已完成的工序進行合併，合併後將替換原本的工序</div>
            <select v-model="selectedCompletedProcess" @change="handleCompletedProcessChange">
              <option value="">請選擇已完成工序</option>
              <option v-for="process in completedProcesses" :key="process.id" :value="process">
                {{ process.pieceName }} - {{ process.sequenceNumber }} ({{ process.majorProcess }})
              </option>
            </select>
          </div>

          <!-- 分片名稱 -->
          <div class="form-group">
            <label>分片名稱：<span class="required">*</span></label>
            <input type="text" v-model="processForm.pieceName" placeholder="請輸入分片名稱" />
          </div>

          <!-- 部位組織 -->
          <div class="form-group">
            <label>部位組織：</label>
            <select v-model="processForm.partStructure" @change="handlePartStructureChange">
              <option value="">請選擇部位組織</option>
              <option v-for="(part, index) in partStructureList" :key="index" :value="part">
                {{ part.code }} {{ part.name }}
              </option>
            </select>
          </div>

          <!-- 分片組織 -->
          <div class="form-group">
            <label>分片組織：</label>
            <select v-model="processForm.pieceStructure">
              <option value="">請選擇分片組織</option>
              <option v-for="(piece, index) in filteredPieceStructures" :key="index" :value="piece">
                {{ piece.code }} {{ piece.name }}
              </option>
            </select>
          </div>

          <!-- 材料 -->
          <div class="form-group">
            <label>材料：</label>
            <select v-model="processForm.material">
              <option value="">請選擇材料</option>
              <option v-for="(material, index) in materialList" :key="index" :value="material">
                {{ material.name }}
              </option>
            </select>
          </div>

          <!-- 順序 -->
          <div class="form-group">
            <label>順序：</label>
            <select v-model="processForm.order">
              <option value="">請選擇順序</option>
              <option v-for="n in 20" :key="n" :value="n.toString().padStart(2, '0')">
                {{ n.toString().padStart(2, '0') }}
              </option>
            </select>
          </div>

          <!-- 大工序 -->
          <div class="form-group">
            <label>大工序：</label>
            <select v-model="processForm.majorProcess" @change="handleMajorProcessChange">
              <option value="">請選擇大工序</option>
              <option v-for="(process, index) in filteredMajorProcesses" :key="index" :value="process">
                {{ process }}
              </option>
            </select>
          </div>

          <!-- 小工序 -->
          <div class="form-group">
            <label>小工序：</label>
            <select v-model="processForm.minorProcess" @change="handleMinorProcessChange">
              <option value="">請選擇小工序</option>
              <option v-for="(process, index) in filteredMinorProcesses" :key="index" :value="process">
                {{ process.code }} {{ process.name }}
              </option>
            </select>
          </div>

          <!-- 工具 -->
          <div class="form-group">
            <label>工具：</label>
            <select v-model="processForm.tool">
              <option value="">請選擇工具</option>
              <option v-for="(tool, index) in toolOptions" :key="index" :value="tool">
                {{ tool }}
              </option>
            </select>
          </div>

          <!-- 耗材 -->
          <div class="form-group">
            <label>耗材：</label>
            <select v-model="processForm.consumable">
              <option value="">請選擇耗材</option>
              <option v-for="(consumable, index) in consumableOptions" :key="index" :value="consumable">
                {{ consumable }}
              </option>
            </select>
          </div>

          <!-- 數量 -->
          <div class="form-group">
            <label>數量：</label>
            <input type="number" v-model="processForm.quantity" min="1" step="1" />
          </div>

          <!-- 按鈕 -->
          <div class="form-actions">
            <button class="stage-complete-btn" @click="markStageComplete" v-if="!isCurrentProcessCompleted && selectedProcessId">
              <i class="fas fa-check"></i> 階段完成
            </button>
            <button class="stage-incomplete-btn" @click="markStageIncomplete" v-if="isCurrentProcessCompleted && selectedProcessId">
              <i class="fas fa-undo"></i> 取消階段完成
            </button>
            <button class="unmerge-btn" @click="cancelMerge" v-if="isCurrentProcessMerged">
              <i class="fas fa-unlink"></i> 取消合併
            </button>
            <button class="delete-btn" @click="deleteCurrentProcess" v-if="selectedProcessId">
              <i class="fas fa-trash"></i> 刪除工序
            </button>
            <button class="save-btn" @click="saveProcess">
              <i class="fas fa-save"></i> 保存
            </button>
            <button class="cancel-btn" @click="closeProcessModal">
              <i class="fas fa-times"></i> 取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import api from '../../services/api';
import { showError, showWarning, showSuccess } from '../../services/notificationService';
import MaterialUsageView from './MaterialUsageView.vue';

const router = useRouter();
const route = useRoute();
const bomId = ref(route.params.bomId);

// 用戶相關信息
const currentUserDepartment = ref(-1);
const isAdmin = ref(false);
const bomConfirmationStatus = ref(0);

// 權限控制
const canEditProcess = computed(() => {
  // 檢查BOM確認狀態，只有未確認狀態(0)才能編輯
  if (Number(bomConfirmationStatus.value) !== 0) {
    return false;
  }

  const isDept0 = currentUserDepartment.value === 0;
  const canEdit = !isDept0;
  return canEdit; // 非台北部部門可編輯
});

// 大工序列表
const majorProcesses = ref(['A選料', 'B開料', 'C備料', 'D塗邊', 'E製作', 'F包裝', 'G補料']);

// 部位名稱列表
const positionNames = ref([]);

// 預設的部位名稱示例
const defaultPositionNames = [
  '-1前上片皮',
  '-2後上片皮',
  '-3左側皮',
  '-4左側內'
];

// 初始化部位名稱
positionNames.value = defaultPositionNames;

// 已選擇的部位
const selectedPositions = ref([]);

// 材料用量表相關
const materialUsageVisible = ref(false);

// 打開材料用量表
const openMaterialUsage = () => {
  materialUsageVisible.value = true;
};

// 關閉材料用量表
const closeMaterialUsage = () => {
  materialUsageVisible.value = false;
  // 關閉後獲取部位名稱
  fetchPositionNames();
};

// 處理材料選擇
const handleMaterialSelected = (materials) => {
  // 這裡可以處理從材料用量表選擇的材料
  console.log('選擇的材料:', materials);
};

// 返回BOM頁面
const backToBom = () => {
  router.push({ name: 'design-bom' });
};

// 工序模態對話框相關
const processModalVisible = ref(false);
const selectedPosition = ref('');

// CSS 轉義函數，用於轉義 CSS 選擇器中的特殊字符
const escapeCSSSelector = (str) => {
  if (!str) return '';
  return str.toString().replace(/[\!\"\#\$\%\&\'\(\)\*\+\,\.\/\:\;\<\=\>\?\@\[\\\]\^\`\{\|\}\~]/g, '\\$&');
};

// 工序表單數據
const processForm = ref({
  pieceName: '',
  partStructure: '',
  pieceStructure: '',
  material: '',
  order: '',
  majorProcess: '',
  minorProcess: '',
  tool: '',
  consumable: '',
  quantity: 1
});

// 已選擇的已完成工序
const selectedCompletedProcess = ref('');

// 用來追蹤每個大工序的計數器
const processCounters = ref({});

// 產生後序號
const generatedProcessCode = ref('');

// 監聽大工序和小工序的變化，更新產生後序號
watch([() => processForm.value.majorProcess, () => processForm.value.minorProcess, () => processForm.value.order,
       () => processForm.value.partStructure, () => processForm.value.pieceStructure],
  ([newMajorProcess, newMinorProcess, newOrder, newPartStructure, newPieceStructure]) => {
    if (newMajorProcess && newMinorProcess && newOrder && newPartStructure && newPieceStructure) {
      // 生成序號格式：部位組織代碼 + 分片組織代碼 + 順序號 + 大工序代號 + 小工序代碼 + '-' + 計數
      // 大工序代號已經包含在 newMajorProcess 的第一個字符中
      const majorProcessKey = newMajorProcess.charAt(0);

      // 初始化計數器
      if (!processCounters.value[majorProcessKey]) {
        processCounters.value[majorProcessKey] = 1;
      }

      generatedProcessCode.value = `${newPartStructure.code}${newPieceStructure.code}${newOrder}${majorProcessKey}${newMinorProcess.code}-${processCounters.value[majorProcessKey]}`;
    } else {
      generatedProcessCode.value = '';
    }
  }
);

// 選擇部位來開始工序
const toggleSelectPosition = (position) => {
  // 打開工序選擇模態對話框
  selectedPosition.value = position;
  processForm.value.pieceName = position; // 預設分片名稱為部位名稱
  processModalVisible.value = true;

  // 重置限制
  currentProcessRestriction.value = null;

  // 重置來源工序 ID
  selectedProcessId.value = null;
};

// 從現有工序選擇下一個工序
const currentProcessRestriction = ref(null);
const selectedProcessId = ref(null);

// 計算屬性：判斷當前選擇的工序是否已完成
const isCurrentProcessCompleted = computed(() => {
  if (!selectedProcessId.value) return false;

  const selectedProcess = savedProcesses.value.find(p => p.id === selectedProcessId.value);
  return selectedProcess ? selectedProcess.isCompleted : false;
});

// 計算屬性：判斷當前選擇的工序是否是合併的
const isCurrentProcessMerged = computed(() => {
  if (!selectedProcessId.value) return false;

  const selectedProcess = savedProcesses.value.find(p => p.id === selectedProcessId.value);
  return selectedProcess ? (selectedProcess.originalSources && selectedProcess.originalSources.length > 0) : false;
});

// 計算屬性：已完成的工序列表
const completedProcesses = computed(() => {
  return savedProcesses.value.filter(process => process.isCompleted);
});

// 處理已完成工序選擇變更
const handleCompletedProcessChange = () => {
  if (selectedCompletedProcess.value) {
    // 如果選擇了已完成的工序，將其信息填入到表單中
    const process = selectedCompletedProcess.value;

    // 將選擇的已完成工序設置為綠色
    const completedProcessElement = document.querySelector(`.process-item[data-id="${process.id}"]`);
    if (completedProcessElement) {
      completedProcessElement.classList.add('merged');
    }

    // 如果有選擇的工序 ID，就將連接線更新為從已完成工序到當前工序
    if (selectedProcessId.value) {
      // 找到當前工序的索引
      const currentProcessIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);

      // 找到被合併的工序的索引
      const mergedProcessIndex = savedProcesses.value.findIndex(p => p.id === process.id);

      if (currentProcessIndex !== -1 && mergedProcessIndex !== -1) {
        // 儲存原本的來源工序 ID 或來源材料名稱
        const originalSourceProcessId = savedProcesses.value[currentProcessIndex].sourceProcessId;
        const originalSourcePosition = savedProcesses.value[currentProcessIndex].sourcePosition;

        // 儲存原本的完成狀態
        const originalIsCompleted = savedProcesses.value[currentProcessIndex].isCompleted;

        // 儲存被合併的工序的完成狀態
        const mergedProcessIsCompleted = savedProcesses.value[mergedProcessIndex].isCompleted;

        // 創建或更新原始來源陣列
        if (!savedProcesses.value[currentProcessIndex].originalSources) {
          savedProcesses.value[currentProcessIndex].originalSources = [];
        }

        // 將原本的來源信息和完成狀態添加到原始來源陣列中
        if (originalSourceProcessId) {
          savedProcesses.value[currentProcessIndex].originalSources.push({
            sourceProcessId: originalSourceProcessId,
            sourcePosition: null,
            isCompleted: originalIsCompleted,
            mergedProcessId: process.id,
            mergedProcessIsCompleted: mergedProcessIsCompleted
          });
        } else if (originalSourcePosition) {
          savedProcesses.value[currentProcessIndex].originalSources.push({
            sourceProcessId: null,
            sourcePosition: originalSourcePosition,
            isCompleted: originalIsCompleted,
            mergedProcessId: process.id,
            mergedProcessIsCompleted: mergedProcessIsCompleted
          });
        }

        // 保留原始的 originalSourceProcessId 和 originalSourcePosition 屬性，以保持向後兼容
        if (originalSourceProcessId) {
          savedProcesses.value[currentProcessIndex].originalSourceProcessId = originalSourceProcessId;
        } else if (originalSourcePosition) {
          savedProcesses.value[currentProcessIndex].originalSourcePosition = originalSourcePosition;
        }

        // 更新當前工序的來源工序 ID
        savedProcesses.value[currentProcessIndex].sourceProcessId = process.id;

        // 取消當前工序的階段完成狀態
        savedProcesses.value[currentProcessIndex].isCompleted = false;

        // 取消被合併的工序的階段完成狀態
        savedProcesses.value[mergedProcessIndex].isCompleted = false;

        // 保存到 localStorage
        saveProcessesToLocalStorage();

        // 更新連接線
        nextTick(() => {
          updateConnections();
        });

        // 顯示成功訊息
        showSuccess(`已將工序 ${process.pieceName} 合併到當前工序，並取消兩個工序的階段完成狀態`);

        // 關閉模態對話框
        closeProcessModal();
      }
    }
  }
};

const selectNextProcess = (process) => {
  // 打開工序選擇模態對話框
  selectedPosition.value = process.pieceName;
  processForm.value.pieceName = process.pieceName; // 預設分片名稱為原本的分片名稱
  processModalVisible.value = true;

  // 不再設置大工序限制，可以選擇任何大工序
  currentProcessRestriction.value = null;

  // 記錄來源工序的ID
  selectedProcessId.value = process.id;

  // 如果上一個工序有填入的內容，將其內容填入到表單中
  if (process.partStructure) {
    // 將部位組織填入到表單中
    const partStructure = partStructureList.value.find(p => p.code === process.partStructure);
    if (partStructure) {
      processForm.value.partStructure = partStructure;
    }
  }

  if (process.pieceStructure) {
    // 將分片組織填入到表單中
    const pieceStructureCode = process.pieceStructure;

    // 先從所有分片組織中查找
    const allPieceStructures = Object.values(pieceStructureList.value).flat();
    const pieceStructure = allPieceStructures.find(p => p.code === pieceStructureCode);

    if (pieceStructure) {
      processForm.value.pieceStructure = pieceStructure;
    } else {
      // 如果在所有分片組織中找不到，則嘗試從特定部位組織中查找
      const partStructureCode = process.partStructure;

      if (partStructureCode && pieceStructureList.value[partStructureCode]) {
        const pieceStructureFromPart = pieceStructureList.value[partStructureCode].find(p => p.code === pieceStructureCode);
        if (pieceStructureFromPart) {
          processForm.value.pieceStructure = pieceStructureFromPart;
        }
      }
    }
  }

  if (process.order) {
    // 將順序填入到表單中
    processForm.value.order = process.order;
  }

  if (process.material) {
    // 將材料填入到表單中
    processForm.value.material = process.material;
  }
};

// 關閉工序模態對話框
const closeProcessModal = () => {
  processModalVisible.value = false;
  // 重置表單數據
  processForm.value = {
    pieceName: '',
    partStructure: '',
    pieceStructure: '',
    material: '',
    order: '',
    majorProcess: '',
    minorProcess: '',
    tool: '',
    consumable: '',
    quantity: 1
  };
  // 重置計數器
  processCounters.value = {};

  // 重置限制和來源工序 ID
  currentProcessRestriction.value = null;
  selectedProcessId.value = null;

  // 重置已選擇的已完成工序
  selectedCompletedProcess.value = '';
};

// 已保存的工序列表
const savedProcesses = ref([]);

// 將工序列表保存到 localStorage
const saveProcessesToLocalStorage = () => {
  try {
    localStorage.setItem(`processPreparation_${bomId.value}`, JSON.stringify(savedProcesses.value));
  } catch (error) {
    console.error('將工序列表保存到 localStorage 時發生錯誤:', error);
  }
};

// 從 localStorage 加載工序列表
const loadProcessesFromLocalStorage = () => {
  try {
    const savedData = localStorage.getItem(`processPreparation_${bomId.value}`);
    if (savedData) {
      savedProcesses.value = JSON.parse(savedData);
      // 確保每個工序都有 isCompleted 屬性
      savedProcesses.value.forEach(process => {
        if (process.isCompleted === undefined) {
          process.isCompleted = false;
        }
      });
    }
  } catch (error) {
    console.error('從 localStorage 加載工序列表時發生錯誤:', error);
  }
};

// 標記工序階段完成
const markStageComplete = () => {
  if (!selectedProcessId.value) return;

  // 找到對應的工序並標記為已完成
  const processIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);
  if (processIndex !== -1) {
    // 更新工序的完成狀態
    savedProcesses.value[processIndex].isCompleted = true;

    // 保存到 localStorage
    saveProcessesToLocalStorage();

    // 顯示成功訊息
    showSuccess(`工序 ${savedProcesses.value[processIndex].pieceName} 已標記為完成`);

    // 關閉模態對話框
    closeProcessModal();
  }
};

// 取消標記工序階段完成
const markStageIncomplete = () => {
  if (!selectedProcessId.value) return;

  // 找到對應的工序並標記為未完成
  const processIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);
  if (processIndex !== -1) {
    // 更新工序的完成狀態
    savedProcesses.value[processIndex].isCompleted = false;

    // 保存到 localStorage
    saveProcessesToLocalStorage();

    // 顯示成功訊息
    showSuccess(`工序 ${savedProcesses.value[processIndex].pieceName} 已取消階段完成標記`);

    // 關閉模態對話框
    closeProcessModal();
  }
};

// 清除所有工序
const clearAllProcesses = () => {
  if (confirm('確定要清除所有工序嗎？此操作無法撤銷。')) {
    savedProcesses.value = [];
    saveProcessesToLocalStorage();
    showSuccess('已清除所有工序');

    // 重置計數器
    processCounters.value = {};

    // 更新連接線
    nextTick(() => {
      updateConnections();
    });
  }
};



// 刪除當前工序
const deleteCurrentProcess = () => {
  if (!selectedProcessId.value) return;

  if (confirm('確定要刪除這個工序嗎？此操作無法撤銷。')) {
    // 找到要刪除的工序索引
    const processIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);

    if (processIndex !== -1) {
      // 從工序列表中刪除該工序
      savedProcesses.value.splice(processIndex, 1);

      // 保存到 localStorage
      saveProcessesToLocalStorage();

      // 顯示成功訊息
      showSuccess('工序已成功刪除');

      // 關閉模態對話框
      closeProcessModal();

      // 更新連接線
      nextTick(() => {
        updateConnections();
      });
    }
  }
};

// 取消合併
const cancelMerge = () => {
  if (!selectedProcessId.value) return;

  if (confirm('確定要取消合併嗎？此操作無法撤銷。')) {
    // 找到當前工序
    const processIndex = savedProcesses.value.findIndex(p => p.id === selectedProcessId.value);

    if (processIndex !== -1) {
      const process = savedProcesses.value[processIndex];

      // 檢查是否有原始來源
      if (process.originalSources && process.originalSources.length > 0) {
        // 取最後一個原始來源（即合併前的來源）
        const lastSource = process.originalSources[process.originalSources.length - 1];

        // 將來源設回合併前的狀態
        if (lastSource.sourceProcessId) {
          process.sourceProcessId = lastSource.sourceProcessId;
          process.sourcePosition = null;
        } else if (lastSource.sourcePosition) {
          process.sourcePosition = lastSource.sourcePosition;
          process.sourceProcessId = null;
        }

        // 恢復工序的完成狀態（如果有存儲）
        if (lastSource.isCompleted !== undefined) {
          process.isCompleted = lastSource.isCompleted;
        }

        // 如果有合併的工序 ID，則恢復其完成狀態
        if (lastSource.mergedProcessId && lastSource.mergedProcessIsCompleted !== undefined) {
          // 找到合併的工序
          const mergedProcessIndex = savedProcesses.value.findIndex(p => p.id === lastSource.mergedProcessId);
          if (mergedProcessIndex !== -1) {
            savedProcesses.value[mergedProcessIndex].isCompleted = lastSource.mergedProcessIsCompleted;
          }
        }

        // 移除最後一個原始來源
        process.originalSources.pop();

        // 如果原始來源陣列為空，則移除它
        if (process.originalSources.length === 0) {
          delete process.originalSources;
        }

        // 保存到 localStorage
        saveProcessesToLocalStorage();

        // 顯示成功訊息
        showSuccess('已成功取消合併');

        // 更新連接線
        nextTick(() => {
          updateConnections();
        });

        // 關閉模態對話框
        closeProcessModal();
      }
    }
  }
};

// 保存工序
const saveProcess = () => {
  // 驗證必填欄位
  if (!processForm.value.pieceName) {
    showError('請輸入分片名稱');
    return;
  }

  // 在這裡實現保存工序的邏輯
  // 先模擬保存成功
  showSuccess('工序已成功保存');

  // 創建新的工序項目
  const newProcess = {
    id: Date.now(), // 使用時間戳作為臨時ID
    pieceName: processForm.value.pieceName,
    majorProcess: processForm.value.majorProcess,
    minorProcess: processForm.value.minorProcess,
    sequenceNumber: generatedProcessCode.value,
    sourcePosition: selectedPosition.value,
    tool: processForm.value.tool,
    consumable: processForm.value.consumable,
    quantity: processForm.value.quantity,
    // 不再使用 currentProcessRestriction，直接記錄來源工序的ID
    sourceProcess: null,
    // 如果有選擇工序，則記錄來源工序的ID
    sourceProcessId: selectedProcessId.value,
    // 添加完成狀態標記
    isCompleted: false,
    // 保存部位組織、分片組織、順序和材料等信息
    partStructure: processForm.value.partStructure ? processForm.value.partStructure.code : null,
    pieceStructure: processForm.value.pieceStructure ? processForm.value.pieceStructure.code : null,
    order: processForm.value.order || null,
    material: processForm.value.material || null
  };

  // 將新工序添加到已保存的工序列表中
  savedProcesses.value.push(newProcess);

  // 保存到 localStorage
  saveProcessesToLocalStorage();

  // 增加計數器
  if (processForm.value.majorProcess) {
    // 大工序代號已經包含在 majorProcess 的第一個字符中
    const majorProcessKey = processForm.value.majorProcess.charAt(0);
    if (processCounters.value[majorProcessKey]) {
      processCounters.value[majorProcessKey]++;
    }
  }

  // 關閉模態對話框
  closeProcessModal();
};

// 静態數據
// 部位組織列表
const partStructureList = ref([
  { code: 'A', name: '袋蓋' },
  { code: 'B', name: '前身' },
  { code: 'C', name: '後身' },
  { code: 'D', name: '側片' },
  { code: 'E', name: '底片' },
  { code: 'F', name: '內身' },
  { code: 'G', name: '附件' },
  { code: 'H', name: '背帶' },
  { code: 'I', name: '百褚' }
]);

// 分片組織列表
const pieceStructureList = ref({
  'A': [
    { code: 'U', name: '上' }
  ],
  'B': [
    { code: 'D', name: '下' }
  ],
  'C': [
    { code: 'L', name: '左' }
  ],
  'D': [
    { code: 'R', name: '右' }
  ],
  'E': [
    { code: 'M', name: '中' }
  ],
  'F': [
    { code: '/', name: '身片' }
  ],
  'G': [
    { code: '#', name: '共用分片' }
  ],
  'H': [
    { code: '+', name: '組合' }
  ],
  'I': []
});

// 材料列表
const materialList = ref([
  { name: 'A牛' },
  { name: '裡布' }
]);

// 大工序列表
// 使用 majorProcesses 代替 majorProcessList
// const majorProcessList = ref(['A選料', 'B開料', 'C備料', 'D塗邊', 'E製作', 'F包裝', 'G補料']);

// 小工序列表
const minorProcessList = ref({
  'A選料': [
    { code: '01', name: '選皮' },
    { code: '02', name: '畫皮' }
  ],
  'B開料': [
    { code: '01', name: '電裁' },
    { code: '02', name: '裁斷' },
    { code: '03', name: '手裁' }
  ],
  'C備料': [
    { code: '01', name: '起皮' },
    { code: '02', name: '燒金' },
    { code: '03', name: '烤印' },
    { code: '04', name: '削邊' }
  ],
  'D塗邊': [
    { code: '01', name: '塗填充' },
    { code: '02', name: '邊油上色' }
  ],
  'E製作': [
    { code: '01', name: '手工上膠(刷膠)' },
    { code: '02', name: '機器上膠(噴膠)' },
    { code: '03', name: '貼合(搭接,滾平)' },
    { code: '04', name: '畫線' },
    { code: '05', name: '打磨' },
    { code: '06', name: '補色' },
    { code: '07', name: '折邊' },
    { code: '08', name: '包邊(法國滾)' },
    { code: '09', name: '拉邊' },
    { code: '10', name: '撿角' },
    { code: '11', name: '壓線' },
    { code: '12', name: '燒線' },
    { code: '13', name: '手縫' },
    { code: '14', name: '收線' },
    { code: '15', name: '車縫' },
    { code: '16', name: '手削薄' },
    { code: '17', name: '手工修邊' },
    { code: '18', name: '機器修邊' },
    { code: '19', name: '裁實模' },
    { code: '20', name: '塑型' },
    { code: '21', name: '燒熱' },
    { code: '22', name: '分條' },
    { code: '23', name: '沖孔,打洞' },
    { code: '24', name: 'A釘五金製作中' },
    { code: '25', name: 'B釘五金製作後' },
    { code: '26', name: '整理' }
  ],
  'F包裝': [
    { code: '01', name: '準備前製' },
    { code: '02', name: '品檢' },
    { code: '03', name: '包前(裝箱)' }
  ],
  'G補料': [
    { code: '01', name: '補分片' }
  ]
});

// 工具列表
const toolList = ref({
  'A選料-01': ['電裁機'],
  'A選料-02': ['銀筆'],
  'B開料-01': ['電裁機'],
  'B開料-02': ['小沖台', '大沖台'],
  'C備料-01': ['起皮機'],
  'C備料-02': ['烤印機'],
  'C備料-03': ['烤印機'],
  'C備料-04': ['削邊機'],
  'D塗邊-01': ['手工填縫', '邊油機'],
  'D塗邊-02': ['邊油機'],
  'E製作-01': ['上膠刷'],
  'E製作-02': ['噴膠機'],
  'E製作-03': ['滾平機', '一段輪'],
  'E製作-04': ['銀筆'],
  'E製作-05': ['手持式打粗機'],
  'E製作-07': ['折尺'],
  'E製作-08': ['669機台'],
  'E製作-09': ['美工刀'],
  'E製作-10': ['錫子'],
  'E製作-11': ['壓線機'],
  'E製作-12': ['電烤鐵'],
  'E製作-13': ['手縫針'],
  'E製作-14': ['紗剪'],
  'E製作-15': ['669機台', '869機台', 'JUKI平車', '包邊機'],
  'E製作-16': ['裁刀'],
  'E製作-17': ['裁刀'],
  'E製作-18': ['修邊機'],
  'E製作-19': ['沖台'],
  'E製作-20': ['烤箱'],
  'E製作-21': ['燒斗'],
  'E製作-22': ['分條機'],
  'E製作-23': ['六分頭', '圓沖,鐵鉗,斬版'],
  'E製作-24': ['沖模,鐵鍊'],
  'E製作-25': ['模具,鐵鍊']
});

// 耗材列表
const consumableList = ref({
  'C備料-02': ['燒金紙'],
  'D塗邊-01': ['填縫劑'],
  'D塗邊-02': ['各色邊油'],
  'E製作-01': ['無苯黃膠'],
  'E製作-02': ['水性白膠'],
  'E製作-05': ['鑽石磨棒', '砂紙磨頭'],
  'E製作-06': ['邊油'],
  'E製作-08': ['車線'],
  'E製作-10': ['膠水'],
  'E製作-13': ['車線'],
  'E製作-15': ['車針,車線', '車針,車線', '車針,車線', '車針,車線'],
  'E製作-18': ['膠版'],
  'E製作-19': ['膠版'],
  'E製作-24': ['膠版'],
  'E製作-25': ['膠版'],
  'F包裝-01': ['米紙'],
  'F包裝-03': ['防塵袋']
});

// 計算屬性：過濾後的大工序列表
const filteredMajorProcesses = computed(() => {
  // 不管什麼階段，都可以選擇所有的大工序
  return majorProcesses.value;
});

// 計算屬性：過濾後的小工序列表
const filteredMinorProcesses = computed(() => {
  if (processForm.value.majorProcess) {
    return minorProcessList.value[processForm.value.majorProcess] || [];
  }
  return [];
});

// 計算屬性：過濾後的分片組織列表
const filteredPieceStructures = computed(() => {
  // 將 pieceStructureList 物件的所有值（陣列）合併成一個單一陣列
  const allPieces = Object.values(pieceStructureList.value).flat();
  const uniquePieces = [];
  const seenCodes = new Set();
  for (const piece of allPieces) {
    if (!seenCodes.has(piece.code)) {
      uniquePieces.push(piece);
      seenCodes.add(piece.code);
    }
  }
  return uniquePieces;
});

// 計算當前工序可用的工具選項
const toolOptions = computed(() => {
  if (processForm.value.majorProcess && processForm.value.minorProcess) {
    const processKey = `${processForm.value.majorProcess}-${processForm.value.minorProcess.code}`;
    return toolList.value[processKey] || [];
  }
  return [];
});

// 計算當前工序可用的耗材選項
const consumableOptions = computed(() => {
  if (processForm.value.majorProcess && processForm.value.minorProcess) {
    const processKey = `${processForm.value.majorProcess}-${processForm.value.minorProcess.code}`;
    return consumableList.value[processKey] || [];
  }
  return [];
});

// 處理大工序變更
const handleMajorProcessChange = () => {
  // 重置小工序、工具和耗材
  processForm.value.minorProcess = '';
  processForm.value.tool = '';
  processForm.value.consumable = '';
};

// 處理小工序變更
const handleMinorProcessChange = () => {
  // 重置工具和耗材
  processForm.value.tool = '';
  processForm.value.consumable = '';
};

// 處理部位組織變更
const handlePartStructureChange = () => {
  // 重置分片組織
  processForm.value.pieceStructure = '';
};

// 獲取部位名稱
const fetchPositionNames = async () => {
  try {
    // 獲取所有部位名稱（主料、副料和其他）
    const allPositions = [];

    // 獲取主料部位名稱
    const mainResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '主料'
    });

    if (mainResponse && mainResponse.status === 'success' && Array.isArray(mainResponse.data)) {
      const mainPositions = mainResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...mainPositions);
    }

    // 獲取副料部位名稱
    const subResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '副料'
    });

    if (subResponse && subResponse.status === 'success' && Array.isArray(subResponse.data)) {
      const subPositions = subResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...subPositions);
    }

    // 獲取其他部位名稱
    const otherResponse = await api.materialDetailSheet.getItems({
      bomId: bomId.value,
      sheetType: '其他'
    });

    if (otherResponse && otherResponse.status === 'success' && Array.isArray(otherResponse.data)) {
      const otherPositions = otherResponse.data
        .map(item => item.position_name)
        .filter(name => name && name.trim() !== '');
      allPositions.push(...otherPositions);
    }

    // 去重
    const uniquePositions = [...new Set(allPositions)];

    // 更新部位名稱列表
    positionNames.value = uniquePositions;
  } catch (error) {
    console.error('獲取部位名稱失敗:', error);
    showError('獲取部位名稱失敗');
  }
};

// 獲取當前用戶信息
const fetchCurrentUser = async () => {
  try {
    const response = await api.getCurrentUser();
    if (response.status === 'success' && response.data && response.data.employee) {
      currentUserDepartment.value = parseInt(response.data.employee.department);
      isAdmin.value = response.data.employee.role === 0;
    } else {
      // 如果API獲取失敗，嘗試從localStorage獲取
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          currentUserDepartment.value = parseInt(user.department);
          isAdmin.value = user.role === 0;
        } catch (error) {
          console.error('解析用戶資訊出錯:', error);
        }
      }
    }
  } catch (error) {
    console.error('獲取使用者資訊時發生錯誤:', error);
    // 嘗試從localStorage獲取
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        currentUserDepartment.value = parseInt(user.department);
        isAdmin.value = user.role === 0;
      } catch (error) {
        console.error('解析用戶資訊出錯:', error);
      }
    }
  }
};

// 獲取BOM資訊，檢查確認狀態
const fetchBomInfo = async () => {
  try {
    if (!bomId.value) return;

    const response = await api.bom.getById(bomId.value);

    if (response.status === 'success' && response.data) {
      bomConfirmationStatus.value = Number(response.data.confirmationStatus || 0);

      // 如果BOM不是未確認狀態，顯示警告
      if (bomConfirmationStatus.value !== 0) {
        showWarning('注意：此BOM已經被確認，無法進行編輯操作。');
      }
    }
  } catch (error) {
    console.error('獲取BOM資訊失敗:', error);
    showWarning('無法獲取BOM確認狀態');
  }
};

// 根據大工序獲取工序列表
const getProcessesByMajorProcess = (majorProcess) => {
  return savedProcesses.value.filter(process => process.majorProcess === majorProcess);
};

// 獲取工序在大工序中的順序序號（01, 02, 03...)
const getProcessIndex = (process, majorProcess) => {
  if (!process || !majorProcess) return '';

  // 獲取同一個大工序中，相同材料來源的工序
  const processes = getProcessesByMajorProcess(majorProcess);
  const sameSourceProcesses = processes.filter(p => p.sourcePosition === process.sourcePosition);
  
  // 按照ID排序，確保工序按照創建順序排列
  sameSourceProcesses.sort((a, b) => a.id - b.id);
  
  // 在相同材料來源的工序中找到當前工序的索引
  const index = sameSourceProcesses.findIndex(p => p.id === process.id);
  if (index === -1) return '';

  // 返回兩位數格式的序號
  return (index + 1).toString().padStart(2, '0');
};

// 繪製連接線
const connectionsContainer = ref(null);

// 更新連接線
const updateConnections = () => {
  if (!connectionsContainer.value) return;

  // 清除現有的連接線
  connectionsContainer.value.innerHTML = '';

  // 確保連接線容器的 z-index 為 5
  connectionsContainer.value.style.zIndex = '5';

  // 先創建一個 defs 元素來存放所有的箭頭
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  defs.style.overflow = 'visible'; // 確保 defs 元素可以正確顯示 z-index
  connectionsContainer.value.appendChild(defs);

  // 將所有工序的來源信息收集到一個 Map 中
  const processSourceMap = new Map();

  // 先記錄每個工序的來源
  savedProcesses.value.forEach(process => {
    // 創建一個陣列來存放所有的來源
    const sources = [];

    // 添加主要來源
    if (process.sourceProcessId) {
      // 如果是從已有工序選擇的，記錄來源工序 ID
      sources.push({ sourceProcessId: process.sourceProcessId, sourcePosition: null });
    } else if (process.sourcePosition) {
      // 如果是從原始材料選擇的，記錄來源材料名稱
      sources.push({ sourceProcessId: null, sourcePosition: process.sourcePosition });
    }

    // 添加原始來源陣列中的所有來源（如果有）
    if (process.originalSources && Array.isArray(process.originalSources)) {
      process.originalSources.forEach(source => {
        sources.push(source);
      });
    }
    // 向後兼容，處理舊版本的原始來源屬性
    else if (process.originalSourceProcessId) {
      sources.push({ sourceProcessId: process.originalSourceProcessId, sourcePosition: null });
    } else if (process.originalSourcePosition) {
      sources.push({ sourceProcessId: null, sourcePosition: process.originalSourcePosition });
    }

    // 將所有來源記錄到 Map 中
    processSourceMap.set(process.id, sources);
  });

  // 為每個工序項目繪製連接線
  savedProcesses.value.forEach(process => {
    // 找到目標工序元素
    const targetElements = document.querySelectorAll(`.process-item[data-id="${process.id}"]`);
    if (targetElements.length === 0) return; // 如果沒有目標元素，則跳過

    const targetElement = targetElements[0];
    const targetRect = targetElement.getBoundingClientRect();
    const containerRect = connectionsContainer.value.getBoundingClientRect();
    const targetX = targetRect.right - containerRect.left;
    const targetY = targetRect.top + targetRect.height / 2 - containerRect.top;

    // 從記錄中獲取來源信息
    const sources = processSourceMap.get(process.id) || [];

    // 為每個來源繪製連接線
    sources.forEach(sourceInfo => {
      let sourceElement = null;

      if (sourceInfo.sourceProcessId) {
        // 如果是從已有工序選擇的，找到來源工序元素
        const sourceProcessElements = document.querySelectorAll(`.process-item[data-id="${sourceInfo.sourceProcessId}"]`);
        if (sourceProcessElements.length > 0) {
          sourceElement = sourceProcessElements[0];
        }
      } else if (sourceInfo.sourcePosition) {
        // 如果是從原始材料選擇的，找到源材料元素
        const sourceElements = document.querySelectorAll(`.position-item:not(.selected)`);

        for (const element of sourceElements) {
          if (element.textContent.trim() === sourceInfo.sourcePosition) {
            sourceElement = element;
            break;
          }
        }
      }

      // 如果沒有找到來源元素，則跳過
      if (!sourceElement) return;

      // 獲取元素的位置
      const sourceRect = sourceElement.getBoundingClientRect();

      // 計算相對位置 - 修正為從右側工序的左邊緣開始
      const sourceX = sourceRect.left - containerRect.left;
      const sourceY = sourceRect.top + sourceRect.height / 2 - containerRect.top;

      // 創建連接線的分組
      const lineGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
      lineGroup.classList.add('process-connection-line');
      lineGroup.setAttribute('data-source-id', sourceInfo.sourceProcessId || '');
      lineGroup.setAttribute('data-target-id', process.id);

      // 添加唯一的 ID，使用來源和目標的組合
      const sourceId = sourceInfo.sourceProcessId || sourceInfo.sourcePosition;
      const rawConnectionId = `connection-line-${process.id}-${sourceId}`;
      // 使用轉義函數確保 ID 中的特殊字符被正確轉義
      const connectionId = rawConnectionId;
      lineGroup.setAttribute('id', connectionId);

      // 創建連接線
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      line.setAttribute('d', `M${sourceX},${sourceY} C${sourceX + (targetX - sourceX) / 2},${sourceY} ${sourceX + (targetX - sourceX) / 2},${targetY} ${targetX},${targetY}`);
      line.setAttribute('stroke', '#e2e8f0'); // 設置為非常淡的顏色
      line.setAttribute('stroke-width', '2');
      line.setAttribute('fill', 'none');

      // 設置連接線的 z-index 為 5
      lineGroup.style.zIndex = '5';

      // 將連接線添加到分組中
      lineGroup.appendChild(line);

      // 添加箭頭
      // 為每個連接線創建唯一的箭頭 ID
      // 使用簡單的數字 ID 避免特殊字符問題
      const arrowheadId = `arrowhead-${process.id}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // 檢查是否已存在相同 ID 的箭頭
      const existingMarker = document.getElementById(arrowheadId);
      if (!existingMarker) {
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', arrowheadId);
        marker.setAttribute('markerWidth', '10');
        marker.setAttribute('markerHeight', '7');
        marker.setAttribute('refX', '9');
        marker.setAttribute('refY', '3.5');
        marker.setAttribute('orient', 'auto');
        marker.setAttribute('z-index', '5'); // 設置箭頭的 z-index 為 5
        marker.style.overflow = 'visible'; // 確保 marker 元素可以正確顯示 z-index
        marker.style.zIndex = '5'; // 同時設置 style 屬性

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        polygon.setAttribute('points', '0 0, 10 3.5, 0 7');
        polygon.setAttribute('fill', '#e2e8f0'); // 設置為非常淡的顏色

        marker.appendChild(polygon);

        // 添加到已存在的 defs 元素中
        const defs = connectionsContainer.value.querySelector('defs');
        if (defs) {
          defs.appendChild(marker);
        }
      }

      // 使用唯一的箭頭 ID
      line.setAttribute('marker-end', `url(#${arrowheadId})`);

      // 將分組添加到 SVG 容器
      connectionsContainer.value.appendChild(lineGroup);
    });
  });
};

// 監聽 savedProcesses 的變化，更新連接線
watch(savedProcesses, () => {
  // 使用 nextTick 確保 DOM 已更新
  nextTick(() => {
    updateConnections();
  });
}, { deep: true });

// 頁面加載時初始化
onMounted(async () => {
  // 獲取當前用戶信息
  await fetchCurrentUser();

  if (!bomId.value) {
    // 如果沒有指定bomId，嘗試從localStorage獲取
    const storedBomId = localStorage.getItem('currentBomId');
    if (storedBomId) {
      bomId.value = storedBomId;
    } else {
      // 如果仍然沒有bomId，返回到BOM頁面
      showWarning('無法獲取BOM ID，請重新選擇BOM');
      router.push({ name: 'design-bom' });
      return;
    }
  }

  // 獲取BOM信息，檢查確認狀態
  await fetchBomInfo();

  // 獲取部位名稱
  await fetchPositionNames();

  // 從 localStorage 加載工序列表
  loadProcessesFromLocalStorage();

  // 初始化連接線和事件監聽
  nextTick(() => {
    updateConnections();
    setupProcessItemHoverEvents();
  });
});

// 添加工序項目和材料項目的滑鼠事件監聽
const setupProcessItemHoverEvents = () => {
  // 為所有工序項目添加滑鼠事件
  const processItems = document.querySelectorAll('.process-item');

  processItems.forEach(item => {
    // 移除現有的事件監聽器，避免重複添加
    item.removeEventListener('mouseenter', handleProcessItemMouseEnter);
    item.removeEventListener('mouseleave', handleProcessItemMouseLeave);

    // 添加新的事件監聽器
    item.addEventListener('mouseenter', handleProcessItemMouseEnter);
    item.addEventListener('mouseleave', handleProcessItemMouseLeave);
  });

  // 為所有材料項目添加滑鼠事件
  const materialItems = document.querySelectorAll('.position-item');

  materialItems.forEach(item => {
    // 移除現有的事件監聽器，避免重複添加
    item.removeEventListener('mouseenter', handleMaterialItemMouseEnter);
    item.removeEventListener('mouseleave', handleMaterialItemMouseLeave);

    // 添加新的事件監聽器
    item.addEventListener('mouseenter', handleMaterialItemMouseEnter);
    item.addEventListener('mouseleave', handleMaterialItemMouseLeave);
  });
};

// 找到從材料到當前工序的所有相關連接線和工序項目
// 參數 direction: 'incoming' - 只找連入的線, 'outgoing' - 只找連出的線, 'both' - 找兩種線, 'next-level' - 找連入的線和下一層連出的線
const findAllRelatedItems = (processId, visitedIds = new Set(), direction = 'both', level = 0) => {
  // 避免循環依賴
  if (visitedIds.has(processId)) return { lines: [], processes: [] };
  visitedIds.add(processId);

  // 找到以此工序為目標的所有連接線（連入的線）
  const incomingLines = Array.from(document.querySelectorAll(`.process-connection-line[data-target-id="${processId}"]`));

  // 找到以此工序為來源的所有連接線（連出的線）
  const outgoingLines = (direction === 'incoming') ? [] : Array.from(document.querySelectorAll(`.process-connection-line[data-source-id="${processId}"]`));

  // 找到當前工序項目
  const currentProcess = document.querySelector(`.process-item[data-id="${processId}"]`);
  let relatedProcesses = currentProcess ? [currentProcess] : [];

  // 根據方向參數決定要包含的連接線
  let allRelatedLines = [];
  if (direction === 'incoming' || direction === 'both' || direction === 'next-level') {
    allRelatedLines = [...allRelatedLines, ...incomingLines];
  }
  if (direction === 'outgoing' || direction === 'both' || (direction === 'next-level' && level === 0)) {
    allRelatedLines = [...allRelatedLines, ...outgoingLines];
  }

  // 遍歷所有連入的連接線，找到它們的來源工序的相關連接線和工序項目
  incomingLines.forEach(line => {
    const sourceId = line.getAttribute('data-source-id');
    if (sourceId) {
      const { lines: sourceRelatedLines, processes: sourceRelatedProcesses } = findAllRelatedItems(sourceId, visitedIds, direction, level + 1);
      allRelatedLines = [...allRelatedLines, ...sourceRelatedLines];
      relatedProcesses = [...relatedProcesses, ...sourceRelatedProcesses];
    }
  });

  // 如果方向包含 outgoing，才遍歷連出的連接線
  if (direction === 'outgoing' || direction === 'both' || (direction === 'next-level' && level === 0)) {
    outgoingLines.forEach(line => {
      const targetId = line.getAttribute('data-target-id');
      if (targetId) {
        // 如果是 next-level 模式，只收集下一層的工序，不進一步遍歷
        if (direction === 'next-level') {
          const targetProcess = document.querySelector(`.process-item[data-id="${targetId}"]`);
          if (targetProcess) {
            relatedProcesses.push(targetProcess);
          }
        } else {
          const { lines: targetRelatedLines, processes: targetRelatedProcesses } = findAllRelatedItems(targetId, visitedIds, direction, level + 1);
          allRelatedLines = [...allRelatedLines, ...targetRelatedLines];
          relatedProcesses = [...relatedProcesses, ...targetRelatedProcesses];
        }
      }
    });
  }

  return { lines: allRelatedLines, processes: relatedProcesses };
};

// 滑鼠移入工序項目時的處理函數
const handleProcessItemMouseEnter = (event) => {
  const processId = event.currentTarget.getAttribute('data-id');
  if (!processId) return;

  // 找到從材料到當前工序的所有相關連接線和工序項目
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  const { lines: relatedLines, processes: relatedProcesses } = findAllRelatedItems(processId, new Set(), 'next-level');

  // 將所有連接線設置為非高亮狀態並加上半透明效果
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    line.style.zIndex = '5'; // 設置為預設層級 5
    line.classList.add('faded'); // 加上半透明效果

    // 確保所有箭頭也設置為預設層級
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          if (marker) {
            marker.style.zIndex = '5';
          }
        }
      }
    }
  });

  // 將所有工序項目設置為非高亮狀態並加上半透明效果
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    item.classList.remove('highlighted');
    item.classList.add('faded'); // 加上半透明效果
  });

  // 將相關的連接線移動到 SVG 容器的最後，確保它們在最後渲染
  relatedLines.forEach(line => {
    // 將連接線元素移動到其父元素的最後
    const parent = line.parentNode;
    if (parent) {
      // 將連接線元素移動到 SVG 容器的最後
      const svg = connectionsContainer.value;
      if (svg) {
        // 先將元素從原來的父元素中移除，然後添加到 SVG 容器的最後
        svg.appendChild(line);
      } else {
        parent.appendChild(line); // 如果找不到 SVG 容器，則移動到父元素的最後
      }
    }

    line.style.zIndex = '30'; // 設置為高層級 30，顯示在高亮工序項目上方

    // 確保相關的箭頭也設置為最高層
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
          if (marker) {
            marker.style.zIndex = '30';

            // 將箭頭元素移動到 defs 的最後
            const defs = connectionsContainer.value.querySelector('defs');
            if (defs) {
              defs.appendChild(marker); // 移動到最後會導致它在最後渲染
            }
          }
        }
      }
    }
  });

  // 將相關連接線的顏色變深並添加高亮類
  relatedLines.forEach(lineGroup => {
    // 添加高亮類到分組
    lineGroup.classList.add('highlighted');

    // 找到分組中的連接線元素
    const line = lineGroup.querySelector('path');
    if (line) {
      // 直接設置線條的顏色
      line.setAttribute('stroke', '#475569'); // 更深的灰色
      line.setAttribute('stroke-width', '3'); // 增加線條寬度

      // 同時變深箭頭的顏色
      const arrowheadId = line.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
          const arrowhead = document.querySelector(`#arrowhead-${arrowheadMatch[1]} polygon`);
          if (marker) {
            marker.setAttribute('z-index', '30'); // 設置箭頭的 z-index 為高層級 30
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#475569'); // 更深的灰色
          }
        }
      }
    }
  });

  // 將相關工序項目高亮
  relatedProcesses.forEach(processItem => {
    processItem.classList.add('highlighted');
  });
};

// 滑鼠移出工序項目時的處理函數
const handleProcessItemMouseLeave = (event) => {
  const processId = event.currentTarget.getAttribute('data-id');
  if (!processId) return;

  // 找到從材料到當前工序的所有相關連接線
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  const { lines: relatedLines } = findAllRelatedItems(processId, new Set(), 'next-level');

  // 將所有連接線恢復為預設狀態並移除半透明效果
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    line.style.zIndex = '5'; // 設置為預設層級 5
    line.classList.remove('faded'); // 移除半透明效果

    // 確保所有箭頭也恢復為預設狀態
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          if (marker) {
            marker.style.zIndex = '5';
          }
        }
      }
    }
  });

  // 將所有工序項目恢復為預設狀態並移除半透明效果
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    item.classList.remove('highlighted');
    item.classList.remove('faded'); // 移除半透明效果
  });

  // 將相關連接線的顏色變回淡色並移除高亮類
  relatedLines.forEach(lineGroup => {
    // 移除分組的高亮類
    lineGroup.classList.remove('highlighted');

    // 找到分組中的連接線元素
    const line = lineGroup.querySelector('path');
    if (line) {
      // 直接設置線條的顏色
      line.setAttribute('stroke', '#e2e8f0'); // 淡色
      line.setAttribute('stroke-width', '2'); // 恢復原始寬度

      // 同時變回箭頭的淡色
      const arrowheadId = line.getAttribute('marker-end');
      if (arrowheadId) {
        const arrowheadMatch = arrowheadId.match(/url\(#arrowhead-([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const marker = document.querySelector(`#arrowhead-${arrowheadMatch[1]}`);
          const arrowhead = document.querySelector(`#arrowhead-${arrowheadMatch[1]} polygon`);
          if (marker) {
            marker.setAttribute('z-index', ''); // 移除箭頭的 z-index
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#e2e8f0');
          }
        }
      }
    }
  });
};

// 滑鼠移入材料項目時的處理函數
const handleMaterialItemMouseEnter = (event) => {
  const materialName = event.currentTarget.textContent.trim();
  if (!materialName) return;

  // 找到所有以此材料為來源的工序
  const relatedProcesses = savedProcesses.value.filter(process => process.sourcePosition === materialName);

  // 對每個相關工序，找到它的所有相關連接線和工序項目
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  let allRelatedLines = [];
  let allRelatedProcesses = [];
  relatedProcesses.forEach(process => {
    const { lines: processRelatedLines, processes: processRelatedProcesses } = findAllRelatedItems(process.id, new Set(), 'next-level');
    allRelatedLines = [...allRelatedLines, ...processRelatedLines];
    allRelatedProcesses = [...allRelatedProcesses, ...processRelatedProcesses];
  });

  // 將所有連接線設置為非高亮狀態並加上半透明效果
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    line.style.zIndex = '5'; // 設置為預設層級 5
    line.classList.add('faded'); // 加上半透明效果

    // 確保所有箭頭也設置為較低的值
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          if (marker) {
            marker.style.zIndex = '5';
          }
        }
      }
    }
  });

  // 將所有工序項目設置為非高亮狀態並加上半透明效果
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    item.classList.remove('highlighted');
    item.classList.add('faded'); // 加上半透明效果
  });

  // 將相關的連接線移動到 SVG 容器的最後，確保它們在最後渲染
  allRelatedLines.forEach(line => {
    // 將連接線元素移動到其父元素的最後
    const parent = line.parentNode;
    if (parent) {
      // 將連接線元素移動到 SVG 容器的最後
      const svg = connectionsContainer.value;
      if (svg) {
        // 先將元素從原來的父元素中移除，然後添加到 SVG 容器的最後
        svg.appendChild(line);
      } else {
        parent.appendChild(line); // 如果找不到 SVG 容器，則移動到父元素的最後
      }
    }

    line.style.zIndex = '30'; // 設置為高層級 30，顯示在高亮工序項目上方

    // 確保相關的箭頭也設置為最高層
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          if (marker) {
            marker.style.zIndex = '30';

            // 將箭頭元素移動到 defs 的最後
            const defs = connectionsContainer.value.querySelector('defs');
            if (defs) {
              defs.appendChild(marker); // 移動到最後會導致它在最後渲染
            }
          }
        }
      }
    }
  });

  // 將相關連接線的顏色變深並添加高亮類
  allRelatedLines.forEach(lineGroup => {
    // 添加高亮類到分組並移除半透明效果
    lineGroup.classList.add('highlighted');
    lineGroup.classList.remove('faded'); // 移除半透明效果

    // 找到分組中的連接線元素
    const line = lineGroup.querySelector('path');
    if (line) {
      // 直接設置線條的顏色
      line.setAttribute('stroke', '#475569'); // 更深的灰色
      line.setAttribute('stroke-width', '3'); // 增加線條寬度

      // 同時變深箭頭的顏色
      const arrowheadId = line.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          const arrowhead = marker ? marker.querySelector('polygon') : null;
          if (marker) {
            marker.setAttribute('z-index', '30'); // 設置箭頭的 z-index 為高層級 30
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#475569'); // 更深的灰色
          }
        }
      }
    }
  });

  // 將相關工序項目高亮並移除半透明效果
  allRelatedProcesses.forEach(processItem => {
    processItem.classList.add('highlighted');
    processItem.classList.remove('faded'); // 移除半透明效果
  });
};

// 滑鼠移出材料項目時的處理函數
const handleMaterialItemMouseLeave = (event) => {
  const materialName = event.currentTarget.textContent.trim();
  if (!materialName) return;

  // 找到所有以此材料為來源的工序
  const relatedProcesses = savedProcesses.value.filter(process => process.sourcePosition === materialName);

  // 對每個相關工序，找到它的所有相關連接線和工序項目
  // 找連入的線（往回的線）和下一層連出的線（往前的線）
  let allRelatedLines = [];
  let allRelatedProcesses = [];
  relatedProcesses.forEach(process => {
    const { lines: processRelatedLines, processes: processRelatedProcesses } = findAllRelatedItems(process.id, new Set(), 'next-level');
    allRelatedLines = [...allRelatedLines, ...processRelatedLines];
    allRelatedProcesses = [...allRelatedProcesses, ...processRelatedProcesses];
  });

  // 將所有連接線恢復為預設狀態並移除半透明效果
  const allLines = document.querySelectorAll('.process-connection-line');
  allLines.forEach(line => {
    line.style.zIndex = '5'; // 設置為預設層級 5
    line.classList.remove('faded'); // 移除半透明效果

    // 確保所有箭頭也恢復為預設狀態
    const path = line.querySelector('path');
    if (path) {
      const arrowheadId = path.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          if (marker) {
            marker.style.zIndex = '5';
          }
        }
      }
    }
  });

  // 將所有工序項目恢復為預設狀態並移除半透明效果
  const allProcessItems = document.querySelectorAll('.process-item');
  allProcessItems.forEach(item => {
    item.classList.remove('highlighted');
    item.classList.remove('faded'); // 移除半透明效果
  });

  // 將相關連接線的顏色變回淡色並移除高亮類
  allRelatedLines.forEach(lineGroup => {
    // 移除分組的高亮類
    lineGroup.classList.remove('highlighted');

    // 找到分組中的連接線元素
    const line = lineGroup.querySelector('path');
    if (line) {
      // 直接設置線條的顏色
      line.setAttribute('stroke', '#e2e8f0'); // 淡色
      line.setAttribute('stroke-width', '2'); // 恢復原始寬度

      // 同時變回箭頭的淡色
      const arrowheadId = line.getAttribute('marker-end');
      if (arrowheadId) {
        // 直接從 url(#id) 中提取 id
        const arrowheadMatch = arrowheadId.match(/url\(#([^)]+)\)/);
        if (arrowheadMatch && arrowheadMatch[1]) {
          const markerId = arrowheadMatch[1];
          const marker = document.getElementById(markerId);
          const arrowhead = marker ? marker.querySelector('polygon') : null;
          if (marker) {
            marker.setAttribute('z-index', ''); // 移除箭頭的 z-index
          }
          if (arrowhead) {
            arrowhead.setAttribute('fill', '#e2e8f0');
          }
        }
      }
    }
  });
};

// 監聽 savedProcesses 的變化，更新連接線和事件監聽
watch(savedProcesses, () => {
  // 使用 nextTick 確保 DOM 已更新
  nextTick(() => {
    updateConnections();
    setupProcessItemHoverEvents();
  });
}, { deep: true });

// 監聽窗口大小變化，更新連接線
onMounted(() => {
  window.addEventListener('resize', () => {
    updateConnections();
    setupProcessItemHoverEvents();
  });
});

// 結束時移除事件監聽
onUnmounted(() => {
  window.removeEventListener('resize', updateConnections);

  // 移除所有工序項目的滑鼠事件監聽器
  const processItems = document.querySelectorAll('.process-item');
  processItems.forEach(item => {
    item.removeEventListener('mouseenter', handleProcessItemMouseEnter);
    item.removeEventListener('mouseleave', handleProcessItemMouseLeave);
  });

  // 移除所有材料項目的滑鼠事件監聽器
  const materialItems = document.querySelectorAll('.position-item');
  materialItems.forEach(item => {
    item.removeEventListener('mouseenter', handleMaterialItemMouseEnter);
    item.removeEventListener('mouseleave', handleMaterialItemMouseLeave);
  });
});
</script>

<style scoped>
@import './ProcessPreparationView.css';
</style>