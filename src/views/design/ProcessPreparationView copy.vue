<template>
  <div class="process-preparation-container">
    <div class="process-header">
      <button class="add-btn" @click="openMaterialUsage" :disabled="!canEditProcess">
        <i class="fas fa-plus"></i> 材料用量表
      </button>
      <h2>備料工序</h2>
      <div class="back-to-bom" @click="backToBom">
        <i class="fas fa-arrow-left"></i>
        返回BOM
      </div>
    </div>

    <div class="process-form" v-if="canEditProcess">
      <!-- Row 1: 產生後序號 & 自訂分片名稱 -->
      <div class="form-row">
        <div class="form-group">
          <label>工序序號：</label>
          <input type="text" v-model="processCode" placeholder="工序序號" />
        </div>
        <div class="form-group">
          <label>產生後序號：</label>
          <div class="sequence-number">{{ sequenceNumber }}</div>
        </div>
        <div class="form-group">
          <label>分片名稱：<span class="red">*</span></label>
          <input type="text" v-model="customPieceName" placeholder="請輸入分片名稱" />
        </div>
      </div>

      <!-- Row 2: 大工序, 小工序, 部位組織 -->
      <div class="form-row">
        <div class="form-group">
          <label>大工序：</label>
          <select v-model="selectedMajorProcess" @change="handleMajorProcessChange">
            <option value="">請選擇大工序</option>
            <option v-for="(process, index) in majorProcessList" :key="index" :value="process">
              {{ process }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>小工序：</label>
          <select v-model="selectedMinorProcess" @change="handleMinorProcessChange" :disabled="!selectedMajorProcess">
            <option value="">請選擇小工序</option>
            <option v-for="(process, index) in filteredMinorProcesses" :key="index" :value="process">
              {{ process.code }} {{ process.name }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>部位組織：</label>
          <select v-model="selectedPartStructure" @change="handlePartStructureChange">
            <option value="">請選擇部位組織</option>
            <option v-for="(part, index) in partStructureList" :key="index" :value="part">
              {{ part.code }} {{ part.name }}
            </option>
          </select>
        </div>
      </div>

      <!-- Row 3: 分片組織, 材料, 順序 -->
      <div class="form-row">
        <div class="form-group">
          <label>分片組織：</label>
          <select v-model="selectedPieceStructure" @change="handlePieceStructureChange">
            <option value="">請選擇分片組織</option>
            <option v-for="(piece, index) in filteredPieceStructures" :key="index" :value="piece">
              {{ piece.code }} {{ piece.name }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>材料：</label>
          <select v-model="selectedMaterial">
            <option value="">請選擇材料</option>
            <option v-for="(material, index) in materialList" :key="index" :value="material">
              {{ material.name }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>順序：</label>
          <select v-model="selectedOrder" @change="handleOrderChange">
            <option value="">請選擇順序</option>
            <option v-for="n in 20" :key="n" :value="n.toString().padStart(2, '0')" :disabled="usedOrders.includes(n.toString().padStart(2, '0'))">
              {{ n.toString().padStart(2, '0') }}
            </option>
          </select>
        </div>
      </div>

      <!-- Row 4: 工具, 耗材, 組成數量 -->
      <div class="form-row">
        <div class="form-group">
          <label>工具：</label>
          <select v-model="selectedTool">
            <option value="">請選擇工具</option>
            <option v-for="(tool, index) in toolOptions" :key="index" :value="tool">
              {{ tool }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>耗材：</label>
          <select v-model="selectedConsumable">
            <option value="">請選擇耗材</option>
            <option v-for="(consumable, index) in consumableOptions" :key="index" :value="consumable">
              {{ consumable }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>組成數量：</label>
          <input type="number" v-model="quantity" min="1" step="1" class="quantity-input" />
        </div>
      </div>

      <!-- Row 5: 加工說明, 標準工時 -->
      <div class="form-row">
        <div class="form-group form-group-large">
          <label>加工說明：</label>
          <input type="text" v-model="processDescription" placeholder="請輸入加工說明" />
        </div>

        <div class="form-group">
          <label>標準工時：</label>
          <input type="number" v-model="standardTime" min="0" step="0.1" class="time-input" />
        </div>
      </div>


      <!-- Row 6: Buttons -->
      <div class="form-row form-actions">
        <button class="add-btn" @click="addProcessItem" :disabled="!canAddProcess">
          <i class="fas fa-plus"></i> 添加工序項目
        </button>

        <button class="clear-btn" @click="clearSelection">
          <i class="fas fa-times"></i> 清除選擇
        </button>
      </div>
    </div>

    <div v-if="!canEditProcess" class="read-only-notice">
      <p v-if="currentUserDepartment === 0">您所屬的部門僅可查看備料工序資料，無法進行編輯操作。</p>
      <p v-else-if="Number(bomConfirmationStatus) !== 0">此BOM已被確認，無法進行編輯操作。只有未確認狀態的BOM才能進行編輯。</p>
    </div>

    <div class="process-table-container">
      <table class="process-table">
        <thead>
          <tr>
            <th>工序序號</th>
            <th>分片名稱</th>
            <th>大工序</th>
            <th>小工序</th>
            <th>產生後序號</th>
            <th>組成數量</th>
            <th>工具</th>
            <th>耗材</th>
            <th>加工說明</th>
            <th>標準工時</th>
            <th>實際工時</th>
            <th v-if="canEditProcess">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in processItemList" :key="index">
            <td>{{ item.processCode }}</td>
            <td>{{ item.pieceName }}</td>
            <td>{{ item.majorProcess }}</td>
            <td>{{ item.minorProcess.split(' ').slice(1).join(' ') }}</td>
            <td>{{ item.sequenceNumber }}</td>
            <td>{{ item.quantity }}</td>
            <td>{{ item.tool || '-' }}</td>
            <td>{{ item.consumable || '-' }}</td>
            <td>{{ item.processDescription || '-' }}</td>
            <td>{{ item.standardTime || '0' }}</td>
            <td>{{ item.actualTime || '0' }}</td>
            <td v-if="canEditProcess">
              <button class="delete-btn" @click="deleteProcessItem(index, item.id)">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="processItemList.length === 0" class="empty-state">
        尚未添加任何備料工序項目，請使用上方表單添加
      </div>
    </div>

    <!-- 彈出材料用量表 -->
    <MaterialUsageView
      :visible="materialUsageVisible"
      :bomId="bomId"
      @close="closeMaterialUsage"
      @material-selected="handleMaterialSelected"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import api from '../../services/api';
import { showSuccess, showError, showWarning } from '../../services/notificationService';
import MaterialUsageView from './MaterialUsageView.vue';

const router = useRouter();
const route = useRoute();
const bomId = ref(route.params.bomId);

// 用戶相關信息
const currentUserDepartment = ref(-1);
const isAdmin = ref(false);
const bomConfirmationStatus = ref(0); // 新增：存儲BOM確認狀態

const canEditProcess = computed(() => {
  // 檢查BOM確認狀態，只有未確認狀態(0)才能編輯
  if (Number(bomConfirmationStatus.value) !== 0) {
    return false;
  }

  const isDept0 = currentUserDepartment.value === 0;
  const canEdit = !isDept0;
  console.log('用戶部門:', currentUserDepartment.value, '是台北部:', isDept0, '可編輯:', canEdit);
  return canEdit; // 非台北部部門可編輯
});

// 各種選擇項
const selectedMajorProcess = ref('');
const selectedMinorProcess = ref('');
const selectedPartStructure = ref('');
const selectedPieceStructure = ref('');
const selectedMaterial = ref('');
const selectedOrder = ref('');
const selectedTool = ref('');
const selectedConsumable = ref('');
const quantity = ref(1);
const processDescription = ref('');
const standardTime = ref(0);
const sequenceNumber = ref('');
const customPieceName = ref(''); // 自訂分片名稱
const processCode = ref(''); // 工序序號

// 工序項目列表
const processItemList = ref([]);
const usedOrders = ref([]);
// 用來追蹤每個大工序的計數器
const processCounters = ref({});

// 靜態資料 (實際開發時應該從API獲取)
const majorProcessList = ref(['A選料', 'B開料', 'C備料', 'D塗邊', 'E製作', 'F包裝', 'G補料']);

const minorProcessList = ref({
  'A選料': [
    { code: '01', name: '選皮' },
    { code: '02', name: '畫皮' }
  ],
  'B開料': [
    { code: '01', name: '電裁' },
    { code: '02', name: '裁斷' },
    { code: '03', name: '手裁' }
  ],
  'C備料': [
    { code: '01', name: '起皮' },
    { code: '02', name: '燙金' },
    { code: '03', name: '烙印' },
    { code: '04', name: '削邊' }
  ],
  'D塗邊': [
    { code: '01', name: '塗填充' },
    { code: '02', name: '邊油上色' }
  ],
  'E製作': [
    { code: '01', name: '手工上膠(刷膠)' },
    { code: '02', name: '機器上膠(噴膠)' },
    { code: '03', name: '貼合(搭接,滾平)' },
    { code: '04', name: '畫線' },
    { code: '05', name: '打磨' },
    { code: '06', name: '補色' },
    { code: '07', name: '摺邊' },
    { code: '08', name: '包邊(法國滾)' },
    { code: '09', name: '拉邊' },
    { code: '10', name: '撿角' },
    { code: '11', name: '壓線' },
    { code: '12', name: '燙線' },
    { code: '13', name: '手縫' },
    { code: '14', name: '收線' },
    { code: '15', name: '車縫' },
    { code: '16', name: '手削薄' },
    { code: '17', name: '手工修邊' },
    { code: '18', name: '機器修邊' },
    { code: '19', name: '裁實模' },
    { code: '20', name: '塑型' },
    { code: '21', name: '燙熨' },
    { code: '22', name: '分條' },
    { code: '23', name: '沖孔,打洞' },
    { code: '24', name: 'A釘五金製作中' },
    { code: '25', name: 'B釘五金製作後' },
    { code: '26', name: '整理' }
  ],
  'F包裝': [
    { code: '01', name: '準備前製' },
    { code: '02', name: '品檢' },
    { code: '03', name: '包前(裝箱)' }
  ],
  'G補料': [
    { code: '01', name: '補分片' }
  ]
});

const toolList = ref({
  'A選料-01': ['電裁機'],
  'A選料-02': ['銀筆'],
  'B開料-01': ['電裁機'],
  'B開料-02': ['小沖台', '大沖台'],
  'C備料-01': ['起皮機'],
  'C備料-02': ['烙印機'],
  'C備料-03': ['烙印機'],
  'C備料-04': ['削邊機'],
  'D塗邊-01': ['手工填縫', '邊油機'],
  'D塗邊-02': ['邊油機'],
  'E製作-01': ['上膠刷'],
  'E製作-02': ['噴膠機'],
  'E製作-03': ['滾平機', '一段輪'],
  'E製作-04': ['銀筆'],
  'E製作-05': ['手持式打粗機'],
  'E製作-07': ['摺尺'],
  'E製作-08': ['669機台'],
  'E製作-09': ['美工刀'],
  'E製作-10': ['錐子'],
  'E製作-11': ['壓線機'],
  'E製作-12': ['電烙鐵'],
  'E製作-13': ['手縫針'],
  'E製作-14': ['紗剪'],
  'E製作-15': ['669機台', '869機台', 'JUKI平車', '包邊機'],
  'E製作-16': ['裁刀'],
  'E製作-17': ['裁刀'],
  'E製作-18': ['修邊機'],
  'E製作-19': ['沖台'],
  'E製作-20': ['烤箱'],
  'E製作-21': ['燙斗'],
  'E製作-22': ['分條機'],
  'E製作-23': ['六分頭', '圓沖,鐵鉗,斬版'],
  'E製作-24': ['沖模,鐵鎚'],
  'E製作-25': ['模具,鐵鎚']
});

const consumableList = ref({
  'C備料-02': ['燙金紙'],
  'D塗邊-01': ['填縫劑'],
  'D塗邊-02': ['各色邊油'],
  'E製作-01': ['無苯黃膠'],
  'E製作-02': ['水性白膠'],
  'E製作-05': ['鑽石磨棒', '砂紙磨頭'],
  'E製作-06': ['邊油'],
  'E製作-08': ['車線'],
  'E製作-10': ['膠水'],
  'E製作-13': ['車線'],
  'E製作-15': ['車針,車線', '車針,車線', '車針,車線', '車針,車線'],
  'E製作-18': ['膠版'],
  'E製作-19': ['膠版'],
  'E製作-24': ['膠版'],
  'E製作-25': ['膠版'],
  'F包裝-01': ['米紙'],
  'F包裝-03': ['防塵袋']
});

// 部位組織列表
const partStructureList = ref([
  { code: 'A', name: '袋蓋' },
  { code: 'B', name: '前身' },
  { code: 'C', name: '後身' },
  { code: 'D', name: '側片' },
  { code: 'E', name: '底片' },
  { code: 'F', name: '內身' },
  { code: 'G', name: '附件' },
  { code: 'H', name: '背帶' },
  { code: 'I', name: '百褶' }
]);

// 分片組織列表
const pieceStructureList = ref({
  'A': [
    { code: 'U', name: '上' }
  ],
  'B': [
    { code: 'D', name: '下' }
  ],
  'C': [
    { code: 'L', name: '左' }
  ],
  'D': [
    { code: 'R', name: '右' }
  ],
  'E': [
    { code: 'M', name: '中' }
  ],
  'F': [
    { code: '/', name: '身片' }
  ],
  'G': [
    { code: '#', name: '共用分片' }
  ],
  'H': [
    { code: '+', name: '組合' }
  ],
  'I': []
});

// 材料列表
const materialList = ref([
  { name: 'A牛' },
  { name: '裡布' }
]);

// 計算屬性：過濾後的小工序列表
const filteredMinorProcesses = computed(() => {
  if (selectedMajorProcess.value) {
    return minorProcessList.value[selectedMajorProcess.value] || [];
  }
  return [];
});

// 計算屬性：過濾後的分片組織列表 (現在返回所有)
const filteredPieceStructures = computed(() => {
  // 將 pieceStructureList 物件的所有值（陣列）合併成一個單一陣列
  // 並確保每個選項都是唯一的（基於 code）
  const allPieces = Object.values(pieceStructureList.value).flat();
  const uniquePieces = [];
  const seenCodes = new Set();
  for (const piece of allPieces) {
    if (!seenCodes.has(piece.code)) {
      uniquePieces.push(piece);
      seenCodes.add(piece.code);
    }
  }
  return uniquePieces;
});

// 計算當前工序可用的工具選項
const toolOptions = computed(() => {
  if (selectedMajorProcess.value && selectedMinorProcess.value) {
    const processKey = `${selectedMajorProcess.value}-${selectedMinorProcess.value.code}`;
    return toolList.value[processKey] || [];
  }
  return [];
});

// 計算當前工序可用的耗材選項
const consumableOptions = computed(() => {
  if (selectedMajorProcess.value && selectedMinorProcess.value) {
    const processKey = `${selectedMajorProcess.value}-${selectedMinorProcess.value.code}`;
    return consumableList.value[processKey] || [];
  }
  return [];
});

// 計算屬性：是否可以添加工序項目
const canAddProcess = computed(() => {
  return canEditProcess.value &&
         selectedMajorProcess.value &&
         selectedMinorProcess.value &&
         selectedPartStructure.value &&
         selectedPieceStructure.value &&
         selectedOrder.value &&
         customPieceName.value; // 新增：檢查自訂分片名稱
});

// 監聽大工序和小工序的變化，更新產生後序號
watch([() => selectedMajorProcess.value, () => selectedMinorProcess.value, () => selectedOrder.value,
       () => selectedPartStructure.value, () => selectedPieceStructure.value],
  ([newMajorProcess, newMinorProcess, newOrder, newPartStructure, newPieceStructure]) => {
    if (newMajorProcess && newMinorProcess && newOrder && newPartStructure && newPieceStructure) {
      // 生成序號格式：部位組織代碼 + 分片組織代碼 + 順序號 + 大工序代號 + 小工序代碼 + '-' + 計數
      const majorProcessKey = newMajorProcess.charAt(0);

      // 初始化計數器
      if (!processCounters.value[majorProcessKey]) {
        processCounters.value[majorProcessKey] = 1;
      }

      sequenceNumber.value = `${newPartStructure.code}${newPieceStructure.code}${newOrder}${majorProcessKey}${newMinorProcess.code}-${processCounters.value[majorProcessKey]}`;
    } else {
      sequenceNumber.value = '';
    }
  }
);

// 返回BOM頁面
const backToBom = () => {
  router.push({ name: 'design-bom' });
};

// 處理大工序變化
const handleMajorProcessChange = () => {
  selectedMinorProcess.value = '';
  selectedTool.value = '';
  selectedConsumable.value = '';
  selectedOrder.value = '';
  updateUsedOrders();
};

// 處理小工序變化
const handleMinorProcessChange = () => {
  selectedTool.value = '';
  selectedConsumable.value = '';

  // 如果有預設工具，自動選擇第一個
  if (toolOptions.value.length > 0) {
    selectedTool.value = toolOptions.value[0];
  }

  // 如果有預設耗材，自動選擇第一個
  if (consumableOptions.value.length > 0) {
    selectedConsumable.value = consumableOptions.value[0];
  }
};

// 處理部位組織變化
const handlePartStructureChange = () => {
  // selectedPieceStructure.value = ''; // 移除清空分片組織的邏輯
};

// 處理分片組織變化
const handlePieceStructureChange = () => {
  // 可以在這裡實現其他依賴邏輯
};

// 處理順序變化
const handleOrderChange = () => {
  // 可以在這裡實現其他依賴邏輯
};

// 更新已使用的順序
const updateUsedOrders = () => {
  if (!selectedMajorProcess.value) {
    usedOrders.value = [];
    return;
  }

  // 篩選出當前大工序中已使用的順序
  usedOrders.value = processItemList.value
    .filter(item => item.majorProcess === selectedMajorProcess.value)
    .map(item => item.order);
};

// 添加工序項目
const addProcessItem = async () => {
  if (!canAddProcess.value) {
    showWarning('請先完成必填項的選擇');
    return;
  }

  // 從 ref 獲取用戶輸入的工序序號值
  const codeInputValue = processCode.value; // 使用不同的變數名避免衝突

  // 確保產生後序號與上方顯示的一致
  const processSequenceNumber = sequenceNumber.value;

  // 創建新的工序項目
  const newItem = {
    bomId: bomId.value,
    processCode: codeInputValue, // 使用獲取到的值
    pieceName: customPieceName.value,
    majorProcess: selectedMajorProcess.value,
    minorProcess: `${selectedMinorProcess.value.code} ${selectedMinorProcess.value.name}`,
    sequenceNumber: processSequenceNumber,
    pieceGroup: selectedPartStructure.value.code,
    pieceDetail: selectedPieceStructure.value.code,
    material: selectedMaterial.value ? selectedMaterial.value.name : '',
    quantity: quantity.value,
    tool: selectedTool.value || '',
    consumable: selectedConsumable.value || '',
    processDescription: processDescription.value || '',
    standardTime: standardTime.value || 0,
    actualTime: 0,
    order: selectedOrder.value
  };

  try {
    // 保存到後端
    const result = await api.processPreparation.create(newItem);

    if (result.status === 'success' && result.data) {
      // 確保返回的數據使用正確的產生後序號
      const savedItem = { ...result.data };
      if (!savedItem.sequenceNumber || savedItem.sequenceNumber !== processSequenceNumber) {
        savedItem.sequenceNumber = processSequenceNumber;
      }

      // 添加到列表，新項目放在最上方
      processItemList.value.unshift(savedItem);
      showSuccess('已添加備料工序項目');

      // 更新已使用的順序
      updateUsedOrders();

      // 更新計數器
      const majorProcessKey = selectedMajorProcess.value.charAt(0);
      processCounters.value[majorProcessKey]++;

      // 清除選擇
      clearSelection();
    } else {
      showError(result.message || '添加備料工序項目失敗');
    }
  } catch (error) {
    console.error('添加備料工序項目失敗:', error);
    showError('添加備料工序項目失敗');
  }
};

// 刪除工序項目
const deleteProcessItem = async (index, id) => {
  // 檢查用戶是否有權限刪除
  if (!canEditProcess.value) {
    showError('您沒有權限執行此操作');
    return;
  }

  try {
    if (id) {
      // 調用 API 刪除
      const result = await api.processPreparation.delete(id);

      if (result.status === 'success') {
        processItemList.value.splice(index, 1);
        showSuccess('已刪除備料工序項目');

        // 更新已使用的順序
        updateUsedOrders();
      } else {
        showError(result.message || '刪除備料工序項目失敗');
      }
    } else {
      // 本地刪除（尚未保存到後端的項目）
      processItemList.value.splice(index, 1);
      showSuccess('已刪除備料工序項目');

      // 更新已使用的順序
      updateUsedOrders();
    }
  } catch (error) {
    console.error('刪除備料工序項目失敗:', error);
    showError('刪除備料工序項目失敗');
  }
};

// 清除選擇
const clearSelection = () => {
  selectedMajorProcess.value = '';
  selectedMinorProcess.value = '';
  selectedPartStructure.value = '';
  selectedPieceStructure.value = '';
  selectedMaterial.value = '';
  selectedOrder.value = '';
  selectedTool.value = '';
  selectedConsumable.value = '';
  quantity.value = 1;
  processDescription.value = '';
  standardTime.value = 0;
  sequenceNumber.value = '';
  customPieceName.value = ''; // 清除自訂分片名稱
  processCode.value = ''; // 清除工序序號
};

// 材料用量表相關
const materialUsageVisible = ref(false);

// 打開材料用量表
const openMaterialUsage = () => {
  materialUsageVisible.value = true;
};

// 關閉材料用量表
const closeMaterialUsage = () => {
  materialUsageVisible.value = false;
};

// 處理材料選擇
const handleMaterialSelected = (materials) => {
  if (materials && materials.length > 0) {
    // 取得第一個選擇的材料，並將其材料編號填入工序序號
    const selectedMaterial = materials[0];
    if (selectedMaterial.code) {
      processCode.value = selectedMaterial.code;
    }
  }
};

const fetchProcessItems = async () => {
  try {
    const response = await api.processPreparation.getByBomId(bomId.value);

    if (response.status === 'success' && response.data) {
      // 確保最新添加的項目顯示在最上方
      processItemList.value = [...response.data].reverse();

      // 更新已使用的順序
      updateUsedOrders();

      // 更新計數器
      processCounters.value = {};
      processItemList.value.forEach(item => {
        const majorProcessKey = item.majorProcess.charAt(0);
        const counterValue = parseInt(item.sequenceNumber.split('-')[1] || '0');

        if (!processCounters.value[majorProcessKey] || counterValue >= processCounters.value[majorProcessKey]) {
          processCounters.value[majorProcessKey] = counterValue + 1;
        }
      });
    }
  } catch (error) {
    console.error('獲取備料工序列表失敗:', error);
    showError('獲取備料工序列表失敗');
  }
};

// 獲取當前用戶信息
const fetchCurrentUser = async () => {
  try {
    const response = await api.getCurrentUser();
    console.log('獲取到的用戶信息:', response);
    if (response.status === 'success' && response.data && response.data.employee) {
      currentUserDepartment.value = parseInt(response.data.employee.department);
      isAdmin.value = response.data.employee.role === 0;
      console.log('設置用戶部門:', currentUserDepartment.value, '是管理員:', isAdmin.value);
    } else {
      // 如果API獲取失敗，嘗試從localStorage獲取
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          currentUserDepartment.value = parseInt(user.department);
          isAdmin.value = user.role === 0;
          console.log('從localStorage獲取用戶部門:', currentUserDepartment.value, '是管理員:', isAdmin.value);
        } catch (error) {
          console.error('解析用戶資訊出錯:', error);
        }
      }
    }
  } catch (error) {
    console.error('獲取使用者資訊時發生錯誤:', error);
    // 嘗試從localStorage獲取
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        currentUserDepartment.value = parseInt(user.department);
        isAdmin.value = user.role === 0;
        console.log('從localStorage獲取用戶部門:', currentUserDepartment.value, '是管理員:', isAdmin.value);
      } catch (error) {
        console.error('解析用戶資訊出錯:', error);
      }
    }
  }
};

// 獲取BOM資訊，檢查確認狀態
const fetchBomInfo = async () => {
  try {
    if (!bomId.value) return;

    const response = await api.bom.getById(bomId.value);

    if (response.status === 'success' && response.data) {
      bomConfirmationStatus.value = Number(response.data.confirmationStatus || 0);

      // 如果BOM不是未確認狀態，顯示警告
      if (bomConfirmationStatus.value !== 0) {
        showWarning('注意：此BOM已經被確認，無法進行編輯操作。');
      }
    }
  } catch (error) {
    console.error('獲取BOM資訊失敗:', error);
    showWarning('無法獲取BOM確認狀態');
  }
};

// 頁面加載時初始化
onMounted(async () => {
  // 直接從localStorage獲取用戶信息進行調試
  const userStr = localStorage.getItem('user');
  console.log('localStorage中的原始用戶信息:', userStr);

  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      console.log('解析後的用戶信息:', user);
      console.log('部門值和類型:', user.department, typeof user.department);

      // 強制設置部門值，確保正確解析
      const deptValue = parseInt(user.department);
      currentUserDepartment.value = isNaN(deptValue) ? -1 : deptValue;
      isAdmin.value = user.role === 0;
      console.log('設置後的部門值:', currentUserDepartment.value, '是否為台北部:', currentUserDepartment.value === 0);

      // 直接檢查部門值是否為0
      if (currentUserDepartment.value === 0) {
        console.log('⚠️ 用戶為台北部，應為只讀模式');
      } else {
        console.log('✅ 用戶非台北部，應可編輯');
      }
    } catch (error) {
      console.error('解析用戶資訊出錯:', error);
    }
  }

  // 獲取當前用戶信息 (仍然保留API調用，以防localStorage方式失敗)
  await fetchCurrentUser();

  // 再次確認權限設置
  console.log('最終權限設置 - 部門:', currentUserDepartment.value, '管理員:', isAdmin.value, '可編輯:', canEditProcess.value);

  if (!bomId.value) {
    // 如果沒有指定bomId，嘗試從localStorage獲取
    const storedBomId = localStorage.getItem('currentBomId');
    if (storedBomId) {
      bomId.value = storedBomId;
    } else {
      // 如果仍然沒有bomId，返回到BOM頁面
      showWarning('無法獲取BOM ID，請重新選擇BOM');
      router.push({ name: 'design-bom' });
      return;
    }
  }

  // 獲取BOM信息，檢查確認狀態
  await fetchBomInfo();

  // 強制檢查一次最終值
  const finalCanEdit = currentUserDepartment.value !== 0 && bomConfirmationStatus.value === 0;
  console.log('【重要】最終檢查 - 部門:', currentUserDepartment.value, 'BOM確認狀態:', bomConfirmationStatus.value, '是否可編輯:', finalCanEdit);

  // 獲取備料工序列表
  fetchProcessItems();
});
</script>

<style scoped>
.process-preparation-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.process-header {
  display: flex;
  justify-content: flex-start; /* Align items to the start */
  align-items: center;
  margin-bottom: 24px;
  gap: 16px; /* Add gap between items */
}

.process-header h2 {
  flex-grow: 1; /* Allow the heading to take up remaining space */
}

.process-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.back-to-bom {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #3b82f6;
  font-weight: 500;
}

.back-to-bom:hover {
  text-decoration: underline;
}

.process-form {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  flex: 1;
  min-width: 0; /* Prevent flex items from overflowing */
}

/* Style for larger description input */
.form-group-large {
  flex: 2; /* Make description field wider */
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.form-group select,
.form-group input,
.quantity-input,
.time-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.sequence-number {
  padding: 8px 12px;
  background-color: #edf2f7;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 700;
  color: #4a5568;
  height: 38px;
  display: flex;
  align-items: center;
}

.form-group select:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.form-actions {
  margin-top: 16px;
  justify-content: flex-end;
}

.add-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.add-btn {
  background-color: #3b82f6;
  color: white;
}

.add-btn:hover {
  background-color: #2563eb;
}

.add-btn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  opacity: 0.6;
}

.clear-btn {
  background-color: #e5e7eb;
  color: #4b5563;
}

.clear-btn:hover {
  background-color: #d1d5db;
}

.process-table-container {
  overflow-x: auto;
}

.process-table {
  width: 100%;
  border-collapse: collapse;
}

.process-table th,
.process-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
}

.process-table th {
  background-color: #f5f7fa;
  color: #666;
  font-weight: 500;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
}

.read-only-notice {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.read-only-notice p {
  color: #92400e;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
}

.read-only-notice p::before {
  content: '\f071'; /* 警告圖標的 Unicode */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  margin-right: 8px;
  color: #f59e0b;
}

.red {
  color: red;
}
</style>
