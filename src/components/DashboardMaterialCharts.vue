<template>
  <div class="material-charts-container">
    <div class="chart-header">
      <h3>原物料分布</h3>
      <div class="selectors-container">
        <div class="warehouse-selector">
          <label for="warehouse-select">選擇倉庫:</label>
          <select id="warehouse-select" v-model="selectedWarehouse" @change="fetchMaterialData">
            <option value="台北部">台北部</option>
            <option value="廠務部">廠務部</option>
          </select>
        </div>
        <div v-if="showFactoryWarehouseSelector" class="factory-warehouse-selector">
          <label for="factory-warehouse-select">選擇廠務部倉別:</label>
          <select id="factory-warehouse-select" v-model="selectedFactoryWarehouse" @change="fetchMaterialData">
            <option value="全部倉庫">全部倉庫</option>
            <option value="廠務部A">廠務部A</option>
            <option value="廠務部B">廠務部B</option>
            <option value="廠務部C">廠務部C</option>
            <option value="廠務部D">廠務部D</option>
            <option value="廠務部E">廠務部E</option>
          </select>
        </div>
      </div>
    </div>

    <div class="charts-wrapper">
      <div class="chart-container">
        <h4>皮料分布</h4>
        <div v-if="loading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i> 載入中...
        </div>
        <div v-else-if="!hasLeatherData" class="no-data-message">
          無皮料數據
        </div>
        <div v-else ref="leatherChartContainer" class="chart"></div>
      </div>

      <div class="chart-container">
        <h4>其他原物料分布</h4>
        <div v-if="loading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i> 載入中...
        </div>
        <div v-else-if="!hasOtherMaterialData" class="no-data-message">
          無其他原物料數據
        </div>
        <div v-else ref="otherMaterialChartContainer" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, onUnmounted, nextTick } from 'vue';
import api from '@/services/api';
import { useNotification } from '@/services/notificationService';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 註冊必要的 ECharts 組件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CanvasRenderer
]);

const notification = useNotification();
const selectedWarehouse = ref('台北部');
const selectedFactoryWarehouse = ref('全部倉庫'); // 新增廠務部倉別選擇
const loading = ref(false);
const materials = ref([]);
const leatherChart = ref(null);
const otherMaterialChart = ref(null);
const leatherChartContainer = ref(null);
const otherMaterialChartContainer = ref(null);

// 計算是否顯示廠務部倉別選擇
const showFactoryWarehouseSelector = computed(() => {
  return selectedWarehouse.value === '廠務部';
});

// 計算是否有皮料數據
const hasLeatherData = computed(() => {
  return leatherMaterials.value.length > 0;
});

// 計算是否有其他原物料數據
const hasOtherMaterialData = computed(() => {
  return otherMaterials.value.length > 0;
});

// 分離皮料和其他原物料
const leatherMaterials = computed(() => {
  return materials.value.filter(material => material.category === '皮料');
});

const otherMaterials = computed(() => {
  return materials.value.filter(material => material.category !== '皮料');
});

// 獲取原物料數據
const fetchMaterialData = async () => {
  try {
    loading.value = true;
    // 直接獲取所有原物料數據
    const allMaterialsResponse = await api.materials.getAll();

    if (allMaterialsResponse && Array.isArray(allMaterialsResponse)) {
      // 如果選擇的是廠務部，則需要為皮料加載皮料詳情
      if (selectedWarehouse.value === '廠務部') {
        for (const material of allMaterialsResponse) {
          if (material.category === '皮料') {
            try {
              // 獲取皮料詳情，如果選擇了特定倉庫，則只獲取該倉庫的數據
              let params = { material_id: material.id };

              // 如果選擇了特定倉庫，則添加倉庫篩選條件
              if (selectedFactoryWarehouse.value !== '全部倉庫') {
                params.warehouse = selectedFactoryWarehouse.value;
              }

              const leatherDetails = await api.materials.leatherMaterials.getAll(params);

              if (leatherDetails && leatherDetails.data && Array.isArray(leatherDetails.data)) {

                // 計算廠務部A-E的面積
                let factoryAArea = 0;
                let factoryBArea = 0;
                let factoryCArea = 0;
                let factoryDArea = 0;
                let factoryEArea = 0;

                leatherDetails.data.forEach(detail => {
                  const area = Number(detail.area || 0) * Number(detail.quantity || 1);

                  if (detail.warehouse === '廠務部A') {
                    factoryAArea += area;
                  } else if (detail.warehouse === '廠務部B') {
                    factoryBArea += area;
                  } else if (detail.warehouse === '廠務部C') {
                    factoryCArea += area;
                  } else if (detail.warehouse === '廠務部D') {
                    factoryDArea += area;
                  } else if (detail.warehouse === '廠務部E') {
                    factoryEArea += area;
                  }
                });

                // 將面積存入原物料對象中
                material.factory_a_leather_area = factoryAArea;
                material.factory_b_leather_area = factoryBArea;
                material.factory_c_leather_area = factoryCArea;
                material.factory_d_leather_area = factoryDArea;
                material.factory_e_leather_area = factoryEArea;
                material.factory_leather_area = factoryAArea + factoryBArea + factoryCArea + factoryDArea + factoryEArea;


              }
            } catch (error) {
              console.error(`獲取皮料 ${material.code} 詳情失敗:`, error);
            }
          }
        }
      }

      if (selectedWarehouse.value === '台北部') {
        // 篩選出台北部有庫存的原物料
        materials.value = allMaterialsResponse.filter(material => {
          // 所有原物料都使用 taipei_stock_quantity 欄位
          return material.taipei_stock_quantity && Number(material.taipei_stock_quantity) > 0;
        });

      } else if (selectedWarehouse.value === '廠務部') {
        // 篩選出廠務部有庫存的原物料
        materials.value = allMaterialsResponse.filter(material => {
          if (material.category === '皮料') {
            // 如果是皮料，根據選擇的倉庫檢查對應的面積
            if (selectedFactoryWarehouse.value === '全部倉庫') {
              // 如果選擇全部倉庫，則檢查總面積
              return material.factory_leather_area && material.factory_leather_area > 0;
            } else if (selectedFactoryWarehouse.value === '廠務部A') {
              // 如果選擇廠務部A，則檢查A倉庫的面積
              return material.factory_a_leather_area && material.factory_a_leather_area > 0;
            } else if (selectedFactoryWarehouse.value === '廠務部B') {
              return material.factory_b_leather_area && material.factory_b_leather_area > 0;
            } else if (selectedFactoryWarehouse.value === '廠務部C') {
              return material.factory_c_leather_area && material.factory_c_leather_area > 0;
            } else if (selectedFactoryWarehouse.value === '廠務部D') {
              return material.factory_d_leather_area && material.factory_d_leather_area > 0;
            } else if (selectedFactoryWarehouse.value === '廠務部E') {
              return material.factory_e_leather_area && material.factory_e_leather_area > 0;
            }
            return false;
          } else {
            // 其他原物料直接檢查 factory_stock_quantity
            return material.factory_stock_quantity && Number(material.factory_stock_quantity) > 0;
          }
        });

      }

    } else {
      materials.value = [];
    }

    // 更新圖表
    updateCharts();
  } catch (error) {
    notification.error('獲取原物料數據失敗');
    materials.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化圖表
const initCharts = () => {
  // 初始化皮料圖表
  if (leatherChartContainer.value) {
    try {
      // 如果已經初始化過，先銷毀
      if (leatherChart.value) {
        leatherChart.value.dispose();
      }
      leatherChart.value = echarts.init(leatherChartContainer.value);
    } catch (error) {
      // 圖表初始化失敗
    }
  }

  // 初始化其他原物料圖表
  if (otherMaterialChartContainer.value) {
    try {
      // 如果已經初始化過，先銷毀
      if (otherMaterialChart.value) {
        otherMaterialChart.value.dispose();
      }
      otherMaterialChart.value = echarts.init(otherMaterialChartContainer.value);
    } catch (error) {
      // 圖表初始化失敗
    }
  }
};

// 更新圖表
const updateCharts = () => {
  // 確保圖表已經初始化
  if (!leatherChart.value || !otherMaterialChart.value) {
    initCharts();
  }

  // 更新皮料圖表
  try {
    updateLeatherChart();
  } catch (error) {
    // 如果更新失敗，則顯示空圖表
    if (leatherChart.value) {
      leatherChart.value.setOption({
        title: {
          text: '皮料分布',
          left: '40%',
          top: 0,
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '55%'],
          data: []
        }]
      });
    }
  }

  // 更新其他原物料圖表
  try {
    updateOtherMaterialChart();
  } catch (error) {
    // 如果更新失敗，則顯示空圖表
    if (otherMaterialChart.value) {
      otherMaterialChart.value.setOption({
        title: {
          text: '其他原物料分布',
          left: '30%',
          top: 0,
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        series: [{
          type: 'pie',
          radius: ['20%', '70%'],
          center: ['30%', '55%'],
          data: []
        }]
      });
    }
  }

  // 調整圖表大小
  handleResize();
};

// 更新皮料圖表
const updateLeatherChart = () => {
  if (!leatherChart.value) {
    return;
  }

  if (leatherMaterials.value.length === 0) {
    // 顯示空圖表
    leatherChart.value.setOption({
      title: {
        text: '皮料分布',
        left: '40%',
        top: 0,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} m² ({d}%)'
      },
      series: [{
        name: '皮料面積',
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['40%', '55%'],
        data: []
      }]
    });
    return;
  }

  // 按照皮料編號分組
  const leatherByCategory = {};
  leatherMaterials.value.forEach(leather => {
    // 使用皮料編號作為分類
    let category = '未分類';

    if (leather.code) {
      category = leather.code;
    }

    // 計算皮料面積
    let area = 0;

    // 根據倉庫選擇對應的庫存數量
    if (selectedWarehouse.value === '台北部') {
      // 台北部皮料使用 taipei_stock_quantity
      area = Number(leather.taipei_stock_quantity || 0);
    } else if (selectedWarehouse.value === '廠務部') {
      if (selectedFactoryWarehouse.value === '全部倉庫') {
        // 廠務部皮料使用已經計算好的廠務部皮料面積
        area = Number(leather.factory_leather_area || 0);
      } else if (selectedFactoryWarehouse.value === '廠務部A') {
        area = Number(leather.factory_a_leather_area || 0);
      } else if (selectedFactoryWarehouse.value === '廠務部B') {
        area = Number(leather.factory_b_leather_area || 0);
      } else if (selectedFactoryWarehouse.value === '廠務部C') {
        area = Number(leather.factory_c_leather_area || 0);
      } else if (selectedFactoryWarehouse.value === '廠務部D') {
        area = Number(leather.factory_d_leather_area || 0);
      } else if (selectedFactoryWarehouse.value === '廠務部E') {
        area = Number(leather.factory_e_leather_area || 0);
      }
    }

    // 確保面積是有效數字
    if (isNaN(area)) {
      area = 0;
    }

    // 只有面積大於 0 的才加入圖表
    if (area > 0) {
      if (!leatherByCategory[category]) {
        leatherByCategory[category] = 0;
      }
      leatherByCategory[category] += area;
    }
  });

  // 轉換為圖表數據
  const chartData = Object.entries(leatherByCategory).map(([name, value]) => ({
    name,
    value: parseFloat(value.toFixed(2))
  }));

  // 設置圖表選項
  const option = {
    title: {
      left: '40%',
      top: 0,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        // 尋找對應的皮料數據
        const leatherData = leatherMaterials.value.find(item => item.code === params.name);
        if (leatherData) {
          return `${params.seriesName}<br/>
                 編號: ${params.name}<br/>
                 名稱: ${leatherData.name || '無名稱'}<br/>
                 面積: ${params.value.toFixed(2)} m² (${params.percent}%)`;
        } else {
          return `${params.seriesName}<br/>
                 編號: ${params.name}<br/>
                 面積: ${params.value.toFixed(2)} m² (${params.percent}%)`;
        }
      }
    },
    legend: {
      orient: 'vertical',
      right: 10, // 將圖例向左移動，使其距離圓餅圖更近
      top: 'center',
      type: 'scroll',
      formatter: (name) => {
        // 尋找對應的皮料數據
        const leatherData = leatherMaterials.value.find(item => item.code === name);
        if (leatherData && leatherData.name) {
          // 顯示編號和名稱的前幾個字
          const shortName = leatherData.name.length > 8 ? leatherData.name.substring(0, 8) + '...' : leatherData.name;
          return `${name}: ${shortName}`;
        } else {
          return name;
        }
      },
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '皮料面積',
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['40%', '55%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  };

  // 設置圖表選項
  try {
    leatherChart.value.setOption(option, true);
  } catch (error) {
    // 圖表選項設置失敗
  }
};

// 更新其他原物料圖表
const updateOtherMaterialChart = () => {
  if (!otherMaterialChart.value) {
    return;
  }

  if (otherMaterials.value.length === 0) {
    // 顯示空圖表
    otherMaterialChart.value.setOption({
      title: {
        text: '其他原物料分布',
        left: '30%',
        top: 0,
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '原物料數量',
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['30%', '55%'],
        data: []
      }]
    });
    return;
  }

  // 按照原物料編號分組
  const materialByCategory = {};
  otherMaterials.value.forEach(material => {
    // 使用原物料的編號作為分類
    let category = '未分類';

    if (material.code) {
      category = material.code;
    }

    // 根據倉庫選擇對應的庫存數量
    let quantity = 0;
    if (selectedWarehouse.value === '台北部') {
      quantity = Number(material.taipei_stock_quantity || 0);
    } else if (selectedWarehouse.value === '廠務部') {
      // 其他原物料直接使用 factory_stock_quantity
      // 目前其他原物料只有總廠務部庫存，不分A-E倉庫
      // 所以無論選擇哪個廠務部倉庫，都顯示全部廠務部庫存
      quantity = Number(material.factory_stock_quantity || 0);
    }

    // 確保數量是有效數字
    if (isNaN(quantity)) {
      quantity = 0;
    }

    // 只有數量大於 0 的才加入圖表
    if (quantity > 0) {
      if (!materialByCategory[category]) {
        materialByCategory[category] = 0;
      }
      materialByCategory[category] += quantity;
    }
  });

  // 轉換為圖表數據
  const chartData = Object.entries(materialByCategory).map(([name, value]) => ({
    name,
    value: parseFloat(value.toFixed(2))
  }));

  // 設置圖表選項
  const option = {
    title: {
      left: '30%',
      top: 0,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        // 尋找對應的原物料數據
        const materialData = otherMaterials.value.find(item => item.code === params.name);
        if (materialData) {
          return `${params.seriesName}<br/>
                 編號: ${params.name}<br/>
                 名稱: ${materialData.name || '無名稱'}<br/>
                 類別: ${materialData.category || '無類別'}<br/>
                 數量: ${params.value.toFixed(2)} ${materialData.unit || ''} (${params.percent}%)`;
        } else {
          return `${params.seriesName}<br/>
                 編號: ${params.name}<br/>
                 數量: ${params.value.toFixed(2)} (${params.percent}%)`;
        }
      }
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      type: 'scroll',
      formatter: (name) => {
        // 尋找對應的原物料數據
        const materialData = otherMaterials.value.find(item => item.code === name);
        if (materialData && materialData.name) {
          // 顯示編號和名稱的前幾個字
          const shortName = materialData.name.length > 8 ? materialData.name.substring(0, 8) + '...' : materialData.name;
          return `${name}: ${shortName}`;
        } else {
          return name;
        }
      },
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '原物料數量',
        type: 'pie',
        radius: ['20%', '70%'],
        center: ['30%', '55%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  };

  // 設置圖表選項
  try {
    otherMaterialChart.value.setOption(option, true);
  } catch (error) {
    // 圖表選項設置失敗
  }
};

// 監聽窗口大小變化，調整圖表大小
const handleResize = () => {

  // 確保圖表容器有大小
  if (leatherChartContainer.value) {
   
  }

  if (otherMaterialChartContainer.value) {
    
  }

  // 調整圖表大小
  if (leatherChart.value) {
    try {
      leatherChart.value.resize();
     
    } catch (error) {
   
    }
  }

  if (otherMaterialChart.value) {
    try {
      otherMaterialChart.value.resize();
      
    } catch (error) {
      
    }
  }
};

// 監聽倉庫選擇變化
watch(selectedWarehouse, async () => {
  // 重新獲取原物料數據
  await fetchMaterialData();

  // 等待下一個渲染周期
  await nextTick();

  // 重新初始化圖表
  initCharts();

  // 更新圖表
  updateCharts();

  // 延遲一下再次調整圖表大小
  setTimeout(() => {
    handleResize();
    updateCharts(); // 再次更新圖表
  }, 300);
});

// 監聽廠務部倉別選擇變化
watch(selectedFactoryWarehouse, async () => {
  // 只有在選擇廠務部時才重新獲取數據
  if (selectedWarehouse.value === '廠務部') {
    // 重新獲取原物料數據
    await fetchMaterialData();

    // 等待下一個渲染周期
    await nextTick();

    // 重新初始化圖表
    initCharts();

    // 更新圖表
    updateCharts();

    // 延遲一下再次調整圖表大小
    setTimeout(() => {
      handleResize();
      updateCharts(); // 再次更新圖表
    }, 300);
  }
});

onMounted(async () => {
  // 等待DOM渲染完成
  await nextTick();

  // 獲取原物料數據
  await fetchMaterialData();

  // 等待下一個渲染周期
  await nextTick();

  // 初始化圖表
  initCharts();

  // 更新圖表
  updateCharts();

  // 添加窗口大小變化監聽
  window.addEventListener('resize', handleResize);

  // 延遲一下再次調整圖表大小
  setTimeout(() => {
    handleResize();
    updateCharts(); // 再次更新圖表
  }, 500);
});

onUnmounted(() => {
  // 移除窗口大小變化監聽
  window.removeEventListener('resize', handleResize);

  // 銷毀圖表實例
  if (leatherChart.value) {
    leatherChart.value.dispose();
  }
  if (otherMaterialChart.value) {
    otherMaterialChart.value.dispose();
  }
});
</script>

<style scoped>
.material-charts-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  color: #333;
}

.selectors-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.warehouse-selector,
.factory-warehouse-selector {
  display: flex;
  align-items: center;
}

.warehouse-selector label,
.factory-warehouse-selector label {
  margin-right: 10px;
  font-weight: 500;
}

.warehouse-selector select,
.factory-warehouse-selector select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

.charts-wrapper {
  display: flex;
  gap: 10px;
  padding-right: 20px; /* 添加右側間距，避免被遮住 */
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
  max-width: 48%; /* 限制寬度，避免圖表太寬 */
}

.chart-container h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #555;
  /* 移除文字置中，使用左對齊 */
  text-align: left;
  padding-left: 35%;
}

.chart {
  height: 300px;
  width: 100%;
  min-width: 200px;
  min-height: 300px;
  display: block;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
}

.loading-indicator i {
  margin-right: 8px;
}

.no-data-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-style: italic;
}

@media (max-width: 768px) {
  .charts-wrapper {
    flex-direction: column;
    padding-right: 0; /* 移動版不需要右側間距 */
  }

  .chart-container {
    margin-bottom: 20px;
    max-width: 100%; /* 移動版使用全寬 */
  }
}
</style>
