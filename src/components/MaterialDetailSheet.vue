<template>
  <div v-if="visible" class="material-detail-sheet-dialog">
    <div class="material-detail-sheet-content">
      <div class="dialog-header">
        <h2>產品用料明細表({{ sheetType }})</h2>
        <button class="close-btn" @click="closeDialog">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="sheet-info">
        <div class="sheet-header">
          <div class="sheet-title">
            <h3>產品用料明細表({{ sheetType }})</h3>
          </div>
          <div class="sheet-meta">
            <div class="meta-item">
              <span class="meta-label">更新日期:</span>
              <span class="meta-value">{{ formatDate(new Date()) }}</span>
            </div>
          </div>
        </div>

        <div class="sheet-table-container">
          <table class="sheet-table">
            <thead>
              <tr>
                <th>部位名稱</th>
                <th>材料名稱</th>
                <th>寬(cm)</th>
                <th>高(cm)</th>
                <th>面積(cm²)</th>
                <th>組成數量</th>
                <th>分片</th>
                <th>總和</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="detailItems.length === 0">
                <td colspan="9" class="no-data-message">尚未新增任何明細項目</td>
              </tr>
              <template v-for="(group, groupIndex) in groupedDetailItems" :key="groupIndex">
                <tr v-for="(item, itemIndex) in group.items" :key="item.id || itemIndex"
                    :class="{
                      'zebra-even-row': !group.isSpecialGroup && groupIndex % 2 === 0,
                      'zebra-odd-row': !group.isSpecialGroup && groupIndex % 2 === 1,
                      'special-leather-row': group.isSpecialGroup
                    }">
                  <td>{{ item.position_name || '-' }}</td>
                  <td>{{ item.material_name || '-' }}</td>
                  <td>{{ formatNumber(item.width) }}</td>
                  <td>{{ formatNumber(item.height) }}</td>
                  <td>{{ formatNumber(calculateArea(item.width, item.height)) }}</td>
                  <td>{{ formatNumber(item.quantity || 1) }}</td>
                  <td>{{ formatNumber(calculateArea(item.width, item.height) * (item.quantity || 1)) }}</td>
                  <!-- 總和儲存格，只在每組的第一行顯示 -->
                  <td v-if="itemIndex === 0" :rowspan="group.items.length"
                      :class="{
                        'special-group': group.isSpecialGroup,
                        'zebra-even': !group.isSpecialGroup && groupIndex % 2 === 0,
                        'zebra-odd': !group.isSpecialGroup && groupIndex % 2 === 1
                      }">
                    {{ formatNumber(group.totalArea) }}
                    <span v-if="group.isSpecialGroup" class="group-note">(皮料總和)</span>
                  </td>
                  <td class="action-cell">
                    <button class="delete-btn" @click="confirmDelete(item)" title="刪除">
                      <i class="fas fa-trash-alt"></i>
                    </button>
                  </td>
                </tr>
              </template>
              <!-- 總計行 -->
              <tr class="total-row" v-if="detailItems.length > 0">
                <td colspan="6" class="text-right">面積總和(cm²):</td>
                <td>{{ formatNumber(totalArea) }}</td>
                <td>{{ formatNumber(totalArea) }}</td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="sheet-actions">
        <div class="import-section">
          <input
            type="file"
            ref="fileInput"
            @change="handleFileUpload"
            accept=".xlsx,.xls"
            style="display: none"
          />
          <button class="import-btn" @click="triggerFileUpload">
            <i class="fas fa-file-import"></i> 匯入Excel
          </button>
          <button class="delete-all-btn" @click="confirmDeleteAll">
            <i class="fas fa-trash-alt"></i> 全部刪除
          </button>
          <span v-if="importStatus" class="import-status">{{ importStatus }}</span>
        </div>
        <div class="action-buttons">
          <button class="cancel-btn" @click="closeDialog">關閉</button>
        </div>
      </div>
    </div>

    <!-- 確認刪除對話框 -->
    <div v-if="showDeleteConfirm" class="delete-confirm-dialog">
      <div class="delete-confirm-content">
        <h3>確認刪除</h3>
        <p>您確定要刪除這個明細項目嗎？</p>
        <p class="item-info" v-if="itemToDelete">
          <strong>部位名稱:</strong> {{ itemToDelete.position_name || '-' }}<br>
          <strong>材料名稱:</strong> {{ itemToDelete.material_name || '-' }}
        </p>
        <div class="confirm-actions">
          <button class="cancel-btn" @click="cancelDelete">取消</button>
          <button class="confirm-btn" @click="deleteItem">確認刪除</button>
        </div>
      </div>
    </div>

    <!-- 確認全部刪除對話框 -->
    <div v-if="showDeleteAllConfirm" class="delete-confirm-dialog">
      <div class="delete-confirm-content">
        <h3>確認全部刪除</h3>
        <p>您確定要刪除所有明細項目嗎？</p>
        <p class="warning-text">此操作將刪除所有 {{ detailItems.length }} 個項目，且無法恢復。</p>
        <div class="confirm-actions">
          <button class="cancel-btn" @click="cancelDeleteAll">取消</button>
          <button class="confirm-btn" @click="deleteAllItems">確認刪除全部</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import api from '../services/api';
import { useNotification } from '../services/notificationService';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  materialItem: {
    type: Object,
    required: true
  },
  bomId: {
    type: [String, Number],
    required: true
  },
  sheetType: {
    type: String,
    default: '主料',
    validator: (value) => ['主料', '副料'].includes(value)
  }
});

const emit = defineEmits(['close', 'updated']);
const notification = useNotification();

// 檔案上傳相關
const fileInput = ref(null);
const importStatus = ref('');

// 明細項目列表
const detailItems = ref([]);

// 刪除相關
const showDeleteConfirm = ref(false);
const itemToDelete = ref(null);

// 全部刪除相關
const showDeleteAllConfirm = ref(false);

// 計算總面積
const totalArea = computed(() => {
  return detailItems.value.reduce((sum, item) => {
    const area = calculateArea(item.width, item.height);
    const totalArea = area * (item.quantity || 1); // 直接計算面積 * 組成數量
    return sum + totalArea;
  }, 0);
});

// 判斷材料名稱是否為特定模式的動物皮
const isSpecialLeatherPattern = (name) => {
  if (!name || typeof name !== 'string') return false;

  // 檢查是否為 A 開頭加上數字和動物皮的模式
  // 例如：A牛、A+1.5mm牛、A+0.8mm羊 等
  if (name.startsWith('A') && (name.includes('牛') || name.includes('羊') || name.includes('鯤魚'))) {
    return true;
  }

  // 檢查是否為大寫英文字母加上動物皮的模式
  // 例如：ABCDE牛、FGHIJ羊、KLMNO鯤魚等
  const pattern = /^[A-Z]+(牛|羊|鯤魚|鰭魚)/;
  return pattern.test(name);
};

// 獲取材料的分組鍵值
const getMaterialGroupKey = (name) => {
  if (!name || typeof name !== 'string') return '-';

  // 如果是特定模式的動物皮，取出動物皮部分作為分組鍵值
  if (isSpecialLeatherPattern(name)) {
    // 判斷是哪種動物皮
    if (name.includes('牛')) {
      return `A牛皮`; // A牛皮組
    } else if (name.includes('羊')) {
      return `A羊皮`; // A羊皮組
    } else if (name.includes('鯤魚')) {
      return `A鯤魚皮`; // A鯤魚皮組
    } else if (name.includes('鰭魚')) {
      return `A鰭魚皮`; // A鰭魚皮組
    }

    // 如果上面都沒有匹配到，則嘗試使用正則表達式提取
    const animalType = name.match(/[一-龥]+$/)?.[0] || '';
    if (animalType) {
      return `特殊皮料-${animalType}`;
    }
  }

  // 其他情況下直接使用原始名稱
  return name;
};

// 將相同材料名稱的項目分組
const groupedDetailItems = computed(() => {
  // 先將項目按材料名稱分組
  const groups = {};

  detailItems.value.forEach(item => {
    const materialName = item.material_name || '-';
    const groupKey = getMaterialGroupKey(materialName);

    if (!groups[groupKey]) {
      groups[groupKey] = {
        materialName: groupKey,
        items: [],
        totalArea: 0,
        isSpecialGroup: groupKey.startsWith('特殊皮料-') || groupKey.startsWith('A牛皮') || groupKey.startsWith('A羊皮') || groupKey.startsWith('A鯤魚皮') || groupKey.startsWith('A鰭魚皮')
      };
    }
    groups[groupKey].items.push(item);

    // 計算每個項目的分片面積並累加到總和
    const area = calculateArea(item.width, item.height);
    const itemTotalArea = area * (item.quantity || 1);
    groups[groupKey].totalArea += itemTotalArea;
  });

  // 轉換為數組以便在模板中使用
  return Object.values(groups);
});

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

// 格式化數字到小數點後兩位
const formatNumber = (value) => {
  const num = Number(value);
  if (isNaN(num)) {
    return '-';
  }
  return num.toFixed(2);
};

// 計算面積
const calculateArea = (width, height) => {
  const w = Number(width);
  const h = Number(height);
  if (isNaN(w) || isNaN(h)) {
    return 0;
  }
  return w * h;
};

// 觸發檔案上傳
const triggerFileUpload = () => {
  fileInput.value.click();
};

// 處理檔案上傳
const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  importStatus.value = '正在上傳檔案...';
  console.log('開始上傳檔案:', file.name, file.size, file.type);

  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('bomId', props.bomId);
    // 移除 materialId 參數，使明細表在整個 BOM 中通用
    formData.append('sheetType', props.sheetType);

    console.log('準備上傳參數:', {
      bomId: props.bomId,
      sheetType: props.sheetType
    });

    // 呼叫API上傳檔案
    const response = await api.materialDetailSheet.importExcel(formData);
    console.log('上傳響應:', response);

    if (response && response.status === 'success') {
      importStatus.value = '匯入成功';

      // 重新載入明細項目
      await fetchDetailItems();

      // 通知父組件更新
      emit('updated');
    } else {
      throw new Error(response?.message || '匯入失敗');
    }
  } catch (error) {
    console.error('匯入Excel檔案失敗:', error);
    notification.error(`匯入失敗: ${error.message || '未知錯誤'}`);
    importStatus.value = '匯入失敗';
  } finally {
    // 清除檔案輸入
    event.target.value = '';
  }
};

// 獲取明細項目
const fetchDetailItems = async () => {
  try {
    const response = await api.materialDetailSheet.getItems({
      bomId: props.bomId,
      sheetType: props.sheetType
      // 移除 materialId 參數，使明細表在整個 BOM 中通用
    });

    let items = [];
    if (response && response.status === 'success' && Array.isArray(response.data)) {
      items = response.data;
    } else if (response && Array.isArray(response)) {
      // 兼容直接返回數組的情況
      items = response;
    }

    console.log(`原始數據項目數量: ${items.length}`);

    // 直接根據 sheet_type 欄位過濾數據
    // 這樣可以確保主料明細表只顯示主料數據，副料明細表只顯示副料數據
    let filteredItems = items.filter(item => {
      // 如果項目的 sheet_type 與當前頁面的 sheetType 相同，則保留
      return item.sheet_type === props.sheetType;
    });

    console.log(`過濾後的數據項目數量: ${filteredItems.length}`);

    // 移除特殊標記行，如「產品用料明細表(主料)」或「產品用料明細表(副料)」
    filteredItems = filteredItems.filter(item => {
      return !(item.material_name === `產品用料明細表(${props.sheetType})`) &&
             !(item.position_name === `產品用料明細表(${props.sheetType})`);
    });

    // 輸出每個項目的 sheet_type，以便進行調試
    filteredItems.forEach(item => {
      console.log(`項目: ${item.material_name || item.position_name}, sheet_type: ${item.sheet_type}`);
    });

    // 按創建時間降序排序，確保最新的資料在上方
    detailItems.value = filteredItems.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
      const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
      return dateB - dateA; // 降序排序
    });

    console.log(`最終顯示的數據項目數量: ${detailItems.value.length}`);
  } catch (error) {
    console.error('獲取明細項目失敗:', error);
    notification.error('獲取明細項目失敗');
    detailItems.value = [];
  }
};

// 確認刪除項目
const confirmDelete = (item) => {
  itemToDelete.value = item;
  showDeleteConfirm.value = true;
};

// 取消刪除
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  itemToDelete.value = null;
};

// 刪除項目
const deleteItem = async () => {
  if (!itemToDelete.value || !itemToDelete.value.id) {
    notification.error('無法刪除，項目 ID 無效');
    cancelDelete();
    return;
  }

  try {
    const response = await api.materialDetailSheet.delete(itemToDelete.value.id);

    if (response && response.status === 'success') {
      notification.success('明細項目刪除成功');

      // 從列表中移除已刪除的項目
      detailItems.value = detailItems.value.filter(item => item.id !== itemToDelete.value.id);

      // 通知父組件更新
      emit('updated');
    } else {
      throw new Error(response?.message || '刪除失敗');
    }
  } catch (error) {
    console.error('刪除明細項目失敗:', error);
    notification.error(`刪除失敗: ${error.message || '未知錯誤'}`);
  } finally {
    cancelDelete();
  }
};

// 確認全部刪除
const confirmDeleteAll = () => {
  if (detailItems.value.length === 0) {
    notification.info('沒有可刪除的項目');
    return;
  }
  showDeleteAllConfirm.value = true;
};

// 取消全部刪除
const cancelDeleteAll = () => {
  showDeleteAllConfirm.value = false;
};

// 刪除所有項目
const deleteAllItems = async () => {
  try {
    const response = await api.materialDetailSheet.deleteAll({
      bomId: props.bomId,
      // 移除 materialId 參數，使明細表在整個 BOM 中通用
      sheetType: props.sheetType
    });

    if (response && response.status === 'success') {
      notification.success('所有明細項目刪除成功');

      // 清空列表
      detailItems.value = [];

      // 通知父組件更新
      emit('updated');
    } else {
      throw new Error(response?.message || '刪除失敗');
    }
  } catch (error) {
    console.error('刪除所有明細項目失敗:', error);
    notification.error(`刪除失敗: ${error.message || '未知錯誤'}`);
  } finally {
    cancelDeleteAll();
  }
};

// 關閉對話框
const closeDialog = () => {
  emit('close');
};

// 組件掛載時獲取資料
onMounted(async () => {
  if (props.visible) {
    await fetchDetailItems();
  }
});
</script>

<style scoped>
.material-detail-sheet-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.material-detail-sheet-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
}

.close-btn:hover {
  color: #111827;
}

.sheet-info {
  margin-bottom: 24px;
}

.sheet-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.sheet-title {
  text-align: center;
  margin-bottom: 16px;
}

.sheet-title h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #111827;
}

.sheet-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-label {
  font-weight: 500;
  color: #4b5563;
}

.meta-value {
  color: #111827;
}

.sheet-table-container {
  overflow-x: auto;
}

.sheet-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.sheet-table th,
.sheet-table td {
  padding: 12px;
  text-align: left;
  border: 1px solid #e5e7eb;
}

.sheet-table th {
  background-color: #f3f4f6;
  font-weight: 500;
  color: #4b5563;
  white-space: nowrap;
}

.sheet-table .no-data-message {
  text-align: center;
  color: #6b7280;
  padding: 24px;
}

.total-row {
  font-weight: bold;
  background-color: #f9fafb;
}

.text-right {
  text-align: right;
}

.sheet-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.import-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.import-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
}

.import-btn:hover {
  background-color: #059669;
}

.delete-all-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-all-btn:hover {
  background-color: #dc2626;
}

.import-status {
  font-size: 0.875rem;
  color: #4b5563;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.cancel-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  background-color: #e5e7eb;
  color: #4b5563;
}

.cancel-btn:hover {
  background-color: #d1d5db;
}

/* 刪除按鈕樣式 */
.action-cell {
  text-align: center;
  white-space: nowrap;
}

.delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.delete-btn:hover {
  background-color: #fee2e2;
}

/* 確認刪除對話框樣式 */
.delete-confirm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1200;
}

.delete-confirm-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  padding: 24px;
  width: 90%;
  max-width: 400px;
}

.delete-confirm-content h3 {
  margin-top: 0;
  color: #111827;
  font-size: 1.25rem;
  margin-bottom: 16px;
}

.item-info {
  background-color: #f9fafb;
  padding: 12px;
  border-radius: 4px;
  margin: 16px 0;
  font-size: 0.875rem;
}

.warning-text {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 4px;
  margin: 16px 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.confirm-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  background-color: #ef4444;
  color: white;
}

.confirm-btn:hover {
  background-color: #dc2626;
}

/* 特殊分組樣式 */
.special-group {
  background-color: #fef3c7; /* 淡黃色背景 */
  font-weight: bold;
}

/* 新增斑馬紋樣式 */
.zebra-even, .zebra-even-row td {
  background-color: #ffffff; /* 白色背景 */
  font-weight: bold;
}

.zebra-odd, .zebra-odd-row td {
  background-color: #f9fafb; /* 更淡的灰色背景 */
  font-weight: bold;
}

/* 特殊皮料行的樣式 */
.special-leather-row td {
  background-color: #fffbeb; /* 非常淡的黃色背景，與特殊組匹配 */
}

.group-note {
  display: block;
  font-size: 0.8rem;
  color: #b45309; /* 深黃色文字 */
  font-weight: normal;
}
</style>
