<template>
  <div class="material-transfer-overlay" v-if="visible">
    <div class="material-transfer-container">
      <div class="material-transfer-header">
        <h3>原物料調撥單</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="material-transfer-content">
        <form @submit.prevent="handleSubmit">
          <div class="form-section">
            <h4>調撥資訊</h4>
            <div class="form-group">
              <label>申請人 <span class="required">*</span></label>
              <input type="text" v-model="formData.requester" required placeholder="請輸入申請人姓名">
            </div>

            <div class="form-group">
              <label>調出倉庫 <span class="required">*</span></label>
              <select v-model="formData.from_warehouse" required @change="handleWarehouseChange">
                <option value="">請選擇倉庫</option>
                <option value="台北部">台北部</option>
                <option value="廠務部A">廠務部A</option>
                <option value="廠務部B">廠務部B</option>
                <option value="廠務部C">廠務部C</option>
                <option value="廠務部D">廠務部D</option>
                <option value="廠務部E">廠務部E</option>
              </select>
            </div>

            <div class="form-group">
              <label>調入倉庫 <span class="required">*</span></label>
              <select v-model="formData.to_warehouse" required>
                <option value="">請選擇倉庫</option>
                <option v-for="warehouse in availableWarehouses" :key="warehouse" :value="warehouse">{{ warehouse }}</option>
              </select>
            </div>

            <div class="form-group">
              <label>備註</label>
              <textarea v-model="formData.notes" placeholder="請輸入備註"></textarea>
            </div>
          </div>

          <div class="form-section" v-if="formData.from_warehouse">
            <h4>原物料選擇</h4>
            <div class="materials-selection">
              <div class="material-search">
                <input
                  type="text"
                  v-model="searchQuery"
                  placeholder="搜尋原物料（材料編號或名稱）"
                  @input="searchMaterials"
                >
              </div>

              <div class="materials-list-container">
                <table class="materials-list">
                  <thead>
                    <tr>
                      <th style="width: 40px;">選擇</th>
                      <th>倉庫</th>
                      <th>材料編號</th>
                      <th>材料名稱</th>
                      <th>規格</th>
                      <th>可用數量</th>
                      <th>調撥數量</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-if="loading">
                      <td colspan="7" class="loading-data">載入中...</td>
                    </tr>
                    <tr v-else-if="filteredMaterials.length === 0">
                      <td colspan="7" class="no-data">無符合條件的原物料</td>
                    </tr>
                    <tr v-for="material in filteredMaterials" :key="material.id" :class="{'selected-row': selectedMaterials.some(item => item.id === material.id)}">
                      <td>
                        <input
                          type="checkbox"
                          :value="material.id"
                          v-model="selectedMaterialIds"
                          :disabled="material.available_quantity <= 0"
                          @change="handleMaterialSelect(material)"
                        >
                      </td>
                      <td>{{ material.storage_location }}</td>
                      <td>{{ material.code }}</td>
                      <td>{{ material.name }}</td>
                      <td>{{ material.specification }}</td>
                      <td>{{ material.available_quantity ? Number(material.available_quantity).toFixed(2) : '-' }}</td>
                      <td>
                        <!-- 如果是皮革材料，數量固定為1，不可編輯 -->
                        <div class="quantity-input-container" v-if="selectedMaterialIds.includes(material.id) && !material.isLeather">
                          <button
                            type="button"
                            class="quantity-btn"
                            @click="decrementQuantity(material.id)"
                            :disabled="getMaterialQuantity(material.id).quantity <= 0"
                          >
                            -
                          </button>
                          <input
                            type="text"
                            v-model="getMaterialQuantity(material.id).quantity"
                            :max="material.available_quantity"
                            class="quantity-input"
                            @change="validateQuantity(material)"
                          >
                          <button
                            type="button"
                            class="quantity-btn"
                            @click="incrementQuantity(material.id)"
                            :disabled="getMaterialQuantity(material.id).quantity >= material.available_quantity"
                          >
                            +
                          </button>
                        </div>
                        <!-- 皮革材料固定為1張 -->
                        <span v-else-if="selectedMaterialIds.includes(material.id) && material.isLeather">
                          1 張
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="selected-materials" v-if="selectedMaterials.length > 0">
                <h4>已選擇的原物料</h4>
                <table class="selected-materials-table">
                  <thead>
                    <tr>
                      <th>材料編號</th>
                      <th>材料名稱</th>
                      <th>皮革編號</th>
                      <th>調撥數量</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in selectedMaterials" :key="item.id">
                      <td>{{ item.code }}</td>
                      <td>{{ item.name }}</td>
                      <td>
                        <!-- 皮革編號選擇 -->
                        <select
                          v-if="item.isLeather && item.leatherDetails && item.leatherDetails.length > 0"
                          v-model="item.selectedLeatherId"
                          class="leather-select"
                        >
                          <option value="">請選擇皮革編號</option>
                          <option
                            v-for="leather in item.leatherDetails"
                            :key="leather.id"
                            :value="leather.id"
                          >
                            {{ leather.leather_code }} (面積: {{ leather.area }} m²)
                          </option>
                        </select>
                        <span v-else>-</span>
                      </td>
                      <td>
                        <!-- 皮革材料固定為1張 -->
                        <span v-if="item.isLeather">1 張</span>
                        <!-- 普通材料顯示數量 -->
                        <span v-else>{{ item.quantity ? Number(item.quantity).toFixed(2) : '-' }}</span>
                      </td>
                      <td>
                        <button type="button" class="remove-btn" @click="removeMaterial(item.id)">
                          <i class="fas fa-trash-alt"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="closeModal">取消</button>
            <button
              type="submit"
              class="submit-btn"
              :disabled="isSubmitting || selectedMaterials.length === 0 || !formData.requester || !formData.from_warehouse || !formData.to_warehouse || formData.from_warehouse === formData.to_warehouse"
            >
              {{ isSubmitting ? '提交中...' : '提交調撥單' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { debounce } from 'lodash-es';
import api from '@/services/api';
import { useNotification } from '@/services/notificationService';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userDepartment: {
    type: Number,
    default: null
  }
});

// Emit events
const emit = defineEmits(['close', 'submit-success']);

// 通知服務
const notification = useNotification();

// 表單數據
const formData = ref({
  requester: '',
  from_warehouse: '',
  to_warehouse: '',
  notes: ''
});

// 原物料相關
const materials = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedMaterialIds = ref([]);
const selectedMaterials = ref([]);
const isSubmitting = ref(false);

// 皮革相關
const leatherMaterials = ref([]);
const loadingLeather = ref(false);

// 可用的目標倉庫
const availableWarehouses = computed(() => {
  const allWarehouses = ['台北部', '廠務部A', '廠務部B', '廠務部C', '廠務部D', '廠務部E'];
  return allWarehouses.filter(w => w !== formData.value.from_warehouse);
});

// 處理倉庫變化
const handleWarehouseChange = async () => {
  if (formData.value.from_warehouse) {
    // 重置選擇的原物料
    selectedMaterialIds.value = [];
    selectedMaterials.value = [];
    formData.value.to_warehouse = '';

    // 重新獲取原物料
    await fetchMaterialsByWarehouse();
  } else {
    materials.value = [];
  }
};

// 獲取指定倉庫的原物料列表
const fetchMaterialsByWarehouse = async () => {
  try {
    loading.value = true;

    if (!formData.value.from_warehouse) {
      materials.value = [];
      return;
    }

    console.log(`正在獲取 ${formData.value.from_warehouse} 的原物料列表...`);
    const response = await api.materials.getByDepartment(formData.value.from_warehouse, searchQuery.value);

    if (response && Array.isArray(response)) {
      // 只顯示有庫存的原物料，並按照代碼排序
      // 過濾掉皮革類別的原物料，因為我們會從 leather_materials 表中獲取皮革材料
      materials.value = response
        .filter(item => Number(item.available_quantity) > 0 && item.category !== '皮料')
        .sort((a, b) => {
          if (a.code < b.code) return -1;
          if (a.code > b.code) return 1;
          return 0;
        });

      console.log(`獲取到 ${materials.value.length} 個非皮革原物料`);

      // 如果是廠務部倉庫，直接從 leather_materials 表中獲取皮革材料
      if (formData.value.from_warehouse.startsWith('廠務部')) {
        await fetchLeatherMaterials();
      }
    } else {
      console.log('未獲取到有效的原物料數據', response);
      materials.value = [];
    }
  } catch (error) {
    console.error('獲取倉庫原物料數據失敗:', error);
    notification.error('獲取倉庫原物料數據失敗: ' + (error.message || '未知錯誤'));
    materials.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜尋過濾後的原物料
const filteredMaterials = computed(() => {
  if (!searchQuery.value) return materials.value;

  const query = searchQuery.value.toLowerCase();
  return materials.value.filter(material => {
    return (material.code && material.code.toLowerCase().includes(query)) ||
           (material.name && material.name.toLowerCase().includes(query));
  });
});

// 搜尋原物料 (防抖處理)
const searchMaterials = debounce(async () => {
  if (formData.value.from_warehouse) {
    await fetchMaterialsByWarehouse();
  }
}, 300);

// 獲取皮革材料詳細信息
const fetchLeatherMaterials = async () => {
  try {
    loadingLeather.value = true;

    // 直接從 leather_materials 表中獲取皮革材料
    const response = await api.materials.leatherMaterials.getAll({
      warehouse: formData.value.from_warehouse
    });

    if (response && response.data && response.data.length > 0) {
      // 將皮革材料存入 leatherMaterials
      leatherMaterials.value = response.data;

      // 將皮革材料轉換為原物料格式，並添加到 materials 列表中
      // 首先按照 material_id 分組皮革材料
      const leatherByMaterialId = {};
      leatherMaterials.value.forEach(leather => {
        if (!leatherByMaterialId[leather.material_id]) {
          leatherByMaterialId[leather.material_id] = [];
        }
        leatherByMaterialId[leather.material_id].push(leather);
      });

      // 為每種皮革材料創建一個原物料項目
      for (const materialId in leatherByMaterialId) {
        const leathers = leatherByMaterialId[materialId];
        if (leathers.length > 0) {
          // 使用第一個皮革的信息作為原物料信息
          const firstLeather = leathers[0];

          // 將皮革材料添加到 materials 列表中
          materials.value.push({
            id: firstLeather.material_id,
            code: firstLeather.material_code,
            name: firstLeather.material_name,
            category: '皮料',
            storage_location: firstLeather.warehouse,
            available_quantity: leathers.length, // 可用數量為皮革材料的數量
            isLeather: true, // 標記為皮革材料
            leatherDetails: leathers // 存儲皮革詳細信息
          });
        }
      }

      // 重新排序 materials 列表
      materials.value.sort((a, b) => {
        if (a.code < b.code) return -1;
        if (a.code > b.code) return 1;
        return 0;
      });

      console.log(`獲取到 ${leatherMaterials.value.length} 個皮革材料，共 ${Object.keys(leatherByMaterialId).length} 種皮革`);
    } else {
      leatherMaterials.value = [];
      console.log('未獲取到皮革材料');
    }
  } catch (error) {
    console.error('獲取皮革材料詳細信息失敗:', error);
    leatherMaterials.value = [];
  } finally {
    loadingLeather.value = false;
  }
};

// 處理原物料勾選
const handleMaterialSelect = (material) => {
  const isSelected = selectedMaterialIds.value.includes(material.id);

  if (isSelected) {
    // 如果勾選了原物料，將其添加到已選擇列表
    const existingIndex = selectedMaterials.value.findIndex(item => item.id === material.id);

    if (existingIndex === -1) {
      // 如果是皮革材料，直接使用其已經包含的皮革詳細信息
      if (material.isLeather) {
        // 直接使用原物料中的皮革詳細信息
        selectedMaterials.value.push({
          id: material.id,
          code: material.code,
          name: material.name,
          specification: material.specification || '',
          storage_location: material.storage_location,
          available_quantity: material.available_quantity,
          quantity: 1, // 皮革材料固定為1張
          isLeather: true,
          leatherDetails: material.leatherDetails,
          selectedLeatherId: null // 預設沒有選擇特定的皮革
        });
      } else {
        // 如果不是皮革材料，則添加普通項目
        selectedMaterials.value.push({
          id: material.id,
          code: material.code,
          name: material.name,
          specification: material.specification || '',
          storage_location: material.storage_location,
          available_quantity: material.available_quantity,
          quantity: 0, // 默認調撥數量為0
          isLeather: false
        });
      }
    }
  } else {
    // 如果取消勾選，則從已選擇列表中移除
    selectedMaterials.value = selectedMaterials.value.filter(item => item.id !== material.id);
  }
};

// 獲取原物料數量設置
const getMaterialQuantity = (materialId) => {
  const material = selectedMaterials.value.find(item => item.id === materialId);
  if (!material) {
    return { quantity: 0 };
  }
  return material;
};

// 驗證數量是否合法
const validateQuantity = (material) => {
  const selectedMaterial = selectedMaterials.value.find(item => item.id === material.id);
  if (!selectedMaterial) return;

  // 如果是皮革材料，數量固定為1
  if (selectedMaterial.isLeather) {
    selectedMaterial.quantity = 1;
    return;
  }

  // 確保數量是數字且不是NaN
  let inputQuantity = Number(selectedMaterial.quantity);
  if (isNaN(inputQuantity)) {
    selectedMaterial.quantity = 0;
    return;
  }

  // 如果數量小於0，設置為0
  if (inputQuantity < 0) {
    selectedMaterial.quantity = 0;
    return;
  }

  // 四捨五入到兩位小數
  selectedMaterial.quantity = Math.round(inputQuantity * 100) / 100;

  // 如果數量大於可用數量，限制為可用數量
  const availableQuantity = Number(material.available_quantity);
  if (selectedMaterial.quantity > availableQuantity) {
    selectedMaterial.quantity = Math.round(availableQuantity * 100) / 100;
    notification.warning(`調撥數量不能超過可用數量 ${availableQuantity.toFixed(2)}`);
  }
};

// 增加數量（以1為單位）
const incrementQuantity = (materialId) => {
  const material = selectedMaterials.value.find(item => item.id === materialId);
  if (material) {
    // 將數量增加 1
    material.quantity = Math.min(
      Number(material.available_quantity),
      Math.round((Number(material.quantity) + 1) * 100) / 100
    );
  }
};

// 減少數量（以1為單位）
const decrementQuantity = (materialId) => {
  const material = selectedMaterials.value.find(item => item.id === materialId);
  if (material) {
    // 將數量減少 1，但不小於 0
    material.quantity = Math.max(0, Math.round((Number(material.quantity) - 1) * 100) / 100);
  }
};

// 移除已選擇的原物料
const removeMaterial = (materialId) => {
  selectedMaterialIds.value = selectedMaterialIds.value.filter(id => id !== materialId);
  selectedMaterials.value = selectedMaterials.value.filter(item => item.id !== materialId);
};

// 提交調撥單
const handleSubmit = async () => {
  if (selectedMaterials.value.length === 0) {
    notification.warning('請至少選擇一項原物料');
    return;
  }

  if (formData.value.from_warehouse === formData.value.to_warehouse) {
    notification.warning('調出倉庫和調入倉庫不能相同');
    return;
  }

  try {
    isSubmitting.value = true;

    // 為每個選擇的原物料創建調撥交易記錄
    const promises = selectedMaterials.value.map(material => {
      // 確保數量是有效的數字且四捨五入到兩位小數
      const quantity = Math.round(Number(material.quantity) * 100) / 100;

      // 如果數量為0或無效，則跳過此原物料
      if (quantity <= 0) {
        notification.warning(`${material.code} ${material.name} 的調撥數量必須大於0`);
        return Promise.reject(new Error('調撥數量必須大於0'));
      }

      // 如果是皮革材料且需要選擇特定皮革，但沒有選擇
      if (material.isLeather && material.leatherDetails && material.leatherDetails.length > 0) {
        if (!material.selectedLeatherId) {
          notification.warning(`${material.code} ${material.name} 需要選擇皮革編號`);
          return Promise.reject(new Error('需要選擇皮革編號'));
        }

        // 獲取選擇的皮革詳細信息
        const selectedLeather = material.leatherDetails.find(item => item.id === material.selectedLeatherId);
        if (!selectedLeather) {
          notification.warning(`${material.code} ${material.name} 的皮革編號無效`);
          return Promise.reject(new Error('皮革編號無效'));
        }

        // 包含皮革編號信息在備註中
        const leatherNote = `皮革編號: ${selectedLeather.leather_code}, 面積: ${selectedLeather.area} m²`;
        const notes = formData.value.notes ? `${formData.value.notes}, ${leatherNote}` : leatherNote;

        // 對於皮革材料，使用現有的 createTransfer API，但添加皮革相關的額外信息
        // 皮革材料固定為1張
        try {
          // 先嘗試直接調撥，如果失敗，則顯示更具體的錯誤訊息
          return api.materials.createTransfer({
            material_id: material.id,
            quantity: 1, // 皮革材料固定為1張
            from_warehouse: formData.value.from_warehouse,
            to_warehouse: formData.value.to_warehouse,
            requester: formData.value.requester,
            notes: notes,
            is_leather: true, // 標記為皮革材料
            leather_id: selectedLeather.id, // 皮革ID
            leather_code: selectedLeather.leather_code, // 皮革編號
            leather_area: selectedLeather.area, // 皮革面積
            skip_quantity_check: true, // 跳過數量檢查，因為皮革材料的數量在 leather_materials 表中
            leather_quantity_field: 'quantity' // 指定皮革材料數量存儲在 leather_materials 表的 quantity 欄位中
          });
        } catch (error) {
          console.error('皮革材料調撥失敗:', error);
          notification.error(`皮革材料調撥失敗: ${error.message || '未知錯誤'}`);
          throw error;
        }
      }

      // 如果不是皮革材料或不需要選擇特定皮革，則正常調撥
      return api.materials.createTransfer({
        material_id: material.id,
        quantity: quantity,
        from_warehouse: formData.value.from_warehouse,
        to_warehouse: formData.value.to_warehouse,
        requester: formData.value.requester,
        notes: formData.value.notes || undefined
      });
    });

    try {
      await Promise.all(promises);
      notification.success('原物料調撥單提交成功');
      emit('submit-success');
      closeModal();
    } catch (err) {
      console.error('調撥數量驗證失敗:', err);

      // 如果是皮革材料的可用數量不足錯誤，顯示更具體的錯誤訊息
      if (err.message && err.message.includes('可用數量不足')) {
        // 檢查是否有皮革材料
        const hasLeatherMaterial = selectedMaterials.value.some(material => material.isLeather);
        if (hasLeatherMaterial) {
          notification.error('皮革材料調撥失敗: 從 materials 表中無法找到皮革材料的庫存資訊。請聯絡系統管理員修正後端代碼，使其能夠從 leather_materials 表的 quantity 欄位中獲取皮革材料的庫存資訊。');
          return;
        }
      }

      // 顯示一般錯誤訊息
      notification.error(`調撥失敗: ${err.message || '未知錯誤'}`);
    }
  } catch (error) {
    console.error('提交調撥單失敗:', error);
    notification.error('提交調撥單失敗');
  } finally {
    isSubmitting.value = false;
  }
};

// 關閉彈窗並重置表單
const closeModal = () => {
  formData.value = {
    requester: '',
    from_warehouse: '',
    to_warehouse: '',
    notes: ''
  };
  searchQuery.value = '';
  selectedMaterialIds.value = [];
  selectedMaterials.value = [];
  materials.value = [];
  emit('close');
};

// 當彈窗可見時重新獲取原物料
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 根據用戶部門設置默認倉庫
    if (props.userDepartment === 0 && !formData.value.from_warehouse) {
      formData.value.from_warehouse = '台北部';
    } else if (props.userDepartment === 1 && !formData.value.from_warehouse) {
      formData.value.from_warehouse = '廠務部A';
    }

    // 如果已經選擇了倉庫，立即獲取原物料
    if (formData.value.from_warehouse) {
      try {
        await fetchMaterialsByWarehouse();
      } catch (error) {
        console.error('自動載入原物料失敗:', error);
        notification.error('自動載入原物料失敗，請手動選擇倉庫');
      }
    }
  }
});
</script>

<style scoped>
.material-transfer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.material-transfer-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.material-transfer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.material-transfer-header h3 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
}

.material-transfer-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

.form-section {
  margin-bottom: 20px;
}

.form-section h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  color: #444;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group label .required {
  color: #e53935;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #2196F3;
  outline: none;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.materials-selection {
  margin-top: 15px;
}

.material-search {
  margin-bottom: 15px;
  position: relative;
}

.material-search input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.material-search input:focus {
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  outline: none;
}

.material-search::before {
  content: '\f002';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
}

.materials-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.materials-list {
  width: 100%;
  border-collapse: collapse;
}

.materials-list th,
.materials-list td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.materials-list th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid #ddd;
}

.materials-list tr:hover {
  background-color: #f9f9f9;
}

.materials-list .loading-data,
.materials-list .no-data {
  text-align: center;
  padding: 15px;
  color: #777;
}

.quantity-input {
  width: 80px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: right;
}

.selected-row {
  background-color: #f0f7ff;
}

.selected-materials {
  margin-top: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-materials h4 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #333;
  font-size: 1.1rem;
}

.selected-materials-table {
  width: 100%;
  border-collapse: collapse;
}

.selected-materials-table th,
.selected-materials-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.selected-materials-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #ddd;
}

.remove-btn {
  background: none;
  border: none;
  color: #F44336;
  cursor: pointer;
  transition: opacity 0.3s;
  padding: 5px;
}

.remove-btn:hover {
  opacity: 0.8;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border-radius: 4px;
  border: none;
  font-weight: 500;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.submit-btn {
  background-color: #9C27B0;
  color: white;
}

.submit-btn:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

.leather-select {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
}

.quantity-input-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-input {
  width: 60px;
  text-align: center;
  margin: 0 5px;
  appearance: textfield; /* Standard */
  -moz-appearance: textfield; /* Firefox */
}

/* 移除數字輸入框的上下箭頭 */
.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>