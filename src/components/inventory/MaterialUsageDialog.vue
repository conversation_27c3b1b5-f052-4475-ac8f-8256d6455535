<template>
  <div class="material-usage-overlay" v-if="visible">
    <div class="material-usage-container">
      <div class="material-usage-header">
        <h3>原物料領用單</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="material-usage-content">
        <form @submit.prevent="handleSubmit">
          <div class="form-section">
            <h4>領用資訊</h4>
            <div class="form-group">
              <label>申請人 <span class="required">*</span></label>
              <input type="text" v-model="formData.requester" required placeholder="請輸入申請人姓名">
            </div>

            <div class="form-group">
              <label>領用部門 <span class="required">*</span></label>
              <select v-model="formData.department" required @change="handleDepartmentChange">
                <option value="">請選擇部門</option>
                <option value="台北部">台北部</option>
                <option value="廠務部A">廠務部A</option>
                <option value="廠務部B">廠務部B</option>
                <option value="廠務部C">廠務部C</option>
                <option value="廠務部D">廠務部D</option>
                <option value="廠務部E">廠務部E</option>
              </select>
            </div>

            <div class="form-group">
              <label>用途</label>
              <input type="text" v-model="formData.purpose" placeholder="請輸入用途">
            </div>

            <div class="form-group">
              <label>備註</label>
              <textarea v-model="formData.notes" placeholder="請輸入備註"></textarea>
            </div>
          </div>

          <div class="form-section" v-if="formData.department">
            <h4>原物料選擇</h4>
            <div class="materials-selection">
              <div class="material-search">
                <input
                  type="text"
                  v-model="searchQuery"
                  placeholder="搜尋原物料（材料編號或名稱）"
                  @input="searchMaterials"
                >
              </div>

              <div class="materials-list-container">
                <table class="materials-list">
                  <thead>
                    <tr>
                      <th style="width: 40px;">選擇</th>
                      <th>倉庫</th>
                      <th>材料編號</th>
                      <th>材料名稱</th>
                      <th>規格</th>
                      <th>可用數量</th>
                      <th>領用數量</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-if="loading">
                      <td colspan="7" class="loading-data">載入中...</td>
                    </tr>
                    <tr v-else-if="filteredMaterials.length === 0">
                      <td colspan="7" class="no-data">無符合條件的原物料</td>
                    </tr>
                    <tr v-for="material in filteredMaterials" :key="material.id" :class="{'selected-row': selectedMaterials.some(item => item.id === material.id)}">
                      <td>
                        <input
                          type="checkbox"
                          :value="material.id"
                          v-model="selectedMaterialIds"
                          :disabled="material.available_quantity <= 0"
                          @change="handleMaterialSelect(material)"
                        >
                      </td>
                      <td>{{ material.storage_location }}</td>
                      <td>{{ material.code }}</td>
                      <td>{{ material.name }}</td>
                      <td>{{ material.specification }}</td>
                      <td>{{ material.available_quantity ? Number(material.available_quantity).toFixed(2) : '-' }}</td>
                      <td>
                        <input
                          v-if="selectedMaterialIds.includes(material.id)"
                          type="number"
                          v-model="getMaterialQuantity(material.id).quantity"
                          :max="material.available_quantity"
                          min="0.01"
                          step="1"
                          class="quantity-input"
                          @change="validateQuantity(material)"
                        >
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="selected-materials" v-if="selectedMaterials.length > 0">
                <h4>已選擇的原物料</h4>
                <table class="selected-materials-table">
                  <thead>
                    <tr>
                      <th>材料編號</th>
                      <th>材料名稱</th>
                      <th>領用數量</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in selectedMaterials" :key="item.id">
                      <td>{{ item.code }}</td>
                      <td>{{ item.name }}</td>
                      <td>{{ item.quantity ? Number(item.quantity).toFixed(2) : '-' }}</td>
                      <td>
                        <button type="button" class="remove-btn" @click="removeMaterial(item.id)">
                          <i class="fas fa-trash-alt"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="closeModal">取消</button>
            <button
              type="submit"
              class="submit-btn"
              :disabled="isSubmitting || selectedMaterials.length === 0 || !formData.requester || !formData.department"
            >
              {{ isSubmitting ? '提交中...' : '提交領用單' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { debounce } from 'lodash-es';
import api from '@/services/api';
import { useNotification } from '@/services/notificationService';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userDepartment: {
    type: Number,
    default: null
  }
});

// Emit events
const emit = defineEmits(['close', 'submit-success']);

// 通知服務
const notification = useNotification();

// 表單數據
const formData = ref({
  requester: '',
  department: '',
  purpose: '',
  notes: ''
});

// 原物料相關
const materials = ref([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedMaterialIds = ref([]);
const selectedMaterials = ref([]);
const isSubmitting = ref(false);

// 處理部門變化
const handleDepartmentChange = async () => {
  if (formData.value.department) {
    // 重置選擇的原物料
    selectedMaterialIds.value = [];
    selectedMaterials.value = [];

    // 重新獲取原物料
    await fetchMaterialsByDepartment();
  } else {
    materials.value = [];
  }
};

// 獲取指定部門的原物料列表
const fetchMaterialsByDepartment = async () => {
  try {
    loading.value = true;

    if (!formData.value.department) {
      materials.value = [];
      return;
    }

    const response = await api.materials.getByDepartment(formData.value.department, searchQuery.value);

    if (response && Array.isArray(response)) {
      // 只顯示有庫存的原物料，並按照代碼排序
      materials.value = response
        .filter(item => Number(item.available_quantity) > 0)
        .sort((a, b) => {
          if (a.code < b.code) return -1;
          if (a.code > b.code) return 1;
          return 0;
        });
    } else {
      materials.value = [];
    }
  } catch (error) {
    console.error('獲取部門原物料數據失敗:', error);
    notification.error('獲取部門原物料數據失敗');
  } finally {
    loading.value = false;
  }
};

// 搜尋過濾後的原物料
const filteredMaterials = computed(() => {
  const query = searchQuery.value.toLowerCase().trim();
  if (!query) return materials.value;

  return materials.value.filter(material =>
    (material.code && material.code.toLowerCase().includes(query)) ||
    (material.name && material.name.toLowerCase().includes(query))
  );
});

// 防抖搜尋功能
const searchMaterials = debounce(async () => {
  if (formData.value.department) {
    await fetchMaterialsByDepartment();
  }
}, 300);

// 處理原物料選擇
const handleMaterialSelect = (material) => {
  const index = selectedMaterialIds.value.indexOf(material.id);

  if (index > -1) {
    // 如果已選擇，添加到已選列表
    const existingIndex = selectedMaterials.value.findIndex(item => item.id === material.id);

    if (existingIndex === -1) {
      selectedMaterials.value.push({
        id: material.id,
        code: material.code,
        name: material.name,
        available_quantity: material.available_quantity,
        quantity: null // 預設數量為空
      });
    }
  } else {
    // 如果取消選擇，從已選列表移除
    const existingIndex = selectedMaterials.value.findIndex(item => item.id === material.id);
    if (existingIndex !== -1) {
      selectedMaterials.value.splice(existingIndex, 1);
    }
  }
};

// 獲取特定原物料的數量設置
const getMaterialQuantity = (materialId) => {
  const material = selectedMaterials.value.find(item => item.id === materialId);
  if (!material) {
    return { quantity: 0 };
  }
  return material;
};

// 驗證數量輸入
const validateQuantity = (material) => {
  const selectedMaterial = selectedMaterials.value.find(item => item.id === material.id);
  if (!selectedMaterial) return;

  let quantity = parseFloat(selectedMaterial.quantity);

  // 確保數量在可用範圍內
  if (isNaN(quantity) || quantity <= 0) {
    quantity = 0.01;
  } else if (quantity > material.available_quantity) {
    quantity = material.available_quantity;
    notification.warning(`領用數量不能超過可用數量 ${material.available_quantity}`);
  }

  // 四捨五入到兩位小數
  quantity = Math.round(quantity * 100) / 100;
  selectedMaterial.quantity = quantity;
};

// 移除已選擇的原物料
const removeMaterial = (materialId) => {
  // 從已選清單中移除
  const index = selectedMaterials.value.findIndex(item => item.id === materialId);
  if (index !== -1) {
    selectedMaterials.value.splice(index, 1);
  }

  // 取消勾選對應的 checkbox
  const idIndex = selectedMaterialIds.value.indexOf(materialId);
  if (idIndex !== -1) {
    selectedMaterialIds.value.splice(idIndex, 1);
  }
};

// 處理表單提交
const handleSubmit = async () => {
  if (selectedMaterials.value.length === 0) {
    notification.warning('請至少選擇一項原物料');
    return;
  }

  try {
    isSubmitting.value = true;

    // 為每個選擇的原物料創建一個交易記錄
    const promises = selectedMaterials.value.map(material => {
      return api.materials.createTransaction({
        material_id: material.id,
        quantity: material.quantity,
        transaction_type: '領用',
        requester: formData.value.requester,
        department: formData.value.department,
        purpose: formData.value.purpose || undefined,
        notes: formData.value.notes || undefined
      });
    });

    await Promise.all(promises);

    notification.success('原物料領用單提交成功');
    emit('submit-success');
    closeModal();
  } catch (error) {
    console.error('提交領用單失敗:', error);
    notification.error('提交領用單失敗');
  } finally {
    isSubmitting.value = false;
  }
};

// 關閉彈窗並重置表單
const closeModal = () => {
  formData.value = {
    requester: '',
    department: '',
    purpose: '',
    notes: ''
  };
  searchQuery.value = '';
  selectedMaterialIds.value = [];
  selectedMaterials.value = [];
  materials.value = [];
  emit('close');
};

// 組件掛載時獲取原物料數據
onMounted(() => {
  // 根據用戶部門設置默認領用部門
  if (props.userDepartment !== null) {
    formData.value.department = props.userDepartment === 0 ? '台北部' : '廠務部A';
    // 設置部門後獲取該部門的原物料
    handleDepartmentChange();
  }
});

// 當彈窗可見時重新獲取原物料
watch(() => props.visible, async (newVal) => {
  if (newVal && formData.value.department) {
    await fetchMaterialsByDepartment();
  }
});
</script>

<style scoped>
.material-usage-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1010;
}

.material-usage-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.material-usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.material-usage-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #333;
}

.material-usage-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #F44336;
}

input[type="text"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

input[type="text"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  border-color: #2196F3;
  outline: none;
}

textarea {
  height: 80px;
  resize: vertical;
}

.materials-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.material-search {
  margin-bottom: 12px;
}

.materials-list-container {
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

.materials-list,
.selected-materials-table {
  width: 100%;
  border-collapse: collapse;
}

.materials-list th,
.materials-list td,
.selected-materials-table th,
.selected-materials-table td {
  padding: 10px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.materials-list th,
.selected-materials-table th {
  background-color: #f7f7f7;
  position: sticky;
  top: 0;
  z-index: 1;
}

.selected-row {
  background-color: #e3f2fd;
}

.quantity-input {
  width: 80px;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.selected-materials {
  margin-top: 16px;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 16px;
  background-color: #f9f9f9;
}

.selected-materials h4 {
  margin-top: 0;
  margin-bottom: 12px;
}

.remove-btn {
  background: none;
  border: none;
  color: #F44336;
  cursor: pointer;
  transition: opacity 0.3s;
}

.remove-btn:hover {
  opacity: 0.8;
}

.loading-data,
.no-data {
  padding: 20px;
  text-align: center;
  color: #999;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.cancel-btn:hover {
  background-color: #e0e0e0;
}

.submit-btn {
  background-color: #2196F3;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background-color: #1976D2;
}

.submit-btn:disabled {
  background-color: #90caf9;
  cursor: not-allowed;
}
</style>
