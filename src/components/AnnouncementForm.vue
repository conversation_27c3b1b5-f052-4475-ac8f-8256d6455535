<template>
  <div class="announcement-form-overlay" v-if="visible">
    <div class="announcement-form-container">
      <div class="announcement-form-header">
        <h3>{{ editMode ? '編輯公告' : '新增公告' }}</h3>
        <button class="close-btn" @click="cancel">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="announcement-form-content">
        <div class="form-section">
          <div class="form-group">
            <label>主旨：</label>
            <input type="text" v-model="formData.title" placeholder="請輸入公告主旨" required>
          </div>

          <div class="form-group">
            <label>內容：</label>
            <textarea v-model="formData.content" placeholder="請輸入公告內容" rows="10" required></textarea>
          </div>
        </div>

        <div class="form-group">
          <label>公告人：</label>
          <input type="text" v-model="formData.informer" placeholder="請輸入公告人姓名" required>
        </div>
        <div class="form-group">
          <label>公告類型：</label>
          <select v-model="formData.type" required>
            <option value="">請選擇類型</option>
            <option value="類型1">類型1</option>
            <option value="類型2">類型2</option>
            <option value="類型1">測試</option>
          </select>
        </div>

          <div class="form-group">
            <label>公告單位：</label>
            <div class="checkbox-group">
              <label class="checkbox-item">
                <input
                  type="checkbox"
                  value="全體"
                  v-model="selectedUnits"
                  @change="handleAllUnitsChange"
                >
                <span>全體</span>
              </label>
              <label
                class="checkbox-item"
                v-for="unit in otherUnits"
                :key="unit"
                :class="{ 'disabled': isAllUnitsSelected }"
              >
                <input
                  type="checkbox"
                  :value="unit"
                  v-model="selectedUnits"
                  :disabled="isAllUnitsSelected"
                >
                <span>{{ unit }}</span>
              </label>
            </div>
          </div>



        <div v-if="!hideButtons" class="form-actions">
          <button class="save-btn" @click="save">保存</button>
          <button v-if="editMode" class="delete-btn" @click="deleteConfirm">刪除</button>
          <button class="cancel-btn" @click="cancel">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch, computed } from 'vue';
import { showConfirm } from '../services/notificationService';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    editMode: {
      type: Boolean,
      default: false
    },
    hideButtons: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'save', 'delete'],
  data() {
    return {
      formData: {
        id: null,
        type: '',
        announcementUnit: '',
        informer: '',
        title: '',
        content: '',
        createdAt: null,
        display: 1 // 預設顯示公告
      },
      selectedUnits: [],
      otherUnits: ['樣品', '設計', '開發', '業務', '行銷', '會計', '廠務部', '管理部', '技術長', '樣板室', '備料工坊', '塗邊工坊', '包工坊', '夾工坊']
    };
  },
  computed: {
    isAllUnitsSelected() {
      return this.selectedUnits.includes('全體');
    }
  },
  watch: {
    selectedUnits(newValue) {
      this.formData.announcementUnit = newValue.join(',');
    },
    visible(newVal) {
      if (newVal) {
        this.resetFormData();

        if (this.editMode && this.editData) {
          Object.keys(this.editData).forEach(key => {
            if (key in this.formData) {
              this.formData[key] = this.editData[key];
            }
          });

          if (this.editData.announcementUnit) {
            this.selectedUnits = this.editData.announcementUnit.split(',').map(unit => unit.trim());
          }
        }
      }
    }
  },
  methods: {
    resetFormData() {
      Object.keys(this.formData).forEach(key => {
        this.formData[key] = '';
      });
      this.formData.id = null;
      this.formData.createdAt = null;
      this.selectedUnits = [];
    },
    async save() {
      if (!this.formData.type) {
        alert('請選擇公告類型');
        return;
      }

      if (this.selectedUnits.length === 0) {
        alert('請選擇至少一個公告單位');
        return;
      }

      if (!this.formData.informer) {
        alert('請輸入公告人');
        return;
      }

      if (!this.formData.title) {
        alert('請輸入公告主旨');
        return;
      }

      if (!this.formData.content) {
        alert('請輸入公告內容');
        return;
      }

      try {
        this.formData.announcementUnit = this.selectedUnits.join(',');
        this.formData.createdAt = new Date();
        this.$emit('save', { ...this.formData });
      } catch (error) {
        console.error('保存失敗:', error);
      }
    },
    deleteConfirm() {
      if (!this.editMode || !this.editData) {
        return;
      }

      this.$emit('delete', this.editData);
    },
    cancel() {
      this.$emit('close');
    },
    handleAllUnitsChange() {
      if (this.selectedUnits.includes('全體')) {
        this.selectedUnits = ['全體'];
      } else {
        this.selectedUnits = [];
      }
    }
  }
};
</script>

<style scoped>
.announcement-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.announcement-form-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.announcement-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.announcement-form-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.announcement-form-content {
  padding: 20px;
  overflow-y: auto;
  max-height: calc(90vh - 62px);
}

.form-section {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  color: #555;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #2196f3;
  outline: none;
}

.form-group textarea {
  min-height: 150px;
  resize: vertical;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.checkbox-item input {
  width: auto;
}

.checkbox-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.save-btn,
.delete-btn,
.cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  background-color: #059669;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
}

.delete-btn:hover {
  background-color: #dc2626;
}

.cancel-btn {
  background-color: #9ca3af;
  color: white;
}

.cancel-btn:hover {
  background-color: #d5d5d5;
}
</style>
