<template>
  <div class="setting-manager-overlay" v-if="visible">
    <div class="setting-manager-container">
      <div class="setting-manager-header">
        <h3>{{ title }}</h3>
        <button class="close-btn" @click="closeModal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="setting-manager-content">
        <div class="table-container">
          <table class="setting-table">
            <thead>
              <tr>
                <th>排序</th>
                <th>名稱</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in items" :key="index">
                <td>
                  <div class="sort-buttons">
                    <button @click="moveUp(index)" :disabled="index === 0" title="上移">
                      <i class="fas fa-arrow-up"></i>
                    </button>
                    <button @click="moveDown(index)" :disabled="index === items.length - 1" title="下移">
                      <i class="fas fa-arrow-down"></i>
                    </button>
                  </div>
                </td>
                <td>
                  <div v-if="editingIndex === index" class="edit-input-container">
                    <input type="text" v-model="editingName" class="edit-input" @keyup.enter="saveEdit" />
                  </div>
                  <span v-else>{{ item.name }}</span>
                </td>
                <td>
                  <div class="action-buttons">
                    <button v-if="editingIndex === index" @click="saveEdit" title="儲存">
                      <i class="fas fa-save"></i>
                    </button>
                    <button v-else @click="startEdit(index)" title="編輯">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button @click="deleteItem(index)" title="刪除">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="add-item-container">
          <div class="add-item-input">
            <input type="text" v-model="newItemName" placeholder="輸入新名稱"/>
            <button @click="addItem" :disabled="!newItemName.trim()">新增</button>
          </div>
        </div>

        <div class="button-group">
          <button class="save-btn" @click="saveChanges" :disabled="isSaving">
            {{ isSaving ? '儲存中...' : '儲存變更' }}
          </button>
          <button class="cancel-btn" @click="closeModal" :disabled="isSaving">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import api from '../services/api';
import { useNotification } from '../services/notificationService';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  settingType: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: '設定管理'
  }
});

const emit = defineEmits(['close', 'updated']);

const notification = useNotification();
const items = ref([]);
const newItemName = ref('');
const editingIndex = ref(-1);
const editingName = ref('');
const isSaving = ref(false);

// 獲取設定列表
const fetchItems = async () => {
  try {
    const response = await api.settingRecord.getAll({
      type: props.settingType
    });

    if (response && response.data) {
      // 根據 sort_order 排序
      items.value = response.data.sort((a, b) => a.sort_order - b.sort_order);
    }
  } catch (error) {
    notification.error(`獲取${props.title}列表失敗`);
    console.error(`獲取${props.title}列表失敗:`, error);
  }
};

// 新增項目
const addItem = async () => {
  if (!newItemName.value.trim()) return;

  const newItem = {
    name: newItemName.value.trim(),
    sort_order: items.value.length
  };

  items.value.push(newItem);
  newItemName.value = '';
};

// 開始編輯
const startEdit = (index) => {
  editingIndex.value = index;
  editingName.value = items.value[index].name;
};

// 儲存編輯
const saveEdit = () => {
  if (editingName.value.trim()) {
    items.value[editingIndex.value].name = editingName.value.trim();
  }
  editingIndex.value = -1;
};

// 刪除項目
const deleteItem = (index) => {
  if (confirm('確定要刪除此項目嗎？')) {
    items.value.splice(index, 1);

    // 更新其餘項目的排序
    items.value.forEach((item, idx) => {
      item.sort_order = idx;
    });
  }
};

// 上移
const moveUp = (index) => {
  if (index > 0) {
    const temp = items.value[index];
    items.value[index] = items.value[index - 1];
    items.value[index - 1] = temp;

    // 更新排序值
    items.value.forEach((item, idx) => {
      item.sort_order = idx;
    });
  }
};

// 下移
const moveDown = (index) => {
  if (index < items.value.length - 1) {
    const temp = items.value[index];
    items.value[index] = items.value[index + 1];
    items.value[index + 1] = temp;

    // 更新排序值
    items.value.forEach((item, idx) => {
      item.sort_order = idx;
    });
  }
};

// 保存所有更改
const saveChanges = async () => {
  try {
    isSaving.value = true;

    // 獲取現有的設定項
    const existingItems = await api.settingRecord.getAll({
      type: props.settingType
    });

    // 刪除所有現有的設定項
    if (existingItems && existingItems.data) {
      for (const item of existingItems.data) {
        await api.settingRecord.delete(item.id);
      }
    }

    // 添加新的設定項
    for (const [index, item] of items.value.entries()) {
      await api.settingRecord.create({
        type: props.settingType,
        name: item.name,
        value: item.name, // 值和名稱相同
        sort_order: index,
        is_active: true
      });
    }

    notification.success(`${props.title}設定已更新`);
    emit('updated', props.settingType);
    emit('close');
  } catch (error) {
    notification.error(`更新${props.title}設定失敗`);
    console.error(`更新${props.title}設定失敗:`, error);
  } finally {
    isSaving.value = false;
  }
};

// 關閉彈窗
const closeModal = () => {
  emit('close');
};

// 監聽彈窗顯示狀態
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchItems();
  }
});

// 元件掛載時獲取設定
onMounted(() => {
  if (props.visible) {
    fetchItems();
  }
});
</script>

<style scoped>
.setting-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1010;
}

.setting-manager-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.setting-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.setting-manager-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
}

.setting-manager-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.table-container {
  margin-bottom: 20px;
}

.setting-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.setting-table th,
.setting-table td {
  border: 1px solid #e0e0e0;
  padding: 12px;
  text-align: left;
}

.setting-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.setting-table td {
  vertical-align: middle;
}

.sort-buttons {
  display: flex;
  gap: 8px;
}

.sort-buttons button,
.action-buttons button {
  background: none;
  border: none;
  color: #2196f3;
  cursor: pointer;
  padding: 4px;
  font-size: 14px;
}

.sort-buttons button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.sort-buttons button:hover:not(:disabled),
.action-buttons button:hover {
  color: #0d47a1;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-buttons button:last-child {
  color: #f44336;
}

.action-buttons button:last-child:hover {
  color: #d32f2f;
}

.add-item-container {
  margin-top: 20px;
  margin-bottom: 30px;
}

.add-item-input {
  display: flex;
  gap: 8px;
}

.add-item-input input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-item-input button {
  padding: 10px 16px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.add-item-input button:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
}

.edit-input-container {
  width: 100%;
}

.edit-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #2196f3;
  border-radius: 4px;
  font-size: 14px;
}

.button-group {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.save-btn, .cancel-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  background-color: #059669;
}

.save-btn:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #9ca3af;
  color: white;
}

.cancel-btn:hover {
  background-color: #4b5563;
}
</style>