import axios from 'axios';
import { API_BASE_URL } from './config';

const materialDetailSheetApi = {
  /**
   * 獲取產品用料明細表項目
   * @param {Object} params - 查詢參數
   * @param {number|string} params.bomId - BOM ID
   * @param {string} params.sheetType - 明細表類型 (主料/副料)
   * @param {number|string} [params.materialId] - 材料 ID（可選）
   * @returns {Promise<Array>} - 明細表項目列表
   */
  getItems: async (params) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/material-detail-sheet`, { params });
      return response.data;
    } catch (error) {
      console.error('獲取產品用料明細表項目失敗:', error);
      throw error;
    }
  },

  /**
   * 匯入Excel檔案
   * @param {FormData} formData - 包含Excel檔案和相關參數的FormData
   * @param {number|string} formData.bomId - BOM ID
   * @param {string} formData.sheetType - 明細表類型 (主料/副料)
   * @param {number|string} [formData.materialId] - 材料 ID（可選）
   * @returns {Promise<Object>} - 匯入結果
   */
  importExcel: async (formData) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/material-detail-sheet/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('匯入Excel檔案失敗:', error);
      throw error;
    }
  },

  /**
   * 創建明細表項目
   * @param {Object} data - 明細表項目數據
   * @returns {Promise<Object>} - 創建結果
   */
  create: async (data) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/material-detail-sheet`, data);
      return response.data;
    } catch (error) {
      console.error('創建明細表項目失敗:', error);
      throw error;
    }
  },

  /**
   * 更新明細表項目
   * @param {number|string} id - 明細表項目ID
   * @param {Object} data - 更新數據
   * @returns {Promise<Object>} - 更新結果
   */
  update: async (id, data) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/material-detail-sheet/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新明細表項目失敗:', error);
      throw error;
    }
  },

  /**
   * 刪除明細表項目
   * @param {number|string} id - 明細表項目ID
   * @returns {Promise<Object>} - 刪除結果
   */
  delete: async (id) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/material-detail-sheet/${id}`);
      return response.data;
    } catch (error) {
      console.error('刪除明細表項目失敗:', error);
      throw error;
    }
  }
};

export default materialDetailSheetApi;
