const { exec } = require('child_process');
const os = require('os');

const ports = [5173, 3000]; // 前端和後端的端口

function killPort(port) {
  const platform = os.platform();
  
  if (platform === 'win32') {
    // Windows
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (stdout) {
        const pid = stdout.split('\n')[0].split(' ').filter(Boolean).pop();
        if (pid) {
          exec(`taskkill /F /PID ${pid}`);
        }
      }
    });
  } else {
    // macOS 和 Linux
    exec(`lsof -i :${port} | grep LISTEN | awk '{print $2}' | xargs kill -9`, (error) => {
      if (error) {
        console.log(`端口 ${port} 未被佔用`);
      } else {
        console.log(`已清除端口 ${port}`);
      }
    });
  }
}

ports.forEach(port => killPort(port)); 