const { exec } = require('child_process');
const path = require('path');

// 先執行端口清理
require('./kill-ports');

// 等待端口清理完成
setTimeout(() => {
  // 使用 concurrently 同時啟動前後端
  const frontendCmd = 'cd frontend && npm run dev';
  const backendCmd = 'cd backend && npm run dev';
  
  const devProcess = exec(`concurrently "${frontendCmd}" "${backendCmd}"`, {
    stdio: 'inherit'
  });

  devProcess.stdout.on('data', (data) => {
    console.log(data);
  });

  devProcess.stderr.on('data', (data) => {
    console.error(data);
  });
}, 1000); // 等待1秒確保端口清理完成 