const express = require('express');
const router = express.Router();
const bomController = require('../controllers/bomController');
const authController = require('../controllers/authController');

// 獲取所有BOM
router.get('/', authController.protect, bomController.getAllBoms);

// 搜尋BOM (必須放在/:id路由前面，否則會被誤認為id參數)
router.get('/search', authController.protect, bomController.searchBoms);

// 獲取單個BOM
router.get('/:id', authController.protect, bomController.getBomById);

// 創建新BOM
router.post('/', authController.protect, bomController.createBom);

// 更新BOM
router.put('/:id', authController.protect, bomController.updateBom);

// 完成修改BOM
router.put('/:id/complete', authController.protect, bomController.completeBom);

// 作廢BOM
router.put('/:id/invalidate', authController.protect, bomController.invalidateBom);

// 刪除BOM
router.delete('/:id', authController.protect, bomController.deleteBom);

// --- BOM 材料用量相關路由 ---

// 獲取指定 BOM 的材料用量列表
router.get('/:bomId/material-usage', authController.protect, bomController.getBomMaterialUsage);

// 為指定 BOM 添加材料用量記錄
router.post('/:bomId/material-usage', authController.protect, bomController.createBomMaterialUsage);

// 上傳 BOM 材料用量圖片
router.post('/:bomId/material-usage/upload-image', authController.protect, bomController.uploadBomMaterialImage);

// 刪除指定 BOM 的材料用量記錄
router.delete('/:bomId/material-usage/:usageId', authController.protect, bomController.deleteBomMaterialUsage);

module.exports = router;
