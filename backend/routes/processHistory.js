const express = require('express');
const router = express.Router();
const processHistoryController = require('../controllers/processHistoryController');

// 保存歷史記錄
router.post('/save', processHistoryController.saveHistory);

// 獲取指定 BOM 的歷史記錄列表
router.get('/list/:bomId', processHistoryController.getHistoryList);

// 獲取指定歷史記錄的詳細數據
router.get('/detail/:historyId', processHistoryController.getHistoryDetail);

// 清除指定 BOM 的所有歷史記錄
router.delete('/clear/:bomId', processHistoryController.clearHistory);

// 批量保存歷史記錄
router.post('/batch', processHistoryController.saveHistoryBatch);

module.exports = router; 