const express = require('express');
const router = express.Router();

// 定義公告相關的路由
// 創建公告
const db = require('../database/db');

router.post('/', async (req, res) => {
  try {
    // 獲取請求體中的公告數據
    const announcementData = req.body;

    // 確保 display 欄位的值預設為 1
    announcementData.display = 1;

    // 將公告數據保存到數據庫
    const [id] = await db('bulletinBoard').insert(announcementData);

    // 獲取新創建的公告
    const newAnnouncement = await db('bulletinBoard').where({ id }).first();

    // 返回創建成功的響應
    res.status(201).json({
      status: 'success',
      message: '公告創建成功',
      data: newAnnouncement
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 獲取所有公告
router.get('/', async (req, res) => {
  try {
    // 從數據庫獲取所有公告，包括隱藏和非隱藏的
    const announcements = await db('bulletinBoard')
      .select('*')
      .orderBy('createdAt', 'desc');

    res.status(200).json({
      status: 'success',
      message: '獲取公告列表成功',
      data: announcements
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 搜尋公告
router.get('/search', async (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      // 如果沒有搜尋關鍵字，返回所有公告
      const announcements = await db('bulletinBoard')
        .select('*')
        .orderBy('createdAt', 'desc');

      return res.status(200).json({
        status: 'success',
        message: '獲取公告列表成功',
        data: announcements
      });
    }

    // 從數據庫搜尋公告，包括隱藏和非隱藏的
    const announcements = await db('bulletinBoard')
      .select('*')
      .where('title', 'like', `%${query}%`)
      .orWhere('content', 'like', `%${query}%`)
      .orWhere('informer', 'like', `%${query}%`)
      .orWhere('type', 'like', `%${query}%`)
      .orderBy('createdAt', 'desc');

    res.status(200).json({
      status: 'success',
      message: '搜尋公告成功',
      data: announcements
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 獲取單個公告
router.get('/:id', async (req, res) => {
  try {
    const announcementId = req.params.id;
    // 從數據庫獲取指定ID的公告
    const announcement = await db('bulletinBoard').where({ id: announcementId }).first();

    if (!announcement) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該公告'
      });
    }

    res.status(200).json({
      status: 'success',
      message: '獲取公告詳情成功',
      data: announcement
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 更新公告
router.put('/:id', async (req, res) => {
  try {
    const announcementId = req.params.id;
    const announcementData = req.body;

    // 允許更新 display 欄位
    await db('bulletinBoard').where({ id: announcementId }).update(announcementData);

    // 獲取更新後的公告
    const updatedAnnouncement = await db('bulletinBoard').where({ id: announcementId }).first();

    res.status(200).json({
      status: 'success',
      message: '公告更新成功',
      data: updatedAnnouncement
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 刪除公告
router.delete('/:id', async (req, res) => {
  try {
    const announcementId = req.params.id;

    // 從數據庫中刪除指定ID的公告
    await db('bulletinBoard').where({ id: announcementId }).del();

    res.status(200).json({
      status: 'success',
      message: '公告刪除成功'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

// 搜尋公告
router.get('/search', async (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      // 如果沒有搜尋關鍵字，返回所有公告
      const announcements = await db('bulletinBoard')
        .select('*')
        .orderBy('createdAt', 'desc');

      return res.status(200).json({
        status: 'success',
        message: '獲取公告列表成功',
        data: announcements
      });
    }

    // 從數據庫搜尋公告，包括隱藏和非隱藏的
    const announcements = await db('bulletinBoard')
      .select('*')
      .where('title', 'like', `%${query}%`)
      .orWhere('content', 'like', `%${query}%`)
      .orWhere('informer', 'like', `%${query}%`)
      .orWhere('type', 'like', `%${query}%`)
      .orderBy('createdAt', 'desc');

    res.status(200).json({
      status: 'success',
      message: '搜尋公告成功',
      data: announcements
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: 'error',
      message: '伺服器內部錯誤'
    });
  }
});

module.exports = router;
