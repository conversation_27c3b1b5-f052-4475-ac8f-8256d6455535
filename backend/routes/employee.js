const express = require('express');
const router = express.Router();
const employeeController = require('../controllers/employeeController');
const authController = require('../controllers/authController');

// 獲取所有員工
router.get('/', authController.protect, employeeController.getAllEmployees);

// 獲取單個員工詳情
router.get('/:id', authController.protect, employeeController.getEmployeeById);

// 創建新員工
router.post('/', authController.protect, employeeController.createEmployee);

// 更新員工
router.put('/:id', authController.protect, employeeController.updateEmployee);

// 刪除員工
router.delete('/:id', authController.protect, employeeController.deleteEmployee);

module.exports = router; 