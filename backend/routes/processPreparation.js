const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController'); // 修正引入路徑
const ProcessPreparation = require('../models/ProcessPreparation');

// 獲取特定BOM的備料工序列表
router.get('/bom/:bomId', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    const { bomId } = req.params;
    const result = await ProcessPreparation.getByBomId(bomId);
    
    // 確保安全序列化，避免循環引用
    if (result.status === 'success' && Array.isArray(result.data)) {
      const safeResponse = {
        status: result.status,
        data: result.data // 已經在模型層安全處理過
      };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    } else {
      // 處理錯誤情況
      const safeResponse = {
        status: result.status || 'error',
        message: result.message || '獲取備料工序列表失敗',
        data: result.status === 'success' ? result.data : null
      };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    }
  } catch (error) {
    console.error('處理 GET /bom/:bomId 請求時出錯:', error); // 更新日誌
    const safeErrorResponse = { status: 'error', message: '獲取備料工序列表時發生錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 創建新的備料工序
router.post('/', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    // 獲取當前登錄用戶信息
    const userId = req.employee.id; // 修正：從 req.employee 獲取
    const userName = req.employee.name; // 修正：從 req.employee 獲取
    
    // 添加創建者信息
    const processData = {
      ...req.body,
      created_by: userId,
      created_by_name: userName
    };
    
    const result = await ProcessPreparation.create(processData);
    
    if (result.status === 'success' && result.data) {
      // 創建一個新的乾淨物件，只保留需要的屬性，避免循環引用
      const safeResponseData = {
        status: 'success',
        data: {
          id: result.data.id,
          bomId: result.data.bomId,
          processCode: result.data.processCode,
          majorProcess: result.data.majorProcess,
          minorProcess: result.data.minorProcess,
          sequenceNumber: result.data.sequenceNumber,
          pieceGroup: result.data.pieceGroup,
          pieceDetail: result.data.pieceDetail,
          pieceName: result.data.pieceName,
          material: result.data.material,
          quantity: result.data.quantity,
          tool: result.data.tool,
          consumable: result.data.consumable,
          processDescription: result.data.processDescription,
          standardTime: result.data.standardTime,
          actualTime: result.data.actualTime,
          order: result.data.order,
          created_by: result.data.created_by,
          created_by_name: result.data.created_by_name,
          createdAt: result.data.createdAt,
          updatedAt: result.data.updatedAt
        }
      };
      
      // 使用 res.send() 而非 res.json() 來避免自動序列化
      res.status(201).header('Content-Type', 'application/json').send(JSON.stringify(safeResponseData));
    } else {
      // 處理來自模型的錯誤
      const errorMessage = (result && typeof result.message === 'string') ? result.message : '創建備料工序失敗';
      const safeErrorResponse = { status: 'error', message: errorMessage };
      res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
    }
  } catch (error) {
    console.error('處理 POST /processPreparation 請求時捕獲到意外錯誤:', error);
    const safeErrorResponse = { status: 'error', message: '伺服器處理請求時發生意外錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 更新備料工序
router.put('/:id', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    const { id } = req.params;
    const result = await ProcessPreparation.update(id, req.body);
    
    // 創建安全的回應物件
    const safeResponse = {
      status: result.status || 'error',
      message: result.message || '更新備料工序完成'
    };
    
    res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
  } catch (error) {
    console.error('處理 PUT /:id 請求時出錯:', error);
    const safeErrorResponse = { status: 'error', message: '更新備料工序時發生錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 刪除備料工序
router.delete('/:id', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    const { id } = req.params;
    const result = await ProcessPreparation.delete(id);
    
    // 創建安全的回應物件
    const safeResponse = {
      status: result.status || 'error',
      message: result.message || '刪除備料工序完成'
    };
    
    res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
  } catch (error) {
    console.error('處理 DELETE /:id 請求時出錯:', error);
    const safeErrorResponse = { status: 'error', message: '刪除備料工序時發生錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 獲取工具列表
router.get('/tools', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    const result = await ProcessPreparation.getTools();
    
    // 確保結果不含循環引用
    if (result.status === 'success' && Array.isArray(result.data)) {
      const safeToolsList = result.data.map(tool => ({
        id: tool.id,
        processKey: tool.processKey,
        toolName: tool.toolName,
        sort_order: tool.sort_order,
        is_active: tool.is_active,
        createdAt: tool.createdAt,
        updatedAt: tool.updatedAt
      }));
      
      const safeResponse = { status: 'success', data: safeToolsList };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    } else {
      const safeResponse = {
        status: result.status || 'error',
        message: result.message || '獲取工具列表失敗',
        data: result.status === 'success' ? result.data : null
      };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    }
  } catch (error) {
    console.error('處理 GET /tools 請求時出錯:', error);
    const safeErrorResponse = { status: 'error', message: '獲取工具列表時發生錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 獲取耗材列表
router.get('/consumables', authController.protect, async (req, res) => { // 使用 authController.protect
  try {
    const result = await ProcessPreparation.getConsumables();
    
    // 確保結果不含循環引用
    if (result.status === 'success' && Array.isArray(result.data)) {
      const safeConsumablesList = result.data.map(consumable => ({
        id: consumable.id,
        processKey: consumable.processKey,
        consumableName: consumable.consumableName,
        sort_order: consumable.sort_order,
        is_active: consumable.is_active,
        createdAt: consumable.createdAt,
        updatedAt: consumable.updatedAt
      }));
      
      const safeResponse = { status: 'success', data: safeConsumablesList };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    } else {
      const safeResponse = {
        status: result.status || 'error',
        message: result.message || '獲取耗材列表失敗',
        data: result.status === 'success' ? result.data : null
      };
      res.header('Content-Type', 'application/json').send(JSON.stringify(safeResponse));
    }
  } catch (error) {
    console.error('處理 GET /consumables 請求時出錯:', error);
    const safeErrorResponse = { status: 'error', message: '獲取耗材列表時發生錯誤' };
    res.status(500).header('Content-Type', 'application/json').send(JSON.stringify(safeErrorResponse));
  }
});

// 合併工序
router.post('/merge', authController.protect, async (req, res) => {
  try {
    const { sourceProcessId, targetProcessId } = req.body;

    if (!sourceProcessId || !targetProcessId) {
      return res.status(400).json({
        status: 'error',
        message: '缺少必要參數：sourceProcessId 和 targetProcessId'
      });
    }

    const result = await ProcessPreparation.mergeProcess(sourceProcessId, targetProcessId);

    if (result.status === 'success') {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('合併工序失敗:', error);
    res.status(500).json({ status: 'error', message: '合併工序失敗' });
  }
});

// 取消工序合併
router.post('/unmerge', authController.protect, async (req, res) => {
  try {
    const { sourceProcessId } = req.body;

    if (!sourceProcessId) {
      return res.status(400).json({
        status: 'error',
        message: '缺少必要參數：sourceProcessId'
      });
    }

    const result = await ProcessPreparation.unmergeProcess(sourceProcessId);

    if (result.status === 'success') {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('取消工序合併失敗:', error);
    res.status(500).json({ status: 'error', message: '取消工序合併失敗' });
  }
});

// 獲取可合併的工序列表
router.get('/mergeable/:bomId', authController.protect, async (req, res) => {
  try {
    const { bomId } = req.params;
    const { excludeProcessId } = req.query;

    const result = await ProcessPreparation.getMergeableProcesses(bomId, excludeProcessId);

    if (result.status === 'success') {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('獲取可合併工序列表失敗:', error);
    res.status(500).json({ status: 'error', message: '獲取可合併工序列表失敗' });
  }
});

module.exports = router;
