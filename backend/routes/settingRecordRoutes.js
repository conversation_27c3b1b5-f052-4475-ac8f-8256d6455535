const express = require('express');
const router = express.Router();
const settingRecordController = require('../controllers/settingRecordController');
const authController = require('../controllers/authController');

// 需要認證的路由
router.use(authController.protect);

// 獲取所有設定記錄
router.get('/', settingRecordController.getAllSettingRecords);

// 獲取單個設定記錄
router.get('/:id', settingRecordController.getSettingRecordById);

// 創建新設定記錄
router.post('/', settingRecordController.createSettingRecord);

// 更新設定記錄
router.put('/:id', settingRecordController.updateSettingRecord);

// 刪除設定記錄
router.delete('/:id', settingRecordController.deleteSettingRecord);

module.exports = router;