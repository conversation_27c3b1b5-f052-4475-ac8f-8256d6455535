const express = require('express');
const router = express.Router();
const materialController = require('../controllers/materialController');

// 創建原物料交易記錄 (領用或入庫)
router.post('/materials/transaction', materialController.createMaterialTransaction);

// 獲取原物料交易記錄
router.get('/materials/transactions', materialController.getMaterialTransactions);

// 向後兼容 - 創建原物料領用記錄 (重定向到 transaction)
router.post('/materials/usage', materialController.createMaterialUsage);

// 向後兼容 - 獲取領用記錄 (重定向到 transactions，並增加領用類型過濾)
router.get('/materials/usage', materialController.getMaterialUsage);

// 創建原物料調撥記錄
router.post('/materials/transfer', materialController.createMaterialTransfer);

// 獲取指定部門的原物料
router.get('/materials/department/:department', materialController.getMaterialsByDepartment);

// 獲取所有原物料
router.get('/materials', materialController.getAllMaterials);

// 創建原物料
router.post('/materials', materialController.createMaterial);

// 獲取單個原物料
router.get('/materials/:id', materialController.getMaterialById);

// 更新原物料
router.put('/materials/:id', materialController.updateMaterial);

// 刪除原物料
router.delete('/materials/:id', materialController.deleteMaterial);

module.exports = router; 