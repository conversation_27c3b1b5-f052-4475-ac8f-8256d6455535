const express = require('express');
const router = express.Router();
const leatherMaterialController = require('../controllers/leatherMaterialController');

// 獲取皮料特殊資料列表
router.get('/leather-materials', leatherMaterialController.getLeatherMaterials);

// 獲取單個皮料特殊資料
router.get('/leather-materials/:id', leatherMaterialController.getLeatherMaterialById);

// 創建皮料特殊資料
router.post('/leather-materials', leatherMaterialController.createLeatherMaterial);

// 更新皮料特殊資料
router.put('/leather-materials/:id', leatherMaterialController.updateLeatherMaterial);

// 刪除皮料特殊資料
router.delete('/leather-materials/:id', leatherMaterialController.deleteLeatherMaterial);

module.exports = router; 