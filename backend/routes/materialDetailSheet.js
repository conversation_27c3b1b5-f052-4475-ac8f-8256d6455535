const express = require('express');
const router = express.Router();
const path = require('path');
const materialDetailSheetController = require('../controllers/materialDetailSheetController');
const authController = require('../controllers/authController');
const fileUpload = require('express-fileupload');

// 啟用文件上傳中間件
router.use(fileUpload({
  createParentPath: true,
  limits: { fileSize: 50 * 1024 * 1024 }, // 限制檔案大小為 50MB
  debug: true, // 啟用試錯模式
  useTempFiles: true, // 使用臨時檔案
  tempFileDir: path.join(__dirname, '../database/uploads/temp/'), // 臨時檔案目錄
  abortOnLimit: true, // 當檔案大小超過限制時中止上傳
  safeFileNames: true, // 安全的檔案名稱
  preserveExtension: true // 保留檔案副檔名
}));

// 獲取產品用料明細表項目
router.get('/', authController.protect, materialDetailSheetController.getItems);

// 創建產品用料明細表項目
router.post('/', authController.protect, materialDetailSheetController.createItem);

// 更新產品用料明細表項目
router.put('/:id', authController.protect, materialDetailSheetController.updateItem);

// 刪除所有產品用料明細表項目
router.delete('/delete-all', authController.protect, materialDetailSheetController.deleteAllItems);

// 刪除產品用料明細表項目
router.delete('/:id', authController.protect, materialDetailSheetController.deleteItem);

// 匯入Excel檔案
router.post('/import', authController.protect, materialDetailSheetController.importExcel);

module.exports = router;
