# 認證相關 API

## 登入
POST /api/auth/login
請求內容:
{
  "username": "用戶名",
  "password": "密碼"
}
成功響應:
{
  "status": "success",
  "token": "JWT令牌",
  "data": {
    "employee": {
      "id": 1,
      "name": "用戶姓名",
      "username": "用戶名",
      "role": "用戶角色",
      "email": "電子郵件",
      "phone": "手機號碼",
      "employeeId": "工號",
      "department": "部門",
      "position": "職稱",
      "notes": "備註"
    }
  }
}

# 人員管理 API

## 獲取所有員工
GET /api/employees
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "員工姓名",
      "username": "員工帳號",
      "role": "員工角色 (0: 管理員, 1: 組員)",
      "email": "電子郵件",
      "phone": "手機號碼",
      "employeeId": "工號",
      "department": "部門", // 例如: 0: 台北部, 1: 廠務部
      "position": "職稱",
      "level": "等級", // 可能的值: 'A', 'B', 'C', 'D', 'E'
      "notes": "備註"
    },
    // ... 其他員工
  ]
}

## 獲取單個員工詳情
GET /api/employees/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "員工姓名",
    "username": "員工帳號",
    "role": "員工角色 (0: 管理員, 1: 組員)",
    "email": "電子郵件",
    "phone": "手機號碼",
    "employeeId": "工號",
    "department": "部門",
    "position": "職稱",
    "level": "等級", // 可能的值: 'A', 'B', 'C', 'D', 'E'
    "notes": "備註"
  }
}

## 創建新員工
POST /api/employees
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "username": "新員工帳號", // 必填
  "password": "新員工密碼", // 必填
  "employeeId": "新員工工號", // 必填
  "name": "新員工姓名", // 必填
  "department": "部門", // 必填, 0: 台北部, 1: 廠務部
  "position": "職稱", // 可選
  "role": "角色 (0: 管理員, 1: 組員, 可選, 預設 1)",
  "email": "電子郵件", // 可選
  "phone": "手機號碼", // 可選
  "level": "等級", // 可選, 預設 'A', 可能的值: 'A', 'B', 'C', 'D', 'E'
  "notes": "備註" // 可選
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 新員工ID,
    // ... 新員工的其他資料 (不含密碼)
  }
}

## 更新員工資料
PUT /api/employees/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "username": "更新後的帳號",
  "password": "更新後的密碼", // 只有在需要修改密碼時提供
  "employeeId": "更新後的工號",
  "name": "更新後的姓名",
  "department": "更新後的部門", // 例如: 0: 台北部, 1: 廠務部
  "position": "更新後的職稱",
  "role": "更新後的角色 (0: 管理員, 1: 組員)",
  "email": "更新後的電子郵件",
  "phone": "更新後的手機號碼",
  "level": "更新後的等級", // 可能的值: 'A', 'B', 'C', 'D', 'E'
  "notes": "更新後的備註"
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 員工ID,
    // ... 更新後的員工資料 (不含密碼)
  }
}

## 刪除員工
DELETE /api/employees/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "員工已成功刪除"
}

## 獲取當前用戶信息
GET /api/auth/me
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": {
    "employee": {
      "id": 1,
      "name": "用戶姓名",
      "username": "用戶名",
      "role": "用戶角色",
      "email": "電子郵件",
      "phone": "手機號碼",
      "employeeId": "工號",
      "department": "部門",
      "position": "職稱",
      "notes": "備註"
    }
  }
}

# BOM管理 API

## 獲取所有BOM
GET /api/bom
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "唯一識別ID",
      "productCode": "材料編號",
      "cName": "中文品名",
      "eName": "英文品名",
      "category": "產品類別",
      "status": "審核狀態", // 可能的值: 未審核, 審核中, 已審核, 完成確認
      "confirmationStatus": "部門確認狀態", // 格式: 逗號分隔的部門編號, 例如: "0,1" 表示台北部和廠務部都已確認
      "productStatus": "產品狀態", // 例如: "設計階段", "PPS"
      "pics": "圖片路徑",
      // ... 其他欄位
    },
    // ... 其他BOM
  ]
}

## 獲取單個BOM詳情
GET /api/bom/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": {
    "id": "唯一識別ID",
    "productCode": "材料編號",
    "category": "產品類別",
    "trademark": "產品商標",
    "unit": "單位",
    "version": "版本",
    "cName": "中文品名",
    "eName": "英文品名",
    "cColor": "中文顏色",
    "eColor": "英文顏色",
    "cType": "中文大類",
    "eType": "英文大類",
    "format": "規格",
    "barcode": "條碼編號",
    "pics": "圖片路徑或base64",
    "hardwareColor": "五金顏色",
    "materialType": "材質類型",
    "height": "長度cm",
    "width": "面寬cm",
    "deep": "深度cm",
    "weight": "淨重g",
    "cm1": "提把垂高cm",
    "cm2": "背帶最長垂高cm",
    "cm3": "背帶最短垂高cm",
    "format1": "防塵袋規格",
    "format2": "出貨箱規格",
    "productStatus": "產品狀態",
    "status": "審核狀態", // 可能的值: 未審核, 審核中, 已審核, 完成確認
    "confirmationStatus": "部門確認狀態", // 格式: 逗號分隔的部門編號, 例如: "0,1" 表示台北部和廠務部都已確認
    "aboutBom": "備註",
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 創建新BOM
POST /api/bom
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "productCode": "材料編號",
  "cName": "中文品名",
  "category": "產品類別",
  "status": "審核狀態", // 可選，預設為 0 (未審核)
  // ... 其他欄位
}
成功響應:
{
  "status": "success",
  "data": {
    // 新創建或更新的BOM完整資訊
  }
}
備註：如果 productCode 已經存在，則更新現有資料。

## 更新BOM
PUT /api/bom/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "productCode": "更新後的材料編號",
  "cName": "更新後的中文品名",
  // ... 其他欄位
}
成功響應:
{
  "status": "success",
  "data": {
    // 更新後的BOM完整資訊
  }
}

## 刪除BOM
DELETE /api/bom/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "BOM已成功刪除"
}

## 搜尋BOM
GET /api/bom/search?query=搜尋關鍵字
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    // 符合條件的BOM列表
  ]
}

# 公告管理 API

## 獲取所有公告
GET /api/announcement
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "type": "公告類型",
      "announcementUnit": "開採,行銷,會計", // 逗號分隔的公告單位
      "informer": "公告人",
      "title": "公告主旨",
      "content": "公告內容",
      "display": 1,
      "createdAt": "2023-04-01T08:00:00Z"
    },
    // ... 其他公告
  ]
}

## 創建新公告
POST /api/announcement
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "type": "公告類型",
  "announcementUnit": "開採,行銷,會計", // 逗號分隔的公告單位
  "informer": "公告人",
  "title": "公告主旨",
  "content": "公告內容",
  "display": 1
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 1,
    "type": "公告類型",
    "announcementUnit": "開採,行銷,會計",
    "informer": "公告人",
    "title": "公告主旨",
    "content": "公告內容",
    "display": 1,
    "createdAt": "2023-04-01T08:00:00.000Z"
  }
}

## 更新公告
PUT /api/announcement/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "type": "公告類型",
  "announcementUnit": "開採,行銷,會計", // 逗號分隔的公告單位
  "informer": "公告人",
  "title": "公告主旨",
  "content": "公告內容",
  "display": 1
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 1,
    "type": "公告類型",
    "announcementUnit": "開採,行銷,會計",
    "informer": "公告人",
    "title": "公告主旨",
    "content": "公告內容",
    "display": 1,
    "createdAt": "2023-04-01T08:00:00.000Z",
    "updatedAt": "2023-04-02T10:30:00.000Z"
  }
}

## 刪除公告
DELETE /api/announcement/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "公告已刪除"
}

## 搜尋公告
GET /api/announcement/search?query={查詢關鍵字}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
備註：資料庫表格的建立方式已變更，不再使用 Knex.js 遷移。
  "status": "success",
  "data": [
    {
      "id": 1,
      "type": "公告類型",
      "announcementUnit": "開採,行銷,會計",
      "informer": "公告人",
      "title": "包含搜尋關鍵字的主旨",
      "content": "公告內容",
      "display": 1,
      "createdAt": "2023-04-01T08:00:00.000Z"
    },
    // ... 其他符合搜尋條件的公告
  ]
}

# 設定記錄管理 API (SettingRecord)

## 獲取所有設定記錄
GET /api/settingRecord
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
- type: 設定類型 (可選)，例如 "color"、"format" 等
- sortBy: 排序欄位 (可選)，例如 "name"、"value" 等
- sortOrder: 排序方式 (可選)，例如 "asc" (升序) 或 "desc" (降序)
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "type": "設定類型",
      "name": "設定名稱",
      "value": "設定值",
      "description": "描述",
      "sort_order": 0,
      "is_active": true,
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他設定記錄
  ]
}

## 獲取單個設定記錄
GET /api/settingRecord/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": {
    "id": 1,
    "type": "設定類型",
    "name": "設定名稱",
    "value": "設定值",
    "description": "描述",
    "sort_order": 0,
    "is_active": true,
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 創建新設定記錄
POST /api/settingRecord
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "type": "設定類型", // 必填，例如: "color", "format", 等
  "name": "設定名稱", // 必填
  "value": "設定值", // 必填
  "description": "描述", // 可選
  "sort_order": 0, // 可選，排序順序
  "is_active": true // 可選，是否啟用
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 新設定記錄ID,
    "type": "設定類型",
    "name": "設定名稱",
    "value": "設定值",
    "description": "描述",
    "sort_order": 0,
    "is_active": true,
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 更新設定記錄
PUT /api/settingRecord/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "type": "更新後的設定類型",
  "name": "更新後的設定名稱",
  "value": "更新後的設定值",
  "description": "更新後的描述",
  "sort_order": 1,
  "is_active": true
}
成功響應:
{
  "status": "success",
  "data": {
    "id": 設定記錄ID,
    "type": "更新後的設定類型",
    "name": "更新後的設定名稱",
    "value": "更新後的設定值",
    "description": "更新後的描述",
    "sort_order": 1,
    "is_active": true,
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 刪除設定記錄
DELETE /api/settingRecord/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "設定記錄已成功刪除"
}

# 原物料庫存 API

## 獲取所有原物料
GET /api/inventory/materials
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - search: 搜索關鍵字（材料編號或名稱）
  - category: 分類篩選
成功響應:
[
  {
    "id": "唯一識別ID",
    "code": "材料編號",
    "name": "名稱",
    "type": "類型",
    "category": "分類",
    "specification": "規格",
    "unit": "單位", // 新增
    "color": "顏色",
    "storage_location": "倉別",
    "stock_quantity": "庫存數量",
    "available_quantity": "可用數量",
    "description": "描述",
    "is_active": true,
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  },
  // ... 其他原物料
]

## 獲取單個原物料詳情
GET /api/inventory/materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "id": "唯一識別ID",
  "code": "材料編號",
  "name": "名稱",
  "type": "類型",
  "category": "分類",
  "specification": "規格",
  "unit": "單位", // 新增
  "color": "顏色",
  "storage_location": "倉別",
  "stock_quantity": "庫存數量",
  "available_quantity": "可用數量",
  "description": "描述",
  "is_active": true,
  "createdAt": "更新時間",
  "updatedAt": "更新時間"
}

## 創建新原物料
POST /api/inventory/materials
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "code": "材料編號", // 可選
  "name": "名稱", // 可選
  "type": "類型", // 可選
  "category": "分類", // 可選
  "specification": "規格", // 可選
  "unit": "單位", // 可選
  "color": "顏色", // 可選
  "storage_location": "倉別", // 必填
  "stock_quantity": 庫存數量 (小數點後兩位), // 必填
  "available_quantity": 可用數量 (小數點後兩位), // 可選，預設等於庫存數量
  "description": "描述" // 可選
}
備註:
- 當存在相同的材料編號、材料名稱和倉別時，系統會自動累加數量
- 自動以英文字母由小到大排序，最新生成的資料顯示在最上方

成功響應:
{
  "id": "唯一識別ID",
  "code": "材料編號",
  "name": "名稱",
  "type": "類型",
  "category": "分類",
  "specification": "規格", // 補上
  "unit": "單位", // 新增
  "color": "顏色", // 補上
  "storage_location": "倉別",
  "stock_quantity": "庫存數量",
  "available_quantity": "可用數量",
  "description": "描述",
  "is_active": true,
  "createdAt": "創建時間",
  "updatedAt": "創建時間"
}

## 更新原物料
PUT /api/inventory/materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "code": "更新後的材料編號",
  "name": "更新後的名稱",
  "type": "更新後的類型",
  "category": "更新後的分類",
  "specification": "更新後的規格",
  "unit": "更新後的單位", // 新增
  "color": "更新後的顏色",
  "storage_location": "更新後的倉別",
  "stock_quantity": 更新後的庫存數量 (小數點後兩位),
  "available_quantity": 更新後的可用數量 (小數點後兩位),
  "description": "更新後的描述"
}
成功響應:
{
  "id": "原物料ID",
  // ... 更新後的原物料資料
}

## 刪除原物料
DELETE /api/inventory/materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "message": "原物料已成功刪除"
}

## 原物料領用
POST /api/inventory/materials/usage
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "material_id": "原物料ID", // 必填
  "quantity": 領用數量, // 必填，必須為正數
  "requester": "申請人", // 必填
  "department": "領用部門", // 必填
  "purpose": "用途", // 可選
  "notes": "備註" // 可選
}
成功響應:
{
  "id": "領用記錄ID",
  "material_id": "原物料ID",
  "material_code": "原物料代號",
  "material_name": "原物料名稱",
  "quantity": "領用數量",
  "requester": "申請人",
  "department": "領用部門",
  "purpose": "用途",
  "notes": "備註",
  "created_by": "創建人ID",
  "created_by_name": "創建人姓名",
  "createdAt": "創建時間"
}

## 獲取領用記錄
GET /api/inventory/materials/usage
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - material_id: 原物料ID（可選）
  - start_date: 開始日期（可選，格式：YYYY-MM-DD）
  - end_date: 結束日期（可選，格式：YYYY-MM-DD）
  - department: 領用部門（可選）
成功響應:
[
  {
    "id": "領用記錄ID",
    "material_id": "原物料ID",
    "material_code": "原物料代號",
    "material_name": "原物料名稱",
    "quantity": "領用數量",
    "requester": "申請人",
    "department": "領用部門",
    "purpose": "用途",
    "notes": "備註",
    "created_by": "創建人ID",
    "created_by_name": "創建人姓名",
    "createdAt": "創建時間"
  },
  // ... 其他領用記錄
]

## 創建原物料交易記錄
POST /api/inventory/materials/transaction
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "material_id": "原物料ID", // 必填
  "quantity": 交易數量, // 必填，必須為正數
  "transaction_type": "入庫" 或 "領用", // 必填
  "requester": "申請人", // 如果是領用，則必填
  "department": "領用部門", // 如果是領用，則必填
  "purpose": "用途", // 可選
  "notes": "備註" // 可選
}
成功響應:
{
  "id": "交易記錄ID",
  "material_id": "原物料ID",
  "material_code": "原物料代號",
  "material_name": "原物料名稱",
  "quantity": "交易數量",
  "transaction_type": "交易類型",
  "requester": "申請人",
  "department": "領用部門",
  "purpose": "用途",
  "storage_location": "倉別",
  "notes": "備註",
  "created_by": "創建人ID",
  "created_by_name": "創建人姓名",
  "createdAt": "創建時間"
}

## 獲取原物料交易記錄
GET /api/inventory/materials/transactions
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - material_id: 原物料ID (可選)
  - transaction_type: 交易類型 (可選, 例如: 入庫, 領用, 調出, 調入)
  - department: 部門 (可選)
  - start_date: 開始日期 (可選, YYYY-MM-DD)
  - end_date: 結束日期 (可選, YYYY-MM-DD)
成功響應:
[
  {
    "id": "交易記錄ID",
    "material_id": "原物料ID",
    "material_code": "材料編號",
    "material_name": "材料名稱",
    "quantity": 數量,
    "transaction_type": "交易類型", // 入庫, 領用, 調出, 調入
    "requester": "申請人",
    "department": "部門",
    "purpose": "用途",
    "storage_location": "倉別",
    "notes": "備註",
    "transfer_id": "調撥記錄ID", // 只有調撥類型的交易有此欄位
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  },
  //... 其他交易記錄
]

## 獲取指定部門的原物料
GET /api/inventory/materials/department/:department
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - search: 搜索關鍵字（可選）
成功響應:
[
  {
    "id": "原物料ID",
    "code": "材料編號",
    "name": "名稱",
    "type": "類型",
    "category": "分類",
    "specification": "規格",
    "unit": "單位", // 新增
    "color": "顏色",
    "storage_location": "倉別",
    "stock_quantity": "庫存數量",
    "available_quantity": "可用數量",
    "description": "描述",
    "is_active": true/false,
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  },
  // ... 其他原物料
]

# 備料工序管理 API

## 獲取特定BOM的備料工序列表
GET /api/process-preparation/bom/:bomId
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "工序ID",
      "bomId": "BOM ID",
      "processCode": "工序序號",
      "majorProcess": "大工序",
      "minorProcess": "小工序",
      "sequenceNumber": "產生後序號",
      "pieceGroup": "部位組織",
      "pieceDetail": "分片組織",
      "pieceName": "分片名稱",
      "material": "材料",
      "quantity": "組成數量",
      "tool": "工具",
      "consumable": "耗材",
      "processDescription": "加工說明",
      "standardTime": "標準工時",
      "actualTime": "實際工時",
      "order": "順序",
      "created_by": "創建人ID",
      "created_by_name": "創建人姓名",
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他備料工序
  ]
}

## 創建新的備料工序
POST /api/process-preparation
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "bomId": "BOM ID",
  "processCode": "工序序號",
  "majorProcess": "大工序",
  "minorProcess": "小工序",
  "pieceGroup": "部位組織",
  "pieceDetail": "分片組織",
  "pieceName": "分片名稱",
  "material": "材料 (可選)",
  "quantity": "組成數量 (可選, 預設 1)",
  "tool": "工具 (可選)",
  "consumable": "耗材 (可選)",
  "processDescription": "加工說明 (可選)",
  "standardTime": "標準工時 (可選, 預設 0)",
  "actualTime": "實際工時 (可選, 預設 0)",
  "order": "順序"
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "新創建的工序ID",
    // ... 其他工序數據
  }
}

## 更新備料工序
PUT /api/process-preparation/:id
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "processCode": "更新後的工序序號",
  "majorProcess": "更新後的大工序",
  "minorProcess": "更新後的小工序",
  "sequenceNumber": "更新後的產生後序號",
  "pieceGroup": "更新後的部位組織",
  "pieceDetail": "更新後的分片組織",
  "pieceName": "更新後的分片名稱",
  "material": "更新後的材料",
  "quantity": "更新後的組成數量",
  "tool": "更新後的工具",
  "consumable": "更新後的耗材",
  "processDescription": "更新後的加工說明",
  "standardTime": "更新後的標準工時",
  "actualTime": "更新後的實際工時",
  "order": "更新後的順序"
}
成功響應:
{
  "status": "success",
  "message": "備料工序更新成功"
}

## 刪除備料工序
DELETE /api/process-preparation/:id
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "備料工序刪除成功"
}

## 獲取工具列表
GET /api/process-preparation/tools
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "工具ID",
      "processKey": "工序鍵值",
      "toolName": "工具名稱",
      "sort_order": "排序",
      "is_active": true/false,
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他工具
  ]
}

## 獲取耗材列表
GET /api/process-preparation/consumables
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "耗材ID",
      "processKey": "工序鍵值",
      "consumableName": "耗材名稱",
      "sort_order": "排序",
      "is_active": true/false,
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他耗材
  ]
}

# BOM 材料用量管理 API

## 獲取指定 BOM 的材料用量列表
GET /api/bom/:bomId/material-usage
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "材料用量記錄ID",
      "bom_id": "關聯的 BOM ID",
      "material_type": "材料類型",
      "material_name": "材料名稱",
      "quantity": 數量,
      "unit": "單位",
      "remark": "備註",
      "inventory_id": "關聯的庫存原物料 ID (可選)",
      "code": "庫存原物料代號 (可選)",
      "item_number": "項次 (可選)",
      "material_category": "材料類別 (主料/副料/其他)",
      "usage_position": "使用部位 (可選)",
      "image_url": "圖片URL (可選)",
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他材料用量記錄 (按創建時間降冪排序)
  ]
}

## 為指定 BOM 添加材料用量記錄
POST /api/bom/:bomId/material-usage
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "materialType": "材料類型", // 必填
  "materialName": "材料名稱", // 必填
  "quantity": 數量, // 必填
  "unit": "單位", // 必填
  "remark": "備註", // 可選
  "inventoryId": "庫存原物料 ID", // 可選
  "code": "庫存原物料代號", // 可選
  "item_number": "項次", // 可選
  "material_category": "材料類別 (主料/副料/其他)", // 可選，預設為主料
  "usage_position": "使用部位" // 可選
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "新創建的材料用量記錄ID",
    "bom_id": "關聯的 BOM ID",
    "material_type": "材料類型",
    "material_name": "材料名稱",
    "quantity": 數量,
    "unit": "單位",
    "remark": "備註",
    "inventory_id": "關聯的庫存原物料 ID",
    "code": "庫存原物料代號",
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 上傳 BOM 材料用量圖片
POST /api/bom/:bomId/material-usage/upload-image
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "image": "base64編碼的圖片數據" // 必填
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "圖片記錄ID",
    "image_url": "圖片路徑"
  }
}

## 上傳 BOM 材料用量圖片
POST /api/bom/:bomId/material-usage/upload-image
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "image": "base64編碼的圖片數據" // 必填
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "圖片記錄ID",
    "image_url": "圖片路徑"
  }
}

## 刪除指定 BOM 的材料用量記錄
DELETE /api/bom/:bomId/material-usage/:usageId
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "材料用量記錄刪除成功"
}

# 產品用料明細表 API

## 獲取產品用料明細表項目
GET /api/material-detail-sheet
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - bomId: BOM ID (必填)
  - materialId: 材料用量 ID (必填)
  - sheetType: 明細表類型 (必填, "主料" 或 "副料")
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "明細表項目ID",
      "bom_id": "關聯的 BOM ID",
      "material_id": "關聯的材料用量 ID",
      "sheet_type": "明細表類型 (主料/副料)",
      "number": "號碼",
      "position_name": "部位名稱",
      "material_name": "材料名稱",
      "material_code": "料號",
      "width": 寬度(cm),
      "height": 高度(cm),
      "area": 面積(cm²),
      "quantity": 組成數量,
      "total_area": 面積總計(cm²),
      "is_split": 是否分片 (true/false),
      "grand_total": 總合,
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    },
    // ... 其他明細表項目
  ]
}

## 創建產品用料明細表項目
POST /api/material-detail-sheet
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "bom_id": "BOM ID", // 必填
  "material_id": "材料用量 ID", // 必填
  "sheet_type": "明細表類型 (主料/副料)", // 必填
  "number": "號碼", // 可選
  "position_name": "部位名稱", // 可選
  "material_name": "材料名稱", // 可選
  "material_code": "料號", // 可選
  "width": 寬度(cm), // 可選
  "height": 高度(cm), // 可選
  "quantity": 組成數量, // 可選, 預設為 1
  "is_split": 是否分片 (true/false) // 可選, 預設為 false
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "新創建的明細表項目ID",
    // ... 其他明細表項目數據
  }
}

## 更新產品用料明細表項目
PUT /api/material-detail-sheet/:id
請求頭: Authorization: Bearer {JWT令牌}
請求內容 (只需包含要更新的欄位):
{
  "number": "更新後的號碼",
  "position_name": "更新後的部位名稱",
  "material_name": "更新後的材料名稱",
  "material_code": "更新後的料號",
  "width": 更新後的寬度(cm),
  "height": 更新後的高度(cm),
  "quantity": 更新後的組成數量,
  "is_split": 更新後的是否分片 (true/false)
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "明細表項目ID",
    // ... 更新後的明細表項目數據
  }
}

## 刪除產品用料明細表項目
DELETE /api/material-detail-sheet/:id
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "產品用料明細表項目刪除成功"
}

## 匯入Excel檔案
POST /api/material-detail-sheet/import
請求頭: Authorization: Bearer {JWT令牌}
請求內容: multipart/form-data
  - file: Excel檔案 (必填)
  - bomId: BOM ID (必填)
  - materialId: 材料用量 ID (必填)
  - sheetType: 明細表類型 (必填, "主料" 或 "副料")
成功響應:
{
  "status": "success",
  "message": "成功匯入 X 筆資料",
  "data": {
    "count": X
  }
}

# 皮料特殊資料 API

## 獲取皮料特殊資料列表
GET /api/inventory/leather-materials
請求頭: Authorization: Bearer {JWT令牌}
可選查詢參數:
  material_id: 原物料ID
  warehouse: 存放倉庫
  leather_code: 皮料編碼
成功響應:
{
  "status": "success",
  "data": [
    {
      "id": "唯一識別ID",
      "material_id": "關聯的原物料ID",
      "material_code": "原物料編號",
      "material_name": "原物料名稱",
      "warehouse": "存放倉庫",
      "area": 1.25, // 皮料面積(m2)
      "quantity": 1, // 數量
      "leather_code": "皮料編碼", // 格式: 材料編號的後兩碼+日期(YYMMDD)+產生順序(3位數)
      "transaction_id": "關聯的交易記錄ID",
      "notes": "備註",
      "created_by": "創建人ID",
      "created_by_name": "創建人姓名",
      "createdAt": "創建時間",
      "updatedAt": "更新時間"
    }
  ]
}

## 獲取單個皮料特殊資料詳情
GET /api/inventory/leather-materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "data": {
    "id": "唯一識別ID",
    "material_id": "關聯的原物料ID",
    "material_code": "原物料編號",
    "material_name": "原物料名稱",
    "warehouse": "存放倉庫",
    "area": 1.25, // 皮料面積(m2)
    "quantity": 1, // 數量
    "leather_code": "皮料編碼", // 格式: L-倉庫代碼-年月日-序號(3位數)
    "transaction_id": "關聯的交易記錄ID",
    "notes": "備註",
    "created_by": "創建人ID",
    "created_by_name": "創建人姓名",
    "createdAt": "創建時間",
    "updatedAt": "更新時間"
  }
}

## 創建皮料特殊資料
POST /api/inventory/leather-materials
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "material_id": "關聯的原物料ID", // 必填
  "warehouse": "存放倉庫", // 必填
  "area": 1.25, // 皮料面積(m2)，必填
  "quantity": 1, // 數量，默認為1
  "transaction_id": "關聯的交易記錄ID", // 可選
  "notes": "備註" // 可選
}
成功響應:
{
  "status": "success",
  "data": {
    "id": "新建的唯一識別ID",
    "leather_code": "自動生成的皮料編碼", // 格式: L-倉庫代碼-年月日-序號(3位數)
    // ... 其他皮料特殊資料
  }
}

## 更新皮料特殊資料
PUT /api/inventory/leather-materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "warehouse": "存放倉庫", // 可選
  "area": 1.25, // 可選
  "quantity": 1, // 可選
  "notes": "備註" // 可選
}
成功響應:
{
  "status": "success",
  "data": {
    // 更新後的皮料特殊資料
  }
}

## 刪除皮料特殊資料
DELETE /api/inventory/leather-materials/{id}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "status": "success",
  "message": "皮料特殊資料已成功刪除"
}

## 原物料調撥
POST /api/inventory/materials/transfer
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "material_id": "原物料ID", // 必填
  "quantity": 調撥數量, // 必填，必須為正數
  "from_warehouse": "調出倉庫", // 必填，例如: "台北部", "廠務部A"
  "to_warehouse": "調入倉庫", // 必填，例如: "台北部", "廠務部A"
  "requester": "申請人", // 必填
  "notes": "備註" // 可選
}
成功響應:
{
  "id": "調撥記錄ID",
  "material_id": "原物料ID",
  "material_code": "材料編號",
  "material_name": "材料名稱",
  "quantity": 調撥數量,
  "from_warehouse": "調出倉庫",
  "to_warehouse": "調入倉庫",
  "requester": "申請人",
  "notes": "備註",
  "createdAt": "創建時間",
  "updatedAt": "更新時間"
}

# 工序歷史記錄管理 API

## 保存歷史記錄
POST /api/process-history/save
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "bomId": "BOM ID", // 必填
  "description": "操作描述", // 必填，例如: "初始狀態", "保存工序", "刪除工序"
  "stateData": { // 必填，完整的編輯狀態數據
    "processForm": "工序表單數據",
    "savedProcesses": "已保存的工序列表",
    "selectedProcessId": "選中的工序ID",
    "selectedPosition": "選中的位置",
    "processCounters": "工序計數器",
    "materialCollapsed": "材料區域是否折疊",
    "filteredMaterial": "過濾的材料列表",
    "isMultiSelectMode": "是否為多選模式",
    "multiSelectedMaterials": "多選的材料",
    "multiSelectedProcesses": "多選的工序"
  }
}
成功響應:
{
  "success": true,
  "message": "歷史記錄保存成功",
  "data": {
    "id": "歷史記錄ID",
    "created_at": "創建時間"
  }
}

## 獲取歷史記錄列表
GET /api/process-history/list/{bomId}
請求頭: Authorization: Bearer {JWT令牌}
查詢參數:
  - limit: 限制返回數量 (可選，預設 20)
成功響應:
{
  "success": true,
  "data": [
    {
      "id": "歷史記錄ID",
      "description": "操作描述",
      "timestamp": "創建時間",
      "userName": "操作用戶名稱",
      "state": "完整的狀態數據"
    },
    // ... 其他歷史記錄 (按時間降序排列)
  ]
}

## 獲取歷史記錄詳情
GET /api/process-history/detail/{historyId}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "success": true,
  "data": {
    "id": "歷史記錄ID",
    "description": "操作描述",
    "timestamp": "創建時間",
    "userName": "操作用戶名稱",
    "state": "完整的狀態數據"
  }
}

## 清除歷史記錄
DELETE /api/process-history/clear/{bomId}
請求頭: Authorization: Bearer {JWT令牌}
成功響應:
{
  "success": true,
  "message": "歷史記錄已清除"
}

## 批量保存歷史記錄
POST /api/process-history/batch
請求頭: Authorization: Bearer {JWT令牌}
請求內容:
{
  "bomId": "BOM ID", // 必填
  "historyList": [ // 必填，歷史記錄列表
    {
      "description": "操作描述",
      "timestamp": "時間戳",
      "state": "狀態數據"
    },
    // ... 其他歷史記錄
  ]
}
成功響應:
{
  "success": true,
  "message": "歷史記錄批量保存成功",
  "data": {
    "count": "保存的記錄數量"
  }
}
