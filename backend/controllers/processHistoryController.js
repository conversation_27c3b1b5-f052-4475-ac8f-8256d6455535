const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');

// 保存歷史記錄
const saveHistory = async (req, res) => {
  try {
    const { bomId, description, stateData } = req.body;

    // 從 token 中獲取用戶信息 (如果有的話)
    const userId = req.user?.id || null;
    const userName = req.user?.name || '匿名用戶';

    const historyRecord = {
      id: uuidv4(),
      bom_id: bomId,
      user_id: userId,
      user_name: userName,
      description: description || '編輯狀態',
      state_data: JSON.stringify(stateData),
      created_at: new Date()
    };

    await db('process_history').insert(historyRecord);

    // 限制每個 BOM 的歷史記錄數量，保留最新的 20 條
    const allRecords = await db('process_history')
      .where('bom_id', bomId)
      .orderBy('created_at', 'desc');

    if (allRecords.length > 20) {
      const recordsToDelete = allRecords.slice(20);
      const idsToDelete = recordsToDelete.map(record => record.id);
      await db('process_history').whereIn('id', idsToDelete).del();
    }

    res.json({
      success: true,
      message: '歷史記錄保存成功',
      data: {
        id: historyRecord.id,
        created_at: historyRecord.created_at
      }
    });
  } catch (error) {
    console.error('保存歷史記錄錯誤:', error);
    res.status(500).json({
      success: false,
      message: '保存歷史記錄失敗',
      error: error.message
    });
  }
};

// 獲取指定 BOM 的歷史記錄列表
const getHistoryList = async (req, res) => {
  try {
    const { bomId } = req.params;
    const { limit = 10 } = req.query;

    const historyRecords = await db('process_history')
      .where('bom_id', bomId)
      .orderBy('created_at', 'desc')
      .limit(parseInt(limit));

    const formattedRecords = historyRecords.map(record => ({
      id: record.id,
      description: record.description,
      timestamp: record.created_at,
      userName: record.user_name,
      state: JSON.parse(record.state_data)
    }));

    res.json({
      success: true,
      data: formattedRecords
    });
  } catch (error) {
    console.error('獲取歷史記錄錯誤:', error);
    res.status(500).json({
      success: false,
      message: '獲取歷史記錄失敗',
      error: error.message
    });
  }
};

// 獲取指定歷史記錄的詳細數據
const getHistoryDetail = async (req, res) => {
  try {
    const { historyId } = req.params;

    const historyRecord = await db('process_history')
      .where('id', historyId)
      .first();

    if (!historyRecord) {
      return res.status(404).json({
        success: false,
        message: '歷史記錄不存在'
      });
    }

    res.json({
      success: true,
      data: {
        id: historyRecord.id,
        description: historyRecord.description,
        timestamp: historyRecord.created_at,
        userName: historyRecord.user_name,
        state: JSON.parse(historyRecord.state_data)
      }
    });
  } catch (error) {
    console.error('獲取歷史記錄詳情錯誤:', error);
    res.status(500).json({
      success: false,
      message: '獲取歷史記錄詳情失敗',
      error: error.message
    });
  }
};

// 清除指定 BOM 的所有歷史記錄
const clearHistory = async (req, res) => {
  try {
    const { bomId } = req.params;

    await db('process_history')
      .where('bom_id', bomId)
      .del();

    res.json({
      success: true,
      message: '歷史記錄已清除'
    });
  } catch (error) {
    console.error('清除歷史記錄錯誤:', error);
    res.status(500).json({
      success: false,
      message: '清除歷史記錄失敗',
      error: error.message
    });
  }
};

// 批量保存歷史記錄 (用於初始化或批量同步)
const saveHistoryBatch = async (req, res) => {
  try {
    const { bomId, historyList } = req.body;

    // 從 token 中獲取用戶信息
    const userId = req.user?.id || null;
    const userName = req.user?.name || '匿名用戶';

    // 先清除現有歷史記錄
    await db('process_history').where('bom_id', bomId).del();

    // 批量插入新的歷史記錄
    const historyRecords = historyList.map(item => ({
      id: uuidv4(),
      bom_id: bomId,
      user_id: userId,
      user_name: userName,
      description: item.description || '編輯狀態',
      state_data: JSON.stringify(item.state),
      created_at: new Date(item.timestamp)
    }));

    if (historyRecords.length > 0) {
      await db('process_history').insert(historyRecords);
    }

    res.json({
      success: true,
      message: '歷史記錄批量保存成功',
      data: {
        count: historyRecords.length
      }
    });
  } catch (error) {
    console.error('批量保存歷史記錄錯誤:', error);
    res.status(500).json({
      success: false,
      message: '批量保存歷史記錄失敗',
      error: error.message
    });
  }
};

module.exports = {
  saveHistory,
  getHistoryList,
  getHistoryDetail,
  clearHistory,
  saveHistoryBatch
}; 