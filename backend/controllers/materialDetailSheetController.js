const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');

// 獲取產品用料明細表項目
exports.getItems = async (req, res) => {
  try {
    const { bomId, sheetType } = req.query;

    // 驗證必要的查詢參數
    if (!bomId || !sheetType) {
      return res.status(400).json({
        status: 'fail',
        message: 'bomId 和 sheetType 為必填查詢參數'
      });
    }

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: bomId }).first();
    if (!bomExists) {
      console.log('找不到該 BOM:', bomId);
      // 返回空數組而不是錯誤，以便前端可以正常顯示
      return res.status(200).json({
        status: 'success',
        data: []
      });
    }

    // 檢查表是否存在
    const tableExists = await db.schema.hasTable('material_detail_sheets');
    if (!tableExists) {
      console.log('material_detail_sheets 表不存在');
      // 返回空數組
      return res.status(200).json({
        status: 'success',
        data: []
      });
    }

    // 查詢產品用料明細表項目，不再依賴 material_id
    const detailItems = await db('material_detail_sheets')
      .where({
        bom_id: bomId,
        sheet_type: sheetType
      })
      .orderBy('createdAt', 'asc'); // 按創建時間升序排序

    res.status(200).json({
      status: 'success',
      data: detailItems
    });
  } catch (error) {
    console.error('獲取產品用料明細表項目失敗:', error);
    // 返回空數組而不是錯誤，以便前端可以正常顯示
    res.status(200).json({
      status: 'success',
      data: []
    });
  }
};

// 創建產品用料明細表項目
exports.createItem = async (req, res) => {
  try {
    const itemData = req.body;

    // 驗證必填字段
    if (!itemData.bom_id || !itemData.material_id || !itemData.sheet_type) {
      return res.status(400).json({
        status: 'fail',
        message: 'bom_id, material_id 和 sheet_type 為必填欄位'
      });
    }

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: itemData.bom_id }).first();
    if (!bomExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該 BOM'
      });
    }

    // 檢查材料用量是否存在
    const materialExists = await db('bom_material_usage').where({ id: itemData.material_id }).first();
    if (!materialExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該材料用量記錄'
      });
    }

    // 計算面積總計和總合
    const width = parseFloat(itemData.width) || 0;
    const height = parseFloat(itemData.height) || 0;
    const quantity = parseFloat(itemData.quantity) || 1;
    const area = width * height;
    const totalArea = area * quantity;

    // 準備要插入的數據
    const newItem = {
      id: uuidv4(),
      bom_id: itemData.bom_id,
      material_id: itemData.material_id,
      sheet_type: itemData.sheet_type,
      number: itemData.number || null,
      position_name: itemData.position_name || null,
      material_name: itemData.material_name || null,
      material_code: itemData.material_code || null,
      width: width,
      height: height,
      area: area,
      quantity: quantity,
      total_area: totalArea,
      is_split: itemData.is_split || false,
      grand_total: totalArea, // 預設與面積總計相同
      created_by: itemData.created_by || null,
      created_by_name: itemData.created_by_name || null,
      createdAt: db.fn.now(),
      updatedAt: db.fn.now()
    };

    // 插入數據庫
    await db('material_detail_sheets').insert(newItem);

    // 查詢剛創建的記錄並返回
    const createdItem = await db('material_detail_sheets').where({ id: newItem.id }).first();

    res.status(201).json({
      status: 'success',
      data: createdItem
    });
  } catch (error) {
    console.error('創建產品用料明細表項目失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '創建產品用料明細表項目失敗: ' + error.message
    });
  }
};

// 更新產品用料明細表項目
exports.updateItem = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // 檢查項目是否存在
    const itemExists = await db('material_detail_sheets').where({ id }).first();
    if (!itemExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該產品用料明細表項目'
      });
    }

    // 計算面積總計和總合
    let updateFields = { ...updateData };

    if (updateData.width !== undefined || updateData.height !== undefined || updateData.quantity !== undefined) {
      const width = updateData.width !== undefined ? parseFloat(updateData.width) : parseFloat(itemExists.width);
      const height = updateData.height !== undefined ? parseFloat(updateData.height) : parseFloat(itemExists.height);
      const quantity = updateData.quantity !== undefined ? parseFloat(updateData.quantity) : parseFloat(itemExists.quantity);

      const area = width * height;
      const totalArea = area * quantity;

      updateFields = {
        ...updateFields,
        width,
        height,
        area,
        quantity,
        total_area: totalArea,
        grand_total: totalArea // 預設與面積總計相同
      };
    }

    // 更新時間戳
    updateFields.updatedAt = db.fn.now();

    // 更新數據庫
    await db('material_detail_sheets').where({ id }).update(updateFields);

    // 查詢更新後的記錄並返回
    const updatedItem = await db('material_detail_sheets').where({ id }).first();

    res.status(200).json({
      status: 'success',
      data: updatedItem
    });
  } catch (error) {
    console.error('更新產品用料明細表項目失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '更新產品用料明細表項目失敗: ' + error.message
    });
  }
};

// 刪除產品用料明細表項目
exports.deleteItem = async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查項目是否存在
    const itemExists = await db('material_detail_sheets').where({ id }).first();
    if (!itemExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該產品用料明細表項目'
      });
    }

    // 刪除數據庫記錄
    await db('material_detail_sheets').where({ id }).delete();

    res.status(200).json({
      status: 'success',
      message: '產品用料明細表項目刪除成功'
    });
  } catch (error) {
    console.error('刪除產品用料明細表項目失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除產品用料明細表項目失敗: ' + error.message
    });
  }
};

// 匯入Excel檔案
exports.importExcel = async (req, res) => {
  try {
    console.log('開始處理Excel檔案上傳請求');
    console.log('req.files:', req.files);
    console.log('req.body:', req.body);

    // 檢查是否有檔案上傳
    if (!req.files || !req.files.file) {
      console.log('沒有上傳檔案');
      return res.status(400).json({
        status: 'fail',
        message: '沒有上傳檔案'
      });
    }

    const { bomId, sheetType } = req.body;
    // 從請求中獲取 materialId，但不再將其作為必要參數
    const materialId = req.body.materialId || null;
    console.log('參數:', { bomId, materialId, sheetType });

    // 驗證必要的參數
    if (!bomId || !sheetType) {
      console.log('缺少必要參數');
      return res.status(400).json({
        status: 'fail',
        message: 'bomId 和 sheetType 為必填參數'
      });
    }

    // 檢查表是否存在
    const tableExists = await db.schema.hasTable('material_detail_sheets');
    if (!tableExists) {
      console.log('material_detail_sheets 表不存在');
      return res.status(500).json({
        status: 'error',
        message: '資料表不存在，請先執行遷移'
      });
    }

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: bomId }).first();
    if (!bomExists) {
      console.log('找不到該 BOM:', bomId);
      return res.status(404).json({
        status: 'fail',
        message: '找不到該 BOM'
      });
    }

    try {
      const file = req.files.file;
      console.log('檔案資訊:', { name: file.name, size: file.size, mimetype: file.mimetype });

      const uploadPath = path.join(__dirname, '../database/uploads/temp', file.name);
      console.log('上傳路徑:', uploadPath);

      // 確保目錄存在
      const uploadDir = path.dirname(uploadPath);
      if (!fs.existsSync(uploadDir)) {
        console.log('創建目錄:', uploadDir);
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 保存檔案
      console.log('開始保存檔案...');
      await new Promise((resolve, reject) => {
        file.mv(uploadPath, (err) => {
          if (err) {
            console.error('保存檔案失敗:', err);
            reject(err);
          } else {
            console.log('檔案已保存到:', uploadPath);
            resolve();
          }
        });
      });

      // 檢查檔案是否存在
      if (!fs.existsSync(uploadPath)) {
        console.error('檔案保存後不存在:', uploadPath);
        return res.status(500).json({
          status: 'error',
          message: '檔案上傳失敗，請重試'
        });
      }

      // 讀取Excel檔案
      console.log('開始讀取Excel檔案...');
      const workbook = XLSX.readFile(uploadPath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null });
      console.log('讀取到數據行數:', data.length);

      // 解析Excel數據
      const items = [];
      let startRow = -1;

      // 尋找表頭行
      let headerRow = null;
      let headerIndexes = {};

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (row && row.length > 0) {
          // 檢查是否包含表頭關鍵字
          const headerStr = row.join(' ').toLowerCase();
          console.log('檢查行:', i, headerStr);

          // 檢查是否包含部位名稱或材料名稱
          if (headerStr.includes('部位名稱') || headerStr.includes('材料名稱')) {
            headerRow = row;
            startRow = i + 1; // 表頭的下一行開始是數據
            console.log('找到表頭行:', i, '數據開始行:', startRow);

            // 尋找每個欄位的索引
            for (let j = 0; j < headerRow.length; j++) {
              const header = String(headerRow[j] || '').toLowerCase().trim();
              if (header.includes('部位名稱')) {
                headerIndexes.positionName = j;
              } else if (header.includes('材料名稱')) {
                headerIndexes.materialName = j;
              } else if (header.includes('料號')) {
                headerIndexes.materialCode = j;
              } else if (header.includes('寬') || header === '寬(cm)') {
                headerIndexes.width = j;
              } else if (header.includes('高') || header === '高(cm)') {
                headerIndexes.height = j;
              } else if (header.includes('面積') && !header.includes('總計')) {
                headerIndexes.area = j;
              } else if (header.includes('組成數量')) {
                headerIndexes.quantity = j;
              } else if (header.includes('面積總計') || header.includes('面積總')) {
                headerIndexes.totalArea = j;
              } else if (header.includes('分片')) {
                headerIndexes.isSplit = j;
              } else if (header.includes('總合')) {
                headerIndexes.grandTotal = j;
              }
            }

            console.log('欄位索引:', headerIndexes);
            break;
          }
        }
      }

      if (startRow === -1) {
        console.log('無法識別Excel檔案格式，找不到表頭');
        // 刪除臨時檔案
        try {
          fs.unlinkSync(uploadPath);
          console.log('刪除臨時檔案:', uploadPath);
        } catch (err) {
          console.error('刪除臨時檔案失敗:', err);
        }

        return res.status(400).json({
          status: 'fail',
          message: '無法識別Excel檔案格式，找不到表頭'
        });
      }

      // 解析數據行
      console.log('開始解析數據行...');

      // 尋找部位名稱標記行 - 特別關注D列
      const positionMarkers = [];
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (!row || row.length === 0) continue;

        // 檢查D列（索引為3）是否包含標記文字
        // 如果D列不存在，則嘗試使用部位名稱欄位
        const dColumnValue = row[3] ? String(row[3]).trim() : null;
        const positionName = headerIndexes.positionName !== undefined && row[headerIndexes.positionName] ?
                            String(row[headerIndexes.positionName]).trim() : null;

        // 優先檢查D列，如果D列沒有標記則檢查部位名稱欄位
        const markerText = dColumnValue || positionName;

        // 檢查是否為標記行（通常是「產品用料明細表(主料)」、「產品用料明細表(副料)」、「產品用料明細表(其他)」）
        if (markerText && (
            markerText.includes('產品用料明細表(主料)') ||
            markerText.includes('產品用料明細表(副料)') ||
            markerText.includes('產品用料明細表(其他)')
        )) {
          console.log('找到標記行:', i, markerText);
          positionMarkers.push({
            row: i,
            name: markerText.includes('主料') ? '主料' :
                  markerText.includes('副料') ? '副料' : '其他'
          });
        }
      }

      console.log('找到部位名稱標記行數量:', positionMarkers.length);

      // 如果沒有找到部位名稱標記行，則使用預設的 sheetType
      if (positionMarkers.length === 0) {
        console.log('沒有找到部位名稱標記行，使用預設的 sheetType:', sheetType);

        // 使用原來的解析邏輯
        for (let i = startRow; i < data.length; i++) {
          const row = data[i];
          if (!row || row.length === 0) {
            console.log('跳過空行:', i);
            continue; // 跳過空行
          }

          // 檢查是否有部位名稱或材料名稱
          const hasPositionName = headerIndexes.positionName !== undefined && row[headerIndexes.positionName];
          const hasMaterialName = headerIndexes.materialName !== undefined && row[headerIndexes.materialName];

          if (!hasPositionName && !hasMaterialName) {
            console.log('跳過無部位名稱和材料名稱的行:', i);
            continue; // 跳過無部位名稱和材料名稱的行
          }

          // 檢查是否為標題行（標題行的內容與標題相同）
          const isHeaderRow = (
            (headerIndexes.positionName !== undefined &&
             row[headerIndexes.positionName] === headerRow[headerIndexes.positionName]) ||
            (headerIndexes.materialName !== undefined &&
             row[headerIndexes.materialName] === headerRow[headerIndexes.materialName])
          );

          if (isHeaderRow) {
            console.log('跳過標題行:', i);
            continue; // 跳過標題行
          }

          // 解析行數據
          const item = parseRow(row, headerIndexes, bomId, sheetType, null);
          if (item) items.push(item);
        }
      } else {
        // 根據部位名稱標記行分組處理數據
        for (let j = 0; j < positionMarkers.length; j++) {
          const currentMarker = positionMarkers[j];
          const nextMarker = positionMarkers[j + 1];
          const currentType = currentMarker.name; // 主料、副料或其他

          console.log(`處理 ${currentType} 部分，從行 ${currentMarker.row + 1} 開始`);

          // 確定當前部分的結束行
          const endRow = nextMarker ? nextMarker.row : data.length;

          // 處理當前部分的數據行
          for (let i = currentMarker.row + 1; i < endRow; i++) {
            const row = data[i];
            if (!row || row.length === 0) {
              console.log('跳過空行:', i);
              continue; // 跳過空行
            }

            // 檢查是否有部位名稱或材料名稱
            const hasPositionName = headerIndexes.positionName !== undefined && row[headerIndexes.positionName];
            const hasMaterialName = headerIndexes.materialName !== undefined && row[headerIndexes.materialName];

            if (!hasPositionName && !hasMaterialName) {
              console.log('跳過無部位名稱和材料名稱的行:', i);
              continue; // 跳過無部位名稱和材料名稱的行
            }

            // 檢查是否為標題行（標題行的內容與標題相同）
            const isHeaderRow = (
              (headerIndexes.positionName !== undefined &&
               row[headerIndexes.positionName] === headerRow[headerIndexes.positionName]) ||
              (headerIndexes.materialName !== undefined &&
               row[headerIndexes.materialName] === headerRow[headerIndexes.materialName])
            );

            if (isHeaderRow) {
              console.log('跳過標題行:', i);
              continue; // 跳過標題行
            }

            // 解析行數據，使用當前部分的類型
            const item = parseRow(row, headerIndexes, bomId, currentType, null);
            if (item) items.push(item);
          }
        }
      }

      // 輔助函數：解析行數據
      function parseRow(row, headerIndexes, bomId, sheetType, materialId) {
        // 使用欄位索引取值，如果沒有索引則使用預設值
        const positionName = headerIndexes.positionName !== undefined && row[headerIndexes.positionName] ?
                            String(row[headerIndexes.positionName]).trim() : null;

        // 先假設第一列為號碼，如果沒有其他欄位索引
        let number = row[0] ? String(row[0]).trim() : null;

        // 特殊處理：如果部位名稱包含序號，如"-1前上片皮"，則提取序號
        if (positionName && positionName.match(/^-?\d+/)) {
          const match = positionName.match(/^(-?\d+)(.+)/);
          if (match) {
            number = match[1]; // 序號部分，如 "-1"
            // 不修改 positionName，保留完整的部位名稱
          }
        }

        const materialName = headerIndexes.materialName !== undefined && row[headerIndexes.materialName] ?
                            String(row[headerIndexes.materialName]).trim() : null;
        const materialCode = headerIndexes.materialCode !== undefined && row[headerIndexes.materialCode] ?
                            String(row[headerIndexes.materialCode]).trim() : null;

        const width = headerIndexes.width !== undefined && row[headerIndexes.width] ?
                     parseFloat(row[headerIndexes.width]) : 0;
        const height = headerIndexes.height !== undefined && row[headerIndexes.height] ?
                      parseFloat(row[headerIndexes.height]) : 0;

        const area = headerIndexes.area !== undefined && row[headerIndexes.area] ?
                    parseFloat(row[headerIndexes.area]) : (width * height);

        const quantity = headerIndexes.quantity !== undefined && row[headerIndexes.quantity] ?
                        parseFloat(row[headerIndexes.quantity]) : 1;

        const totalArea = headerIndexes.totalArea !== undefined && row[headerIndexes.totalArea] ?
                         parseFloat(row[headerIndexes.totalArea]) : (area * quantity);

        const isSplit = headerIndexes.isSplit !== undefined && row[headerIndexes.isSplit] ?
                       String(row[headerIndexes.isSplit]).trim() === '是' : false;

        const grandTotal = headerIndexes.grandTotal !== undefined && row[headerIndexes.grandTotal] ?
                          parseFloat(row[headerIndexes.grandTotal]) : totalArea;

        // 根據材料名稱或部位名稱確定 sheet_type
        // 如果已經有指定的 sheetType，則使用它
        // 這確保了從標記行分組的數據會使用正確的類型
        let finalSheetType = sheetType;

        console.log('解析行:', { number, positionName, materialName, width, height, area, quantity, totalArea, sheetType: finalSheetType });

        // 創建項目
        return {
          id: uuidv4(),
          bom_id: bomId,
          material_id: null, // 設置為 null，避免 NOT NULL 約束錯誤
          sheet_type: finalSheetType,
          number,
          position_name: positionName,
          material_name: materialName,
          material_code: materialCode,
          width,
          height,
          area,
          quantity,
          total_area: totalArea,
          is_split: isSplit,
          grand_total: grandTotal,
          createdAt: db.fn.now(),
          updatedAt: db.fn.now()
        };
      }

      // 刪除臨時檔案
      try {
        fs.unlinkSync(uploadPath);
        console.log('刪除臨時檔案:', uploadPath);
      } catch (err) {
        console.error('刪除臨時檔案失敗:', err);
      }

      if (items.length === 0) {
        console.log('沒有找到有效的數據行');
        return res.status(400).json({
          status: 'fail',
          message: '沒有找到有效的數據行'
        });
      }

      console.log('解析到', items.length, '筆有效數據');

      // 先刪除現有的項目
      console.log('刪除現有的項目...');
      await db('material_detail_sheets')
        .where({
          bom_id: bomId,
          sheet_type: sheetType
          // 移除 material_id 條件，使明細表在整個 BOM 中通用
        })
        .delete();

      // 批量插入新項目
      console.log('插入新項目...');
      await db('material_detail_sheets').insert(items);
      console.log('插入完成');

      res.status(200).json({
        status: 'success',
        message: `成功匯入 ${items.length} 筆資料`,
        data: {
          count: items.length
        }
      });
    } catch (error) {
      console.error('處理Excel檔案時發生錯誤:', error);
      res.status(500).json({
        status: 'error',
        message: '處理Excel檔案時發生錯誤: ' + error.message
      });
    }
  } catch (error) {
    console.error('匯入Excel檔案失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '匯入Excel檔案失敗: ' + error.message
    });
  }
};

// 刪除所有產品用料明細表項目
exports.deleteAllItems = async (req, res) => {
  try {
    const { bomId, sheetType } = req.query;
    // 從請求中獲取 materialId，但不再將其作為必要參數
    const materialId = req.query.materialId || null;

    // 驗證必要的查詢參數
    if (!bomId || !sheetType) {
      return res.status(400).json({
        status: 'fail',
        message: 'bomId 和 sheetType 為必填查詢參數'
      });
    }

    // 檢查表是否存在
    const tableExists = await db.schema.hasTable('material_detail_sheets');
    if (!tableExists) {
      console.log('material_detail_sheets 表不存在');
      return res.status(500).json({
        status: 'error',
        message: '資料表不存在，請先執行遷移'
      });
    }

    // 查詢符合條件的項目數量
    const whereClause = {
      bom_id: bomId,
      sheet_type: sheetType
    };

    // 如果提供了 materialId，則加入條件
    if (materialId) {
      whereClause.material_id = materialId;
    }

    const count = await db('material_detail_sheets')
      .where(whereClause)
      .count('id as count')
      .first();

    const itemCount = count ? count.count : 0;

    // 刪除符合條件的所有項目
    await db('material_detail_sheets')
      .where(whereClause)
      .delete();

    res.status(200).json({
      status: 'success',
      message: `成功刪除 ${itemCount} 筆明細表項目`,
      data: {
        count: itemCount
      }
    });
  } catch (error) {
    console.error('刪除所有產品用料明細表項目失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除所有產品用料明細表項目失敗: ' + error.message
    });
  }
};