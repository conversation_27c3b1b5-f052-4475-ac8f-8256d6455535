const LeatherMaterial = require('../models/LeatherMaterial');
const Material = require('../models/Material');
const { v4: uuidv4 } = require('uuid');

// 獲取皮料特殊資料列表
const getLeatherMaterials = async (req, res) => {
  try {
    // 從查詢參數獲取過濾條件
    const filters = {
      material_id: req.query.material_id,
      warehouse: req.query.warehouse,
      leather_code: req.query.leather_code
    };

    const leatherMaterials = await LeatherMaterial.getAllLeatherMaterials(filters);

    res.json({
      status: 'success',
      data: leatherMaterials
    });
  } catch (error) {
    console.error('獲取皮料特殊資料失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取皮料特殊資料失敗: ' + error.message
    });
  }
};

// 獲取單個皮料特殊資料詳情
const getLeatherMaterialById = async (req, res) => {
  try {
    const id = req.params.id;
    const leatherMaterial = await LeatherMaterial.getLeatherMaterialById(id);

    if (!leatherMaterial) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該皮料特殊資料'
      });
    }

    res.json({
      status: 'success',
      data: leatherMaterial
    });
  } catch (error) {
    console.error('獲取皮料特殊資料失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取皮料特殊資料失敗: ' + error.message
    });
  }
};

// 創建皮料特殊資料
const createLeatherMaterial = async (req, res) => {
  try {
    const leatherData = req.body;

    // 驗證必填字段
    if (!leatherData.material_id) {
      return res.status(400).json({
        status: 'error',
        message: '原物料ID為必填項'
      });
    }

    if (!leatherData.warehouse) {
      return res.status(400).json({
        status: 'error',
        message: '存放倉庫為必填項'
      });
    }

    if (!leatherData.area || leatherData.area <= 0) {
      return res.status(400).json({
        status: 'error',
        message: '皮料面積必須大於0'
      });
    }

    // 獲取原物料信息
    const material = await Material.getMaterialById(leatherData.material_id);
    if (!material) {
      return res.status(404).json({
        status: 'error',
        message: '找不到對應的原物料'
      });
    }

    // 添加材料編號和名稱
    leatherData.material_code = material.code;
    leatherData.material_name = material.name;

    // 添加認證用戶信息
    if (req.user) {
      leatherData.created_by = req.user.id;
      leatherData.created_by_name = req.user.name;
    }

    // 創建皮料特殊資料
    const newLeatherMaterial = await LeatherMaterial.createLeatherMaterial(leatherData);

    res.status(201).json({
      status: 'success',
      data: newLeatherMaterial
    });
  } catch (error) {
    console.error('創建皮料特殊資料失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '創建皮料特殊資料失敗: ' + error.message
    });
  }
};

// 更新皮料特殊資料
const updateLeatherMaterial = async (req, res) => {
  try {
    const id = req.params.id;
    const updateData = req.body;

    // 檢查是否存在
    const existingLeatherMaterial = await LeatherMaterial.getLeatherMaterialById(id);
    if (!existingLeatherMaterial) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該皮料特殊資料'
      });
    }

    // 更新皮料特殊資料
    const updatedLeatherMaterial = await LeatherMaterial.updateLeatherMaterial(id, updateData);

    res.json({
      status: 'success',
      data: updatedLeatherMaterial
    });
  } catch (error) {
    console.error('更新皮料特殊資料失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '更新皮料特殊資料失敗: ' + error.message
    });
  }
};

// 刪除皮料特殊資料
const deleteLeatherMaterial = async (req, res) => {
  try {
    const id = req.params.id;

    // 檢查是否存在
    const existingLeatherMaterial = await LeatherMaterial.getLeatherMaterialById(id);
    if (!existingLeatherMaterial) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該皮料特殊資料'
      });
    }

    // 刪除皮料特殊資料
    await LeatherMaterial.deleteLeatherMaterial(id);

    res.json({
      status: 'success',
      message: '皮料特殊資料已成功刪除'
    });
  } catch (error) {
    console.error('刪除皮料特殊資料失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除皮料特殊資料失敗: ' + error.message
    });
  }
};

module.exports = {
  getLeatherMaterials,
  getLeatherMaterialById,
  createLeatherMaterial,
  updateLeatherMaterial,
  deleteLeatherMaterial
}; 