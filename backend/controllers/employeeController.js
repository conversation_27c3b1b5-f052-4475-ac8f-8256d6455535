const Employee = require('../models/employee');
const bcrypt = require('bcryptjs');

// 獲取所有員工
exports.getAllEmployees = async (req, res) => {
  try {
    const employees = await Employee.findAll();
    
    // 不返回密碼
    const safeEmployees = employees.map(emp => {
      const { password, ...safeEmp } = emp;
      return safeEmp;
    });
    
    res.status(200).json({
      status: 'success',
      data: safeEmployees
    });
  } catch (error) {
    console.error('獲取員工列表失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取員工列表失敗'
    });
  }
};

// 獲取單個員工詳情
exports.getEmployeeById = async (req, res) => {
  try {
    const { id } = req.params;
    const employee = await Employee.findById(id);
    
    if (!employee) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該員工'
      });
    }
    
    // 不返回密碼
    const { password, ...safeEmployee } = employee;
    
    res.status(200).json({
      status: 'success',
      data: safeEmployee
    });
  } catch (error) {
    console.error('獲取員工詳情失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取員工詳情失敗'
    });
  }
};

// 創建新員工
exports.createEmployee = async (req, res) => {
  try {
    const { 
      username, 
      password, 
      employeeId, 
      name, 
      department, 
      position, 
      role, 
      email, 
      phone, 
      level, 
      notes
    } = req.body;

    console.log('創建員工原始數據:', req.body);
    console.log('部門值和類型:', department, typeof department);
    console.log('角色值和類型:', role, typeof role);

    // 檢查必填項
    if (!username || !password || !employeeId || !name || department === undefined) {
      return res.status(400).json({
        status: 'fail',
        message: '帳號、密碼、工號、姓名和部門為必填項'
      });
    }
    
    // 檢查用戶名是否已存在
    const existingUsername = await Employee.findByUsername(username);
    if (existingUsername) {
      return res.status(400).json({
        status: 'fail',
        message: '帳號已被使用'
      });
    }
    
    // 檢查工號是否已存在
    const existingEmployeeId = await Employee.findByEmployeeId(employeeId);
    if (existingEmployeeId) {
      return res.status(400).json({
        status: 'fail',
        message: '工號已被使用'
      });
    }
    
    // 創建新員工，確保數字欄位使用數字類型
    const newEmployee = await Employee.create({
      username,
      password,
      employeeId,
      name,
      department: Number(department),
      position,
      role: role !== undefined ? Number(role) : 1, // 默認為組員
      email,
      phone,
      level,
      notes
    });
    
    // 不返回密碼
    const { password: pwd, ...safeEmployee } = newEmployee;
    
    res.status(201).json({
      status: 'success',
      data: safeEmployee
    });
  } catch (error) {
    console.error('創建員工失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '創建員工失敗: ' + error.message
    });
  }
};

// 更新員工資料
exports.updateEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      username, 
      password, 
      employeeId, 
      name, 
      department, 
      position, 
      role, 
      email, 
      phone, 
      level, 
      notes 
    } = req.body;
    
    console.log('更新員工原始數據:', req.body);
    console.log('部門值和類型:', department, typeof department);
    console.log('角色值和類型:', role, typeof role);
    
    // 檢查員工是否存在
    const employee = await Employee.findById(id);
    if (!employee) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該員工'
      });
    }
    
    // 確保admin帳號不會被改成非管理員
    if (employee.username === 'admin' && role !== undefined && Number(role) !== 0) {
      return res.status(400).json({
        status: 'fail',
        message: '不能更改admin的權限'
      });
    }
    
    // 如果要更改用戶名，檢查是否已被使用
    if (username && username !== employee.username) {
      const existingUsername = await Employee.findByUsername(username);
      if (existingUsername) {
        return res.status(400).json({
          status: 'fail',
          message: '帳號已被使用'
        });
      }
    }
    
    // 如果要更改工號，檢查是否已被使用
    if (employeeId && employeeId !== employee.employeeId) {
      const existingEmployeeId = await Employee.findByEmployeeId(employeeId);
      if (existingEmployeeId) {
        return res.status(400).json({
          status: 'fail',
          message: '工號已被使用'
        });
      }
    }
    
    // 構建更新數據
    const updateData = {};
    if (username) updateData.username = username;
    if (password) {
      // 只有在密碼不為空時才更新密碼
      updateData.password = password;
    }
    if (employeeId) updateData.employeeId = employeeId;
    if (name) updateData.name = name;
    if (department !== undefined) updateData.department = Number(department);
    if (position !== undefined) updateData.position = position;
    if (role !== undefined) updateData.role = Number(role);
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (level !== undefined) updateData.level = level;
    if (notes !== undefined) updateData.notes = notes;
    
    console.log('更新數據:', updateData);
    
    // 更新員工資料
    const updatedEmployee = await Employee.update(id, updateData);
    
    // 不返回密碼
    const { password: pwd, ...safeEmployee } = updatedEmployee;
    
    res.status(200).json({
      status: 'success',
      data: safeEmployee
    });
  } catch (error) {
    console.error('更新員工失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '更新員工失敗: ' + error.message
    });
  }
};

// 刪除員工
exports.deleteEmployee = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 檢查員工是否存在
    const employee = await Employee.findById(id);
    if (!employee) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該員工'
      });
    }
    
    // 不允許刪除admin帳號
    if (employee.username === 'admin') {
      return res.status(400).json({
        status: 'fail',
        message: '不能刪除系統管理員帳號'
      });
    }
    
    await Employee.delete(id);
    
    res.status(200).json({
      status: 'success',
      message: '員工已成功刪除'
    });
  } catch (error) {
    console.error('刪除員工失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除員工失敗: ' + error.message
    });
  }
};
