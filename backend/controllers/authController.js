const jwt = require('jsonwebtoken');
const Employee = require('../models/employee');

// 簽發 JWT token
const signToken = id => {
  return jwt.sign({ id }, process.env.JWT_SECRET || 'your-secret-key', {
    expiresIn: process.env.JWT_EXPIRES_IN || '1d'
  });
};

// 用戶登入
exports.login = async (req, res, next) => {
  try {
    const { username, password } = req.body;

    // 檢查是否提供了用戶名和密碼
    if (!username || !password) {
      return res.status(400).json({
        status: 'error',
        message: '請提供帳號和密碼'
      });
    }

    // 查找用戶
    const employee = await Employee.findByUsername(username);
    
    // 檢查用戶是否存在及密碼是否正確
    if (!employee || !(await Employee.checkPassword(username, password))) {
      return res.status(401).json({
        status: 'error',
        message: '帳號或密碼不正確'
      });
    }

    // 生成 token
    const token = signToken(employee.id);

    // 不返回密碼給客戶端
    delete employee.password;

    // 發送響應
    res.status(200).json({
      status: 'success',
      token,
      data: {
        employee
      }
    });
  } catch (error) {
    next(error);
  }
};

// 保護路由中間件
exports.protect = async (req, res, next) => {
  try {
    let token;
    
    // 檢查請求頭中是否有 token
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: '您尚未登入，請先登入以獲取訪問權限'
      });
    }

    // 驗證 token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // 檢查用戶是否仍然存在
    const currentEmployee = await Employee.findById(decoded.id);
    if (!currentEmployee) {
      return res.status(401).json({
        status: 'error',
        message: '此 token 對應的用戶已不存在'
      });
    }

    // 將用戶信息添加到請求對象中
    req.employee = currentEmployee;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        status: 'error',
        message: '無效的 token，請重新登入'
      });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        status: 'error',
        message: 'token 已過期，請重新登入'
      });
    }
    next(error);
  }
};

// 獲取當前用戶信息
exports.getMe = (req, res) => {
  res.status(200).json({
    status: 'success',
    data: {
      employee: req.employee
    }
  });
}; 