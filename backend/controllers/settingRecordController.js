const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');

// 獲取所有設定記錄
exports.getAllSettingRecords = async (req, res) => {
  try {
    let query = db('settingRecord').select('*');
    
    // 根據類型過濾
    if (req.query.type) {
      query = query.where('type', req.query.type);
    }
    
    // 排序
    const sortBy = req.query.sortBy || 'sort_order';
    const sortOrder = req.query.sortOrder || 'asc';
    query = query.orderBy(sortBy, sortOrder);
    
    // 只獲取啟用的設定（如果指定）
    if (req.query.is_active === 'true') {
      query = query.where('is_active', true);
    }
    
    const settingRecords = await query;
    
    return res.status(200).json({
      status: 'success',
      data: settingRecords
    });
  } catch (error) {
    console.error('獲取設定記錄失敗:', error);
    return res.status(500).json({
      status: 'error',
      message: '獲取設定記錄失敗: ' + error.message
    });
  }
};

// 獲取單個設定記錄
exports.getSettingRecordById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const settingRecord = await db('settingRecord').where({ id }).first();
    
    if (!settingRecord) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該設定記錄'
      });
    }
    
    return res.status(200).json({
      status: 'success',
      data: settingRecord
    });
  } catch (error) {
    console.error('獲取設定記錄詳情失敗:', error);
    return res.status(500).json({
      status: 'error',
      message: '獲取設定記錄詳情失敗: ' + error.message
    });
  }
};

// 創建新設定記錄
exports.createSettingRecord = async (req, res) => {
  try {
    // 檢查請求體是否包含必要欄位
    if (!req.body.type || !req.body.name || !req.body.value) {
      return res.status(400).json({
        status: 'error',
        message: '請提供所有必要的欄位 (type, name, value)'
      });
    }
    
    // 創建設定記錄
    const newSettingRecord = {
      id: uuidv4(),
      type: req.body.type,
      name: req.body.name,
      value: req.body.value,
      description: req.body.description || '',
      sort_order: req.body.sort_order || 0,
      is_active: req.body.is_active !== undefined ? req.body.is_active : true,
      createdAt: db.fn.now(),
      updatedAt: db.fn.now()
    };
    
    await db('settingRecord').insert(newSettingRecord);
    
    // 獲取新建的設定記錄
    const createdSettingRecord = await db('settingRecord').where({ id: newSettingRecord.id }).first();
    
    return res.status(201).json({
      status: 'success',
      data: createdSettingRecord
    });
  } catch (error) {
    console.error('創建設定記錄失敗:', error);
    return res.status(500).json({
      status: 'error',
      message: '創建設定記錄失敗: ' + error.message
    });
  }
};

// 更新設定記錄
exports.updateSettingRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 檢查設定記錄是否存在
    const existingSettingRecord = await db('settingRecord').where({ id }).first();
    
    if (!existingSettingRecord) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該設定記錄'
      });
    }
    
    // 更新設定記錄
    const updatedSettingRecord = {
      type: req.body.type || existingSettingRecord.type,
      name: req.body.name || existingSettingRecord.name,
      value: req.body.value || existingSettingRecord.value,
      description: req.body.description !== undefined ? req.body.description : existingSettingRecord.description,
      sort_order: req.body.sort_order !== undefined ? req.body.sort_order : existingSettingRecord.sort_order,
      is_active: req.body.is_active !== undefined ? req.body.is_active : existingSettingRecord.is_active,
      updatedAt: db.fn.now()
    };
    
    await db('settingRecord').where({ id }).update(updatedSettingRecord);
    
    // 獲取更新後的設定記錄
    const updatedRecord = await db('settingRecord').where({ id }).first();
    
    return res.status(200).json({
      status: 'success',
      data: updatedRecord
    });
  } catch (error) {
    console.error('更新設定記錄失敗:', error);
    return res.status(500).json({
      status: 'error',
      message: '更新設定記錄失敗: ' + error.message
    });
  }
};

// 刪除設定記錄
exports.deleteSettingRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 檢查設定記錄是否存在
    const settingRecord = await db('settingRecord').where({ id }).first();
    
    if (!settingRecord) {
      return res.status(404).json({
        status: 'error',
        message: '找不到該設定記錄'
      });
    }
    
    // 從資料庫刪除設定記錄
    await db('settingRecord').where({ id }).del();
    
    return res.status(200).json({
      status: 'success',
      message: '設定記錄已成功刪除'
    });
  } catch (error) {
    console.error('刪除設定記錄失敗:', error);
    return res.status(500).json({
      status: 'error',
      message: '刪除設定記錄失敗: ' + error.message
    });
  }
};
