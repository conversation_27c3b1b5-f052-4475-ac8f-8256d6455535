const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('../database/db');

// 獲取所有BOM列表
exports.getAllBoms = async (req, res) => {
  try {
    // 修改：增加按 createdAt 降冪排序
    const bomData = await db('bomVersion').select('*').orderBy('createdAt', 'desc');

    res.set('Cache-Control', 'no-cache');
    res.status(200).json({
      status: 'success',
      data: bomData
    });
  } catch (error) {
    console.error('更新BOM失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '更新BOM失敗: ' + error.message
    });
  }
}

// 獲取單個BOM詳情
exports.getBomById = async (req, res) => {
  try {
    const { id } = req.params;
    const bom = await db('bomVersion').where({ id }).first();

    if (!bom) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該BOM'
      });
    }

    res.status(200).json({
      status: 'success',
      data: bom
    });
  } catch (error) {
    console.error('獲取BOM詳情失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取BOM詳情失敗'
    });
  }
};

// 獲取指定 BOM 的材料用量列表
exports.getBomMaterialUsage = async (req, res) => {
  try {
    const { bomId } = req.params;

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: bomId }).first();
    if (!bomExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該 BOM'
      });
    }

    // 查詢該 BOM 的材料用量，按創建時間降冪排序 (最新的在前面)
    const materialUsage = await db('bom_material_usage')
      .where({ bom_id: bomId })
      .orderBy('createdAt', 'desc');

    res.status(200).json({
      status: 'success',
      data: materialUsage
    });
  } catch (error) {
    console.error('獲取 BOM 材料用量失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '獲取 BOM 材料用量失敗: ' + error.message
    });
  }
};

// 為指定 BOM 添加材料用量記錄
exports.createBomMaterialUsage = async (req, res) => {
  try {
    const { bomId } = req.params;
    const usageData = req.body;

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: bomId }).first();
    if (!bomExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該 BOM'
      });
    }

    // 驗證必填字段
    if (!usageData.materialType || !usageData.materialName || !usageData.quantity || !usageData.unit) {
      return res.status(400).json({
        status: 'fail',
        message: '材料類型、名稱、數量和單位為必填欄位'
      });
    }

    // 準備要插入的數據
    const newUsage = {
      id: uuidv4(),
      bom_id: bomId,
      material_type: usageData.materialType,
      material_name: usageData.materialName,
      quantity: parseFloat(usageData.quantity),
      unit: usageData.unit,
      remark: usageData.remark || null,
      inventory_id: usageData.inventoryId || null,
      code: usageData.code || null, // 如果前端傳遞了庫存代號
      item_number: usageData.item_number || null, // 項次
      material_category: usageData.material_category || '主料', // 材料類別：主料、副料或其他
      usage_position: usageData.usage_position || null, // 使用部位
      image_url: usageData.image_url || null, // 圖片 URL
      createdAt: db.fn.now(),
      updatedAt: db.fn.now()
    };

    // 插入數據庫
    await db('bom_material_usage').insert(newUsage);

    // 查詢剛創建的記錄並返回
    const createdUsage = await db('bom_material_usage').where({ id: newUsage.id }).first();

    res.status(201).json({
      status: 'success',
      data: createdUsage
    });
  } catch (error) {
    console.error('添加 BOM 材料用量失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '添加 BOM 材料用量失敗: ' + error.message
    });
  }
};

// 上傳 BOM 材料用量圖片
exports.uploadBomMaterialImage = async (req, res) => {
  try {
    const { bomId } = req.params;

    // 檢查 BOM 是否存在
    const bomExists = await db('bomVersion').where({ id: bomId }).first();
    if (!bomExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該 BOM'
      });
    }

    // 檢查是否有圖片數據
    if (!req.body.image || !req.body.image.startsWith('data:image')) {
      return res.status(400).json({
        status: 'fail',
        message: '無效的圖片數據'
      });
    }

    // 創建目錄
    const uploadDir = path.join(__dirname, '../database/uploads/materials/bom_images');

    // 確保目錄存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 處理 base64 圖片數據
    const imgData = req.body.image.split(';base64,').pop();
    const imgType = req.body.image.split(';')[0].split('/')[1];
    const imgName = `bom_${bomId}_${Date.now()}.${imgType}`;
    const imgPath = path.join(uploadDir, imgName);

    // 寫入檔案
    fs.writeFileSync(imgPath, imgData, { encoding: 'base64' });

    // 設置圖片URL - 使用相對於 API 的路徑
    const imageUrl = `/database/uploads/materials/bom_images/${imgName}`;

    // 將圖片路徑存入數據庫，作為一個獨立的材料用量記錄
    // 將圖片記錄存入數據庫
    const imageId = uuidv4();
    const newImageRecord = {
      id: imageId,
      bom_id: bomId,
      image_url: imageUrl,
      createdAt: db.fn.now(),
      updatedAt: db.fn.now()
    };

    await db('bom_material_usage').insert(newImageRecord);

    res.status(200).json({
      status: 'success',
      data: {
        id: imageId,
        image_url: imageUrl
      }
    });
  } catch (error) {
    console.error('上傳 BOM 材料用量圖片失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '上傳 BOM 材料用量圖片失敗: ' + error.message
    });
  }
};

// 刪除指定 BOM 的材料用量記錄
exports.deleteBomMaterialUsage = async (req, res) => {
  try {
    const { bomId, usageId } = req.params;

    // 檢查記錄是否存在且屬於該 BOM
    const usageExists = await db('bom_material_usage')
      .where({ id: usageId, bom_id: bomId })
      .first();

    if (!usageExists) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該材料用量記錄或記錄不屬於此 BOM'
      });
    }

    // 如果有圖片，嘗試刪除圖片文件
    if (usageExists.image_url && usageExists.image_url.includes('/bom_images/')) {
      try {
        const imgPath = path.join(__dirname, '..', usageExists.image_url);
        if (fs.existsSync(imgPath)) {
          fs.unlinkSync(imgPath);

        }
      } catch (unlinkError) {
        console.error('刪除材料用量圖片失敗:', unlinkError);
        // 不中止操作，但記錄錯誤
      }
    }

    // 刪除記錄
    await db('bom_material_usage').where({ id: usageId }).del();

    res.status(200).json({
      status: 'success',
      message: '材料用量記錄刪除成功'
    });
  } catch (error) {
    console.error('刪除 BOM 材料用量失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除 BOM 材料用量失敗: ' + error.message
    });
  }
};

// 創建新BOM
exports.createBom = async (req, res) => {
  // 在頂部聲明newBom變數，確保在catch區塊中可用
  let newBom = null;

  try {
    // 驗證使用者是否屬於台北部
    if (req.employee.department !== 0) {
      return;
    }

    // 檢查 productCode 是否為空
    if (!req.body.productCode) {
      return res.status(400).json({
        status: 'fail',
        message: 'BOM材料編號不能為空'
      });
    }

    // 移除 productCode 必填驗證

    // 處理圖片上傳（如果有）
    let picsPath = '';
    if (req.body.pics && req.body.pics.startsWith('data:image')) {
      const imgData = req.body.pics.split(';base64,').pop();
      const imgType = req.body.pics.split(';')[0].split('/')[1];
      const imgName = `${Date.now()}.${imgType}`;
      const imgPath = path.join(__dirname, '../public/uploads', imgName);

      // 確保目錄存在
      const uploadDir = path.join(__dirname, '../public/uploads');
      try {
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        fs.writeFileSync(imgPath, imgData, { encoding: 'base64' });
        picsPath = `/uploads/${imgName}`;
      } catch (writeError) {
        console.error('圖片寫入失敗:', writeError);
        // 可以選擇是否要因為圖片寫入失敗而中止操作
        // return res.status(500).json({ status: 'error', message: '圖片儲存失敗' });
        // 或者繼續執行，但 picsPath 會是空的
      }
    }

    // 創建新BOM
    newBom = {
      id: uuidv4(),
      productCode: req.body.productCode,
      category: req.body.category,
      trademark: req.body.trademark,
      unit: req.body.unit,
      version: req.body.version,
      cName: req.body.cName,
      eName: req.body.eName,
      cColor: req.body.cColor,
      eColor: req.body.eColor,
      cType: req.body.cType,
      eType: req.body.eType,
      format: req.body.format,
      barcode: req.body.barcode,
      pics: picsPath || req.body.pics, // Use saved path or original data if save failed
      hardwareColor: req.body.hardwareColor,
      format1: req.body.format1,
      format2: req.body.format2,
      aboutBom: req.body.aboutBom,
      productStatus: req.body.productStatus || '設計階段',
      // status: req.body.status || 0, // 暫時移除 status 欄位處理
      confirmationStatus: req.body.confirmationStatus || 0, // 確保確認狀態有預設值
      height: isNaN(parseFloat(req.body.height)) ? null : parseFloat(req.body.height),
      width: isNaN(parseFloat(req.body.width)) ? null : parseFloat(req.body.width),
      deep: isNaN(parseFloat(req.body.deep)) ? null : parseFloat(req.body.deep),
      weight: isNaN(parseFloat(req.body.weight)) ? null : parseFloat(req.body.weight),
      cm1: isNaN(parseFloat(req.body.cm1)) ? null : parseFloat(req.body.cm1),
      cm2: isNaN(parseFloat(req.body.cm2)) ? null : parseFloat(req.body.cm2),
      cm3: isNaN(parseFloat(req.body.cm3)) ? null : parseFloat(req.body.cm3)
    };

    // 檢查 productCode 是否已存在
    let existingBom = null;
    try {
      // 檢查 req.body.productCode 是否存在且為字串，再進行查詢
      if (req.body.productCode && typeof req.body.productCode === 'string') {
        existingBom = await db('bomVersion')
          .whereRaw('LOWER(`productCode`) = ?', [req.body.productCode.toLowerCase()])
          .first();
      } else {
        // 如果 productCode 無效或不存在，則視為新紀錄，避免 toLowerCase 錯誤
        existingBom = null;
      }
    } catch (dbError) {
      console.error('查詢材料編號時資料庫出錯:', dbError);
      // 如果查詢出錯，應該返回錯誤，避免後續邏輯問題
      return res.status(500).json({
        status: 'error',
        message: '查詢材料編號時資料庫出錯: ' + dbError.message
      });
    }

    if (existingBom) {
      // 如果 productCode 已存在，則更新現有資料
      await db('bomVersion').where({ productCode: req.body.productCode }).update({...newBom, createdAt: db.fn.now()});
      const updatedBom = await db('bomVersion').where({ productCode: req.body.productCode }).first();
      res.status(200).json({
        status: 'success',
        data: updatedBom
      });
    } else {
      // 如果 productCode 不存在，則插入新資料
      await db('bomVersion').insert(newBom);
      const createdBom = await db('bomVersion').where({ id: newBom.id }).first();
      res.status(201).json({
        status: 'success',
        data: createdBom
      });
    }
  } catch (error) {

    // 檢查錯誤是否包含 'Undefined binding(s) detected' 相關訊息
    if (error.message && error.message.includes('Undefined binding')) {
      // 嘗試檢查BOM是否已經創建成功，通過ID查詢
      try {
        if (newBom && newBom.id) {
          const createdBom = await db('bomVersion').where({ id: newBom.id }).first();
          if (createdBom) {
            return res.status(201).json({
              status: 'success',
              data: createdBom
            });
          }
        }
      } catch (secondaryError) {
        console.error('嘗試查詢創建的BOM失敗:', secondaryError);
      }
    }

    // 檢查數據是否已成功插入，如果是，返回成功響應
    if (newBom && newBom.id) {
      try {
        const createdBom = await db('bomVersion').where({ id: newBom.id }).first();
        if (createdBom) {
          console.log('雖然報錯，但BOM已成功創建:', createdBom.id);
          return res.status(201).json({
            status: 'success',
            data: createdBom
          });
        }
      } catch (checkError) {
        console.error('檢查BOM是否已創建時出錯:', checkError);
      }
    }

    res.status(500).json({

    });
  }
};

// 更新BOM
exports.updateBom = async (req, res) => {
  try {
    // 驗證使用者是否屬於台北部
    if (req.employee.department !== 0) {
      return;
    }

    const { id } = req.params;

    // 檢查 productCode 是否為空
    if (!req.body.productCode) {
      return res.status(400).json({
        status: 'fail',
        message: 'BOM材料編號不能為空'
      });
    }

    // 檢查BOM是否存在
    const existingBom = await db('bomVersion').where({ id }).first();
    if (!existingBom) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該BOM'
      });
    }

    // 處理圖片上傳（如果有）
    let picsPath = existingBom.pics;
    if (req.body.pics && req.body.pics.startsWith('data:image')) {
      const imgData = req.body.pics.split(';base64,').pop();
      const imgType = req.body.pics.split(';')[0].split('/')[1];
      const imgName = `${Date.now()}.${imgType}`;
      const imgPath = path.join(__dirname, '../public/uploads', imgName);

      // 確保目錄存在
      const uploadDir = path.join(__dirname, '../public/uploads');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      fs.writeFileSync(imgPath, imgData, { encoding: 'base64' });
      picsPath = `/uploads/${imgName}`;

      // 刪除舊圖片
      if (existingBom.pics && existingBom.pics.startsWith('/uploads/')) {
        const oldImgPath = path.join(__dirname, '../..', existingBom.pics); // 修正路徑 ../..
        try {
          if (fs.existsSync(oldImgPath)) {
            fs.unlinkSync(imgPath);

          }
        } catch (unlinkError) {
          console.error('刪除舊圖片失敗:', unlinkError);
          // 不中止操作，但記錄錯誤
        }
      }
    }

    // 更新BOM
    const updatedBom = {
      productCode: req.body.productCode,
      category: req.body.category,
      trademark: req.body.trademark,
      unit: req.body.unit,
      version: req.body.version,
      cName: req.body.cName,
      eName: req.body.eName,
      cColor: req.body.cColor,
      eColor: req.body.eColor,
      cType: req.body.cType,
      eType: req.body.eType,
      format: req.body.format,
      barcode: req.body.barcode,
      pics: picsPath, // Use updated path
      hardwareColor: req.body.hardwareColor,
      format1: req.body.format1,
      format2: req.body.format2,
      aboutBom: req.body.aboutBom,
      productStatus: req.body.productStatus, // 新增：更新 productStatus
      updatedAt: db.fn.now(),
      height: isNaN(parseFloat(req.body.height)) ? null : parseFloat(req.body.height),
      width: isNaN(parseFloat(req.body.width)) ? null : parseFloat(req.body.width),
      deep: isNaN(parseFloat(req.body.deep)) ? null : parseFloat(req.body.deep),
      weight: isNaN(parseFloat(req.body.weight)) ? null : parseFloat(req.body.weight),
      cm1: isNaN(parseFloat(req.body.cm1)) ? null : parseFloat(req.body.cm1),
      cm2: isNaN(parseFloat(req.body.cm2)) ? null : parseFloat(req.body.cm2),
      cm3: isNaN(parseFloat(req.body.cm3)) ? null : parseFloat(req.body.cm3)
    };

    await db('bomVersion').where({ id }).update(updatedBom);

    // 查詢更新後的 BOM 數據
    const updatedBomData = await db('bomVersion').where({ id }).first();

    res.status(200).json({
      status: 'success',
      data: updatedBomData
    });
  } catch (error) {
    console.error('更新BOM失敗:', error);

    // 嘗試檢查更新是否已生效
    try {
      const { id } = req.params;
      const updatedBom = await db('bomVersion').where({ id }).first();

      if (updatedBom) {
        console.log('雖然報錯，但BOM已成功更新:', updatedBom.id);
        return res.status(200).json({
          status: 'success',
          data: updatedBom
        });
      }
    } catch (checkError) {
      console.error('檢查BOM是否已更新時出錯:', checkError);
    }

    res.status(500).json({
      status: 'error',
      message: '更新BOM失敗: ' + error.message
    });
  }
}


// 刪除BOM
exports.deleteBom = async (req, res) => {
  try {
    // 驗證使用者是否屬於台北部
    if (req.employee.department !== 0) {
      return;
    }

    const { id } = req.params;

    // 檢查BOM是否存在
    const bom = await db('bomVersion').where({ id }).first();
    if (!bom) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該BOM'
      });
    }

    // 刪除圖片
    if (bom.pics && bom.pics.startsWith('/uploads/')) {
      const imgPath = path.join(__dirname, '../..', bom.pics); // 修正路徑 ../..
      try {
        if (fs.existsSync(imgPath)) {
          fs.unlinkSync(imgPath);

        }
      } catch (unlinkError) {
         console.error('刪除圖片失敗:', unlinkError);
         // 不中止操作，但記錄錯誤
      }
    }

    // 從數據庫刪除BOM
    await db('bomVersion').where({ id }).del();

    res.status(200).json({
      status: 'success',
      message: '刪除成功'
    });
  } catch (error) {
    console.error('刪除BOM失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '刪除BOM失敗: ' + error.message
    });
  }
};

// 搜尋BOM
exports.searchBoms = async (req, res) => {
  try {
    const { query } = req.query;

    let queryBuilder;

    if (!query) {
      // 修改：無搜尋詞時，直接查詢並排序
      queryBuilder = db('bomVersion').select('*').orderBy('createdAt', 'desc');
    } else {
      // 修改：有搜尋詞時，先建立查詢再排序
      queryBuilder = db('bomVersion')
        .where('productCode', 'like', `%${query}%`)
        .orWhere('cName', 'like', `%${query}%`)
        .orWhere('eName', 'like', `%${query}%`)
        .select('*')
        .orderBy('createdAt', 'desc'); // 修改：加入排序
    }

    const bomData = await queryBuilder; // 執行查詢

    res.status(200).json({
      status: 'success',
      data: bomData
    });
  } catch (error) {
    console.error('搜尋BOM失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '搜尋BOM失敗: ' + error.message
    });
  }
};

// 完成修改 BOM
exports.completeBom = async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查BOM是否存在
    const existingBom = await db('bomVersion').where({ id }).first();
    if (!existingBom) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該BOM'
      });
    }

    // 驗證使用者是否為管理員且部門為台北部(0)或廠務部(1)
    if (req.employee.role !== 0 || (req.employee.department !== 0 && req.employee.department !== 1)) {
      return res.status(403).json({
        status: 'fail',
        message: '只有台北部或廠務部管理員才能完成修改BOM'
      });
    }

    // 獲取當前確認狀態
    let currentStatus = Number(existingBom.confirmationStatus || 0);

    // 根據用戶部門更新狀態碼
    if (req.employee.department === 0) { // 台北部確認，狀態+1
      if (currentStatus === 0 || currentStatus === 2) {
        currentStatus += 1;
      }
    } else if (req.employee.department === 1) { // 廠務部確認，狀態+2
      if (currentStatus === 0 || currentStatus === 1) {
        currentStatus += 2;
      }
    }

    // 檢查是否狀態為3（完全確認），需要更新產品狀態和審核狀態
    let newProductStatus = existingBom.productStatus;
    let newStatus = existingBom.status;

    // 如果雙方都確認（狀態為3）
    if (currentStatus === 3) {
      newProductStatus = 'PPS'; // 將屬性改為PPS
      newStatus = '完成確認'; // 將狀態改為完成確認
    }

    // 更新BOM
    const updatedData = {
      confirmationStatus: currentStatus,
      productStatus: newProductStatus,
      status: newStatus,
      updatedAt: db.fn.now()
    };

    await db('bomVersion').where({ id }).update(updatedData);

    // 查詢更新後的 BOM 數據
    const updatedBom = await db('bomVersion').where({ id }).first();

    res.status(200).json({
      status: 'success',
      data: updatedBom
    });
  } catch (error) {
    console.error('完成修改BOM失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '完成修改BOM失敗: ' + error.message
    });
  }
};

// 作廢BOM
exports.invalidateBom = async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查BOM是否存在
    const existingBom = await db('bomVersion').where({ id }).first();
    if (!existingBom) {
      return res.status(404).json({
        status: 'fail',
        message: '找不到該BOM'
      });
    }

    // 驗證使用者是否屬於台北部
    if (req.employee.department !== 0) {
      return;
    }

    // 更新BOM狀態
    const updatedData = {
      status: '已作廢',
      updatedAt: db.fn.now()
    };

    await db('bomVersion').where({ id }).update(updatedData);

    // 查詢更新後的 BOM 數據
    const updatedBom = await db('bomVersion').where({ id }).first();

    res.status(200).json({
      status: 'success',
      data: updatedBom
    });
  } catch (error) {
    console.error('作廢BOM失敗:', error);
    res.status(500).json({
      status: 'error',
      message: '作廢BOM失敗: ' + error.message
    });
  }
};
