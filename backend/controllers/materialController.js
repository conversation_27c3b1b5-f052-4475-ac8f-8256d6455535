const Material = require('../models/Material');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');

// 獲取所有原物料
const getAllMaterials = async (req, res) => {
  try {
    const { search, category } = req.query;
    const materials = await Material.getAllMaterials(search, category);
    res.json(materials);
  } catch (error) {
    console.error('獲取原物料列表失敗:', error);
    res.status(500).json({ message: '獲取原物料列表失敗', error: error.message });
  }
};

// 獲取單個原物料
const getMaterialById = async (req, res) => {
  try {
    const { id } = req.params;
    const material = await Material.getMaterialById(id);

    if (!material) {
      return res.status(404).json({ message: '找不到該原物料' });
    }

    res.json(material);
  } catch (error) {
    console.error('獲取原物料失敗:', error);
    res.status(500).json({ message: '獲取原物料失敗', error: error.message });
  }
};

// 創建原物料
const createMaterial = async (req, res) => {
  try {
    const materialData = req.body;

    // 驗證材料編號是否存在
    if (!materialData.code) {
      return res.status(400).json({ message: '材料編號為必填欄位' });
    }

    // 確保台北部和廠務部數量為數字且不是 NaN
    materialData.taipei_quantity = Number(materialData.taipei_quantity) || 0;
    materialData.factory_quantity = Number(materialData.factory_quantity) || 0;

    // 四捨五入到兩位小數
    materialData.taipei_quantity = parseFloat(materialData.taipei_quantity.toFixed(2));
    materialData.factory_quantity = parseFloat(materialData.factory_quantity.toFixed(2));

    // 處理圖片上傳（如果有）
    if (materialData.image && materialData.image.startsWith('data:image')) {
      // 創建分類目錄
      const typeFolder = materialData.type || 'other';
      const uploadDir = path.join(__dirname, '../database/uploads/materials', typeFolder);

      // 確保目錄存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 處理 base64 圖片數據
      const imgData = materialData.image.split(';base64,').pop();
      const imgType = materialData.image.split(';')[0].split('/')[1];
      const imgName = `${materialData.code}_${Date.now()}.${imgType}`;
      const imgPath = path.join(uploadDir, imgName);

      // 寫入檔案
      fs.writeFileSync(imgPath, imgData, { encoding: 'base64' });

      // 設置圖片URL - 使用相對於 API 的路徑
      materialData.image_url = `/database/uploads/materials/${typeFolder}/${imgName}`;
      console.log('圖片URL設置為:', materialData.image_url);
    }

    // 移除原始圖片數據，不存入數據庫
    delete materialData.image;

    // 檢查是否已存在相同材料編號的原物料
    const existingMaterials = await Material.getAllMaterials();

    // 先檢查是否存在相同材料編號的原物料
    const existingMaterialWithSameCode = existingMaterials.find(material =>
      material.code && materialData.code && material.code === materialData.code
    );

    // 如果存在相同材料編號的原物料
    if (existingMaterialWithSameCode) {
      // 取得現有原物料的數量
      const existingMaterial = await Material.getMaterialById(existingMaterialWithSameCode.id);

      // 計算新的台北部數量，累加而不是覆蓋
      const taipeiQuantity = Number(existingMaterial.taipei_stock_quantity || 0) +
                            Number(materialData.taipei_quantity || 0);

      // 計算新的廠務部數量，累加而不是覆蓋
      const factoryQuantity = Number(existingMaterial.factory_stock_quantity || 0) +
                             Number(materialData.factory_quantity || 0);

      // 更新原物料數量
      const updateData = {
        taipei_stock_quantity: taipeiQuantity,
        taipei_available_quantity: taipeiQuantity,
        factory_stock_quantity: factoryQuantity,
        factory_available_quantity: factoryQuantity
      };

      // 更新現有原物料
      await Material.updateMaterialDirectly(existingMaterialWithSameCode.id, updateData);

      // 重新獲取更新後的原物料
      const updatedMaterial = await Material.getMaterialById(existingMaterialWithSameCode.id);

      // 安全地返回JSON數據
      res.json({
        id: updatedMaterial.id,
        message: '原物料數量更新成功'
      });
    } else {
      // 如果不存在相同材料編號的原物料，創建新原物料
      const newMaterial = await Material.createMaterial(materialData);

      // 安全地返回JSON數據
      res.status(201).json({
        id: newMaterial.id,
        message: '原物料創建成功'
      });
    }
  } catch (error) {
    console.error('創建原物料失敗:', error);
    res.status(500).json({ message: '創建原物料失敗', error: error.message });
  }
};

// 更新原物料
const updateMaterial = async (req, res) => {
  try {
    const { id } = req.params;
    const materialData = req.body;

    // 檢查原物料是否存在
    const existingMaterial = await Material.getMaterialById(id);
    if (!existingMaterial) {
      return res.status(404).json({ message: '找不到該原物料' });
    }

    // 確保台北部和廠務部數量為數字且不是 NaN
    if (materialData.taipei_quantity !== undefined) {
      materialData.taipei_quantity = Number(materialData.taipei_quantity) || 0;
      materialData.taipei_quantity = parseFloat(materialData.taipei_quantity.toFixed(2));
    }

    if (materialData.factory_quantity !== undefined) {
      materialData.factory_quantity = Number(materialData.factory_quantity) || 0;
      materialData.factory_quantity = parseFloat(materialData.factory_quantity.toFixed(2));
    }

    // 處理圖片上傳（如果有）
    if (materialData.image && materialData.image.startsWith('data:image')) {
      // 創建分類目錄
      const typeFolder = materialData.type || 'other';
      const uploadDir = path.join(__dirname, '../database/uploads/materials', typeFolder);

      // 確保目錄存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      // 處理 base64 圖片數據
      const imgData = materialData.image.split(';base64,').pop();
      const imgType = materialData.image.split(';')[0].split('/')[1];
      const imgName = `${materialData.code}_${Date.now()}.${imgType}`;
      const imgPath = path.join(uploadDir, imgName);

      // 寫入檔案
      fs.writeFileSync(imgPath, imgData, { encoding: 'base64' });

      // 設置圖片URL
      materialData.image_url = `/database/uploads/materials/${typeFolder}/${imgName}`;
      console.log('更新圖片URL設置為:', materialData.image_url);

      // 刪除舊圖片（如果有）
      if (existingMaterial.image_url) {
        try {
          const oldImgPath = path.join(__dirname, '..', existingMaterial.image_url.startsWith('/') ? '.' : '', existingMaterial.image_url);
          if (fs.existsSync(oldImgPath)) {
            fs.unlinkSync(oldImgPath);
            console.log('已刪除舊圖片:', oldImgPath);
          }
        } catch (unlinkError) {
          console.error('刪除舊圖片失敗:', unlinkError);
          // 不中止操作，但記錄錯誤
        }
      }
    }

    // 移除原始圖片數據，不存入數據庫
    delete materialData.image;

    // 更新原物料
    await Material.updateMaterial(id, materialData);

    // 安全地返回JSON數據
    res.json({
      id: id,
      message: '原物料更新成功'
    });
  } catch (error) {
    console.error('更新原物料失敗:', error);
    res.status(500).json({ message: '更新原物料失敗', error: error.message });
  }
};

// 刪除原物料
const deleteMaterial = async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查原物料是否存在
    const existingMaterial = await Material.getMaterialById(id);
    if (!existingMaterial) {
      return res.status(404).json({ message: '找不到該原物料' });
    }

    // 刪除圖片（如果有）
    if (existingMaterial.image_url) {
      try {
        const imgPath = path.join(__dirname, '..', existingMaterial.image_url.startsWith('/') ? '.' : '', existingMaterial.image_url);
        if (fs.existsSync(imgPath)) {
          fs.unlinkSync(imgPath);
          console.log('已刪除圖片:', imgPath);
        }
      } catch (unlinkError) {
        console.error('刪除圖片失敗:', unlinkError);
        // 不中止操作，但記錄錯誤
      }
    }

    await Material.deleteMaterial(id);
    res.json({ message: '原物料已成功刪除' });
  } catch (error) {
    console.error('刪除原物料失敗:', error);
    res.status(500).json({ message: '刪除原物料失敗', error: error.message });
  }
};

// 創建原物料交易記錄 (領用或入庫)
const createMaterialTransaction = async (req, res) => {
  try {
    const transactionData = req.body;

    // 驗證必填字段
    if (!transactionData.material_id) {
      return res.status(400).json({ message: '原物料ID為必填欄位' });
    }

    // 允許數量為 0，但不能為負數
    if (transactionData.quantity === undefined || parseFloat(transactionData.quantity) < 0) {
      return res.status(400).json({ message: '數量不能為負數' });
    }

    if (!transactionData.transaction_type) {
      return res.status(400).json({ message: '交易類型為必填欄位' });
    }

    if (!['入庫', '領用'].includes(transactionData.transaction_type)) {
      return res.status(400).json({ message: '交易類型必須為「入庫」或「領用」' });
    }

    // 如果是領用，需要驗證申請人和部門
    if (transactionData.transaction_type === '領用') {
      if (!transactionData.requester) {
        return res.status(400).json({ message: '申請人為必填欄位' });
      }

      if (!transactionData.department) {
        return res.status(400).json({ message: '領用部門為必填欄位' });
      }
    }

    // 檢查原物料是否存在
    const material = await Material.getMaterialById(transactionData.material_id);
    if (!material) {
      return res.status(404).json({ message: '找不到該原物料' });
    }

    // 如果是領用，檢查可用數量是否足夠
    if (transactionData.transaction_type === '領用') {
      // 根據倉庫位置檢查可用數量
      const storageLocation = transactionData.storage_location || '總倉庫';
      let availableQuantity = 0;

      if (storageLocation === '台北部' || storageLocation === '總倉庫') {
        availableQuantity = material.taipei_available_quantity || 0;
      } else if (storageLocation === '廠務部A') {
        availableQuantity = material.factory_a_available_quantity || 0;
      } else if (storageLocation === '廠務部B') {
        availableQuantity = material.factory_b_available_quantity || 0;
      } else if (storageLocation === '廠務部C') {
        availableQuantity = material.factory_c_available_quantity || 0;
      } else if (storageLocation === '廠務部D') {
        availableQuantity = material.factory_d_available_quantity || 0;
      } else if (storageLocation === '廠務部E') {
        availableQuantity = material.factory_e_available_quantity || 0;
      } else if (storageLocation === '總倉庫') {
        availableQuantity = (material.taipei_available_quantity || 0) + (material.factory_available_quantity || 0);
      }

      if (availableQuantity < parseFloat(transactionData.quantity)) {
        return res.status(400).json({
          message: '可用數量不足',
          available: availableQuantity
        });
      }
    }

    // 添加創建者信息（如果有認證）
    if (req.user) {
      transactionData.created_by = req.user.id;
      transactionData.created_by_name = req.user.name;
    }

    // 創建交易記錄
    const transaction = await Material.createMaterialTransaction(transactionData);

    // 返回成功響應
    res.status(201).json(transaction);
  } catch (error) {
    console.error('創建原物料交易記錄失敗:', error);
    res.status(500).json({ message: '創建原物料交易記錄失敗', error: error.message });
  }
};

// 獲取原物料交易記錄
const getMaterialTransactions = async (req, res) => {
  try {
    const filters = req.query;
    const transactions = await Material.getMaterialTransactions(filters);
    res.json(transactions);
  } catch (error) {
    console.error('獲取原物料交易記錄失敗:', error);
    res.status(500).json({ message: '獲取原物料交易記錄失敗', error: error.message });
  }
};

// 獲取指定部門的原物料
const getMaterialsByDepartment = async (req, res) => {
  try {
    const { department } = req.params;
    const { search } = req.query;

    if (!department) {
      return res.status(400).json({ message: '部門為必填欄位' });
    }

    const materials = await Material.getMaterialsByDepartment(department, search);
    res.json(materials);
  } catch (error) {
    console.error('獲取部門原物料列表失敗:', error);
    res.status(500).json({ message: '獲取部門原物料列表失敗', error: error.message });
  }
};

// 向後兼容 - 創建原物料領用記錄 (重定向到 transaction)
const createMaterialUsage = async (req, res) => {
  try {
    const usageData = req.body;

    // 將領用數據轉換為交易數據格式
    const transactionData = {
      material_id: usageData.material_id,
      quantity: usageData.quantity,
      transaction_type: '領用',
      requester: usageData.requester,
      department: usageData.department,
      purpose: usageData.purpose,
      notes: usageData.notes,
      created_by: req.user ? req.user.id : undefined,
      created_by_name: req.user ? req.user.name : undefined
    };

    // 使用交易記錄創建功能
    const result = await createMaterialTransaction(
      { body: transactionData, user: req.user },
      res
    );

    return result;
  } catch (error) {
    console.error('創建原物料領用記錄失敗:', error);
    res.status(500).json({ message: '創建原物料領用記錄失敗', error: error.message });
  }
};

// 向後兼容 - 獲取領用記錄 (重定向到 transactions，並增加領用類型過濾)
const getMaterialUsage = async (req, res) => {
  try {
    // 複製原始查詢參數並添加交易類型為領用
    const filters = { ...req.query, transaction_type: '領用' };

    // 獲取原物料交易記錄
    const transactions = await Material.getMaterialTransactions(filters);

    res.json(transactions);
  } catch (error) {
    console.error('獲取原物料領用記錄失敗:', error);
    res.status(500).json({ message: '獲取原物料領用記錄失敗', error: error.message });
  }
};

// 創建原物料調撥記錄
const createMaterialTransfer = async (req, res) => {
  try {
    const transferData = req.body;

    // 驗證必填字段
    if (!transferData.material_id) {
      return res.status(400).json({ message: '原物料ID為必填欄位' });
    }

    // 檢查調撥數量
    if (transferData.quantity === undefined || parseFloat(transferData.quantity) <= 0) {
      return res.status(400).json({ message: '調撥數量必須大於0' });
    }

    // 驗證倉庫
    if (!transferData.from_warehouse) {
      return res.status(400).json({ message: '調出倉庫為必填欄位' });
    }

    if (!transferData.to_warehouse) {
      return res.status(400).json({ message: '調入倉庫為必填欄位' });
    }

    if (transferData.from_warehouse === transferData.to_warehouse) {
      return res.status(400).json({ message: '調出倉庫和調入倉庫不能相同' });
    }

    // 驗證申請人
    if (!transferData.requester) {
      return res.status(400).json({ message: '申請人為必填欄位' });
    }

    // 檢查原物料是否存在
    const material = await Material.getMaterialById(transferData.material_id);
    if (!material) {
      return res.status(404).json({ message: '找不到該原物料' });
    }

    // 添加創建者信息（如果有認證）
    if (req.user) {
      transferData.created_by = req.user.id;
      transferData.created_by_name = req.user.name;
    }

    // 創建調撥記錄
    const transfer = await Material.createMaterialTransfer(transferData);

    // 返回成功響應
    res.status(201).json(transfer);
  } catch (error) {
    console.error('創建原物料調撥記錄失敗:', error);
    res.status(500).json({ message: '創建原物料調撥記錄失敗', error: error.message });
  }
};

module.exports = {
  getAllMaterials,
  getMaterialById,
  createMaterial,
  updateMaterial,
  deleteMaterial,
  createMaterialTransaction,
  getMaterialTransactions,
  getMaterialsByDepartment,
  createMaterialUsage,
  getMaterialUsage,
  createMaterialTransfer
};
