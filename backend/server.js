const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 載入環境變數
dotenv.config();

// 載入數據庫連接
const db = require('./database/db');

// 初始化數據庫表
const { initTables } = require('./database/tables');

const app = express();

// 中間件
app.use(cors());
// 增加 JSON 請求體的大小限制
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true })); // 同樣增加 urlencoded 的限制

// 靜態文件
app.use(express.static(path.join(__dirname, 'public')));

// 添加對 database/uploads 目錄的靜態文件服務
app.use('/api/database/uploads', express.static(path.join(__dirname, 'database/uploads')));

// 為了兼容性，保留原始路徑
app.use('/database/uploads', express.static(path.join(__dirname, 'database/uploads')));

// 引入路由
const authRoutes = require('./routes/auth');
const bomRoutes = require('./routes/bom');
const employeeRoutes = require('./routes/employee');
const announcementRoutes = require('./routes/announcement');
const settingRecordRoutes = require('./routes/settingRecord');
const materialRoutes = require('./routes/material');
const processPreparationRoutes = require('./routes/processPreparation');
const materialDetailSheetRoutes = require('./routes/materialDetailSheet');
const processHistoryRoutes = require('./routes/processHistory');
// 皮料路由已刪除
const leatherMaterialRoutes = require('./routes/leatherMaterial');

// 使用路由
app.use('/api/auth', authRoutes);
app.use('/api/bom', bomRoutes);
app.use('/api/employee', employeeRoutes);
app.use('/api/announcement', announcementRoutes);
app.use('/api/settingRecord', settingRecordRoutes);
app.use('/api/inventory', materialRoutes);
app.use('/api/process-preparation', processPreparationRoutes);
app.use('/api/material-detail-sheet', materialDetailSheetRoutes);
app.use('/api/process-history', processHistoryRoutes);
// 皮料路由已刪除
app.use('/api/inventory', leatherMaterialRoutes);

// 測試路由，用於檢查靜態文件服務是否正常工作
app.get('/api/test-static', (req, res) => {
  // 檢查目錄是否存在
  const publicPath = path.join(__dirname, 'public');
  const databasePath = path.join(__dirname, 'database/uploads');
  const publicExists = fs.existsSync(publicPath);
  const databaseExists = fs.existsSync(databasePath);

  // 列出 database/uploads 目錄中的文件
  let uploadFiles = [];
  if (databaseExists) {
    try {
      const readDir = (dir) => {
        const files = fs.readdirSync(dir, { withFileTypes: true });
        files.forEach(file => {
          const fullPath = path.join(dir, file.name);
          if (file.isDirectory()) {
            readDir(fullPath);
          } else {
            uploadFiles.push(fullPath.replace(__dirname, ''));
          }
        });
      };
      readDir(databasePath);
    } catch (error) {
      console.error('讀取目錄失敗:', error);
    }
  }

  res.json({
    status: 'success',
    message: '靜態文件服務正常工作',
    paths: {
      public: publicPath,
      database: databasePath
    },
    exists: {
      public: publicExists,
      database: databaseExists
    },
    uploadFiles: uploadFiles,
    routes: {
      api_database: '/api/database/uploads',
      database: '/database/uploads'
    }
  });
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    status: 'error',
    message: '伺服器內部錯誤'
  });
});

// 啟動伺服器
const PORT = process.env.PORT || 3000;

// 啟動前確保數據庫表已初始化
async function startServer() {
  try {
    // 確保數據庫表已初始化
    await initTables();

    app.listen(PORT, () => {
      console.log(`伺服器運行於 http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('啟動服務器失敗:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
