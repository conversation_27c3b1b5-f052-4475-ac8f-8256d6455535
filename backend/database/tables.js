const db = require('./db');
const { v4: uuidv4 } = require('uuid');

// 初始化數據庫表
async function initTables() {
  try {
    console.log('檢查數據庫表...');

    // 檢查並創建 settingRecord 表
    const settingRecordTableExists = await db.schema.hasTable('settingRecord');
    if (!settingRecordTableExists) {
      console.log('創建 settingRecord 表...');
      await db.schema.createTable('settingRecord', table => {
        table.string('id').primary();
        table.string('type').notNullable();
        table.string('name').notNullable();
        table.string('value').notNullable();
        table.string('description');
        table.integer('sort_order').defaultTo(0);
        table.boolean('is_active').defaultTo(true);
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['type', 'sort_order']);
      });
      console.log('settingRecord 表創建成功');

      // 添加預設的設定記錄
      await initDefaultSettings();
    }

    // 檢查並創建 materials 表 (原物料庫存)
    const materialsTableExists = await db.schema.hasTable('materials');
    if (!materialsTableExists) {
      console.log('創建 materials 表 (原物料庫存)...');
      await db.schema.createTable('materials', table => {
        table.string('id').primary();
        table.string('code').comment('材料編號');
        table.string('name').notNullable().comment('名稱');
        table.string('type').notNullable().comment('類型');
        table.string('category').notNullable().comment('分類');
        table.string('specification').comment('規格');
        table.string('unit').comment('單位'); // 新增單位欄位
        table.string('color').comment('顏色');
        table.string('storage_location').notNullable().comment('倉別');
        table.text('description').comment('描述');
        table.boolean('is_active').defaultTo(true).comment('是否啟用');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['code']);
        table.index(['type', 'category']);
      });
      console.log('materials 表創建成功');
    }

    // 檢查並創建 material_transactions 表 (原物料交易記錄)
    const materialTransactionsTableExists = await db.schema.hasTable('material_transactions');
    if (!materialTransactionsTableExists) {
      console.log('創建 material_transactions 表 (原物料交易記錄)...');
      await db.schema.createTable('material_transactions', table => {
        table.string('id').primary();
        table.string('material_id').notNullable().comment('原物料ID');
        table.string('material_code').comment('原物料代號');
        table.string('material_name').notNullable().comment('原物料名稱');
        table.float('quantity').notNullable().comment('數量');
        table.string('transaction_type').notNullable().comment('交易類型: 入庫 | 領用');
        table.string('requester').comment('申請人');
        table.string('department').comment('部門');
        table.string('purpose').comment('用途');
        table.string('storage_location').comment('倉別');
        table.text('notes').comment('備註');
        table.string('created_by').comment('創建人ID');
        table.string('created_by_name').comment('創建人姓名');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['material_id']);
        table.index(['transaction_type']);
        table.index(['department']);
        table.index(['createdAt']);
      });
      console.log('material_transactions 表創建成功');
    }

    // 檢查並創建 process_preparation 表 (備料工序)
    const processPreparationTableExists = await db.schema.hasTable('process_preparation');
    if (!processPreparationTableExists) {
      console.log('創建 process_preparation 表 (備料工序)...');
      await db.schema.createTable('process_preparation', table => {
        table.string('id').primary();
        table.string('bomId').notNullable().comment('關聯的BOM ID');
        table.string('processCode').notNullable().comment('工序序號');
        table.string('majorProcess').notNullable().comment('大工序');
        table.string('minorProcess').notNullable().comment('小工序');
        table.string('sequenceNumber').notNullable().comment('產生後序號');
        table.string('pieceGroup').notNullable().comment('部位組織');
        table.string('pieceDetail').notNullable().comment('分片組織');
        table.string('pieceName').notNullable().comment('分片名稱');
        table.string('material').comment('材料');
        table.float('quantity').defaultTo(1).comment('組成數量');
        table.string('tool').comment('工具');
        table.string('consumable').comment('耗材');
        table.text('processDescription').comment('加工說明');
        table.float('standardTime').defaultTo(0).comment('標準工時');
        table.float('actualTime').defaultTo(0).comment('實際工時');
        table.string('order').notNullable().comment('順序');
        table.string('created_by').comment('創建人ID');
        table.string('created_by_name').comment('創建人姓名');
        table.string('sourcePosition').comment('來源位置');
        table.string('sourceProcessId').comment('來源工序ID');
        table.boolean('isCompleted').defaultTo(false).comment('是否完成');
        table.boolean('isFixed').defaultTo(false).comment('是否固定');
        table.text('originalSources').comment('原始來源');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['bomId']);
        table.index(['majorProcess', 'order']);
      });
      console.log('process_preparation 表創建成功');
    }

    // 檢查並創建 process_tools 表 (工序工具清單)
    const processToolsTableExists = await db.schema.hasTable('process_tools');
    if (!processToolsTableExists) {
      console.log('創建 process_tools 表 (工序工具清單)...');
      await db.schema.createTable('process_tools', table => {
        table.string('id').primary();
        table.string('processKey').notNullable().comment('工序鍵值 (例如: A選料-01)');
        table.string('toolName').notNullable().comment('工具名稱');
        table.integer('sort_order').defaultTo(0).comment('排序');
        table.boolean('is_active').defaultTo(true).comment('是否啟用');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['processKey']);
      });
      console.log('process_tools 表創建成功');
    }

    // 檢查並創建 process_consumables 表 (工序耗材清單)
    const processConsumablesTableExists = await db.schema.hasTable('process_consumables');
    if (!processConsumablesTableExists) {
      console.log('創建 process_consumables 表 (工序耗材清單)...');
      await db.schema.createTable('process_consumables', table => {
        table.string('id').primary();
        table.string('processKey').notNullable().comment('工序鍵值 (例如: A選料-01)');
        table.string('consumableName').notNullable().comment('耗材名稱');
        table.integer('sort_order').defaultTo(0).comment('排序');
        table.boolean('is_active').defaultTo(true).comment('是否啟用');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['processKey']);
      });
      console.log('process_consumables 表創建成功');
    }

    // 檢查並創建 bom_material_usage 表 (BOM 材料用量)
    const bomMaterialUsageTableExists = await db.schema.hasTable('bom_material_usage');
    if (!bomMaterialUsageTableExists) {
      console.log('創建 bom_material_usage 表 (BOM 材料用量)...');
      await db.schema.createTable('bom_material_usage', table => {
        table.string('id').primary();
        table.string('bom_id').notNullable().comment('關聯的 BOM ID');
        table.string('material_type').notNullable().comment('材料類型');
        table.string('material_name').notNullable().comment('材料名稱');
        table.float('quantity').notNullable().comment('數量');
        table.string('unit').notNullable().comment('單位');
        table.text('remark').comment('備註');
        table.string('inventory_id').nullable().comment('關聯的庫存原物料 ID');
        table.string('code').nullable().comment('庫存原物料代號 (冗餘)');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加外鍵約束 (可選，但建議)
        // table.foreign('bom_id').references('id').inTable('bomVersion').onDelete('CASCADE');
        // table.foreign('inventory_id').references('id').inTable('materials').onDelete('SET NULL');

        // 添加索引
        table.index(['bom_id']);
        table.index(['inventory_id']);
      });
      console.log('bom_material_usage 表創建成功');
    }

    // 檢查並創建 material_detail_sheets 表 (產品用料明細表)
    const materialDetailSheetsTableExists = await db.schema.hasTable('material_detail_sheets');
    if (!materialDetailSheetsTableExists) {
      console.log('創建 material_detail_sheets 表 (產品用料明細表)...');
      await db.schema.createTable('material_detail_sheets', table => {
        table.string('id').primary();
        table.string('bom_id').notNullable().comment('關聯的 BOM ID');
        table.string('material_id').notNullable().comment('關聯的材料用量 ID');
        table.string('sheet_type').notNullable().comment('明細表類型: 主料 | 副料');
        table.string('number').nullable().comment('序號');
        table.string('position_name').nullable().comment('部位名稱');
        table.string('material_name').nullable().comment('材料名稱');
        table.string('material_code').nullable().comment('料號');
        table.float('width').nullable().comment('寬度(cm)');
        table.float('height').nullable().comment('高度(cm)');
        table.float('area').nullable().comment('面積(cm²)');
        table.float('quantity').defaultTo(1).comment('組成數量');
        table.float('total_area').nullable().comment('分片面積(cm²)');
        table.boolean('is_split').defaultTo(false).comment('是否分片');
        table.float('grand_total').nullable().comment('總合(cm²)');
        table.string('created_by').nullable().comment('創建人ID');
        table.string('created_by_name').nullable().comment('創建人姓名');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['bom_id']);
        table.index(['material_id']);
        table.index(['sheet_type']);
        table.index(['createdAt']);
      });
      console.log('material_detail_sheets 表創建成功');
    }

    // 檢查並創建 material_detail_sheet_headers 表 (產品用料明細表頭)
    const materialDetailSheetHeadersTableExists = await db.schema.hasTable('material_detail_sheet_headers');
    if (!materialDetailSheetHeadersTableExists) {
      console.log('創建 material_detail_sheet_headers 表 (產品用料明細表頭)...');
      await db.schema.createTable('material_detail_sheet_headers', table => {
        table.string('id').primary();
        table.string('bom_id').notNullable().comment('關聯的 BOM ID');
        table.string('material_id').notNullable().comment('關聯的材料用量 ID');
        table.string('sheet_type').notNullable().comment('明細表類型: 主料 | 副料');
        table.json('details').comment('明細項目 JSON 數據');
        table.float('total_area').nullable().comment('總面積(cm²)');
        table.string('created_by').nullable().comment('創建人ID');
        table.string('created_by_name').nullable().comment('創建人姓名');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['bom_id']);
        table.index(['material_id']);
        table.index(['sheet_type']);
      });
      console.log('material_detail_sheet_headers 表創建成功');
    }

    // 檢查並創建 leather_materials 表 (皮料特殊資料)
    const leatherMaterialsTableExists = await db.schema.hasTable('leather_materials');
    if (!leatherMaterialsTableExists) {
      console.log('創建 leather_materials 表 (皮料特殊資料)...');
      await db.schema.createTable('leather_materials', table => {
        table.string('id').primary();
        table.string('material_id').notNullable().comment('關聯的原物料ID');
        table.string('material_code').comment('原物料編號');
        table.string('material_name').comment('原物料名稱');
        table.string('warehouse').notNullable().comment('存放倉庫');
        table.float('area').notNullable().comment('皮料面積(m2)');
        table.integer('quantity').notNullable().defaultTo(1).comment('數量');
        table.string('leather_code').comment('皮料編碼');
        table.string('transaction_id').comment('關聯的交易記錄ID');
        table.string('created_by').comment('創建人ID');
        table.string('created_by_name').comment('創建人姓名');
        table.text('notes').comment('備註');
        table.timestamp('createdAt').defaultTo(db.fn.now());
        table.timestamp('updatedAt').defaultTo(db.fn.now());

        // 添加索引
        table.index(['material_id']);
        table.index(['warehouse']);
        table.index(['leather_code']);
        table.index(['createdAt']);
      });
      console.log('leather_materials 表創建成功');
    }

    // 檢查並創建 process_history 表 (工序編輯歷史記錄)
    const processHistoryTableExists = await db.schema.hasTable('process_history');
    if (!processHistoryTableExists) {
      console.log('創建 process_history 表 (工序編輯歷史記錄)...');
      await db.schema.createTable('process_history', table => {
        table.string('id').primary();
        table.string('bom_id').notNullable().comment('關聯的 BOM ID');
        table.string('user_id').comment('使用者ID');
        table.string('user_name').comment('使用者姓名');
        table.text('description').notNullable().comment('操作描述');
        table.json('state_data').notNullable().comment('完整的狀態數據 JSON');
        table.timestamp('created_at').defaultTo(db.fn.now()).comment('創建時間');

        // 添加索引
        table.index(['bom_id']);
        table.index(['user_id']);
        table.index(['created_at']);
      });
      console.log('process_history 表創建成功');
    }

    console.log('數據庫表檢查完成');
  } catch (error) {
    console.error('初始化數據庫表時出錯:', error);
  }
}

// 初始化默認設定
async function initDefaultSettings() {
  try {
    console.log('添加默認設定...');

    // 添加默認的五金顏色設定
    const hardwareColors = [
      { name: '金色', value: '金色', sort_order: 0 },
      { name: '銀色', value: '銀色', sort_order: 1 },
      { name: '黑色', value: '黑色', sort_order: 2 },
      { name: '其他', value: '其他', sort_order: 3 }
    ];

    for (const color of hardwareColors) {
      await db('settingRecord').insert({
        id: uuidv4(),
        type: 'hardwareColor',
        name: color.name,
        value: color.value,
        description: '五金顏色',
        sort_order: color.sort_order,
        is_active: true,
        createdAt: db.fn.now(),
        updatedAt: db.fn.now()
      });
    }

    console.log('默認設定添加完成');
  } catch (error) {
    console.error('初始化默認設定時出錯:', error);
  }
}

module.exports = { initTables };
