const path = require('path');
const fs = require('fs');
const { initTables } = require('./tables');

// 初始化資料庫
async function initDatabase() {
  try {
    console.log('開始初始化資料庫...');

    // 創建uploads目錄
    const uploadsDir = path.join(__dirname, '../public/uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('創建上傳目錄:', uploadsDir);
    }
    
    // 初始化數據庫表
    await initTables();
    
    console.log('資料庫初始化完成！您可以開始使用系統了。');
  } catch (error) {
    console.error('初始化資料庫時出錯:', error);
    process.exit(1);
  }
}

// 執行初始化
initDatabase(); 