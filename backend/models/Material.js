const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');


// 獲取皮料詳情並計算總面積和總數量
const getLeatherDetails = async (materialId) => {
  try {
    // 從 leather_materials 表中獲取皮料詳情
    const leatherDetails = await db('leather_materials')
      .where({ material_id: materialId, is_active: true })
      .select('*');

    // 計算總面積和總數量
    let totalArea = 0;
    let totalQuantity = 0;

    leatherDetails.forEach(detail => {
      // 面積單位是 m²，數量是張數
      const area = Number(detail.area) || 0;
      const quantity = Number(detail.quantity) || 0;

      // 累加面積 * 數量
      totalArea += area * quantity;
      totalQuantity += quantity;
    });

    return {
      details: leatherDetails,
      totalArea,
      totalQuantity
    };
  } catch (error) {
    console.error('獲取皮料詳情失敗:', error);
    return { details: [], totalArea: 0, totalQuantity: 0 };
  }
};

// 獲取所有原物料
const getAllMaterials = async (search = '', category = '') => {
  try {
    let query = db('materials').where('is_active', true);

    // 如果提供了搜索關鍵字
    if (search) {
      query = query.where(builder => {
        builder.where('code', 'like', `%${search}%`)
          .orWhere('name', 'like', `%${search}%`);
      });
    }

    // 如果提供了類別篩選
    if (category) {
      query = query.where('category', category);
    }

    // 獲取原物料列表
    const materials = await query.orderBy('createdAt', 'desc');

    // 處理皮料類別的原物料，添加面積和數量信息
    for (const material of materials) {
      if (material.category === '皮料') {
        // 獲取皮料詳情並計算總面積和總數量
        const { totalArea, totalQuantity } = await getLeatherDetails(material.id);

        // 將計算結果添加到原物料對象中
        material.leather_total_area = totalArea;
        material.leather_total_quantity = totalQuantity;
      }
    }

    return materials;
  } catch (error) {
    console.error('獲取原物料列表失敗:', error);
    throw error;
  }
};

// 通過ID獲取單個原物料
const getMaterialById = async (id) => {
  try {
    const material = await db('materials')
      .where({ id, is_active: true })
      .first();

    // 如果是皮料類別，添加皮料詳情信息
    if (material && material.category === '皮料') {
      // 獲取皮料詳情並計算總面積和總數量
      const { totalArea, totalQuantity } = await getLeatherDetails(material.id);

      // 將計算結果添加到原物料對象中
      material.leather_total_area = totalArea;
      material.leather_total_quantity = totalQuantity;
    }

    return material;
  } catch (error) {
    console.error('通過ID獲取原物料失敗:', error);
    throw error;
  }
};

// 創建新原物料
const createMaterial = async (materialData) => {
  try {
    const id = uuidv4();
    const now = new Date();

    // 處理台北部和廠務部數量
    const taipeiStockQuantity = Number(materialData.taipei_quantity || 0);
    const factoryStockQuantity = Number(materialData.factory_quantity || 0);

    // 處理廠務部A-E的數量
    let factoryAStockQuantity = 0;
    let factoryBStockQuantity = 0;
    let factoryCStockQuantity = 0;
    let factoryDStockQuantity = 0;
    let factoryEStockQuantity = 0;

    // 如果有廠務部各倉庫的數量數據，則使用它們
    if (materialData.factory_entries && Array.isArray(materialData.factory_entries)) {
      for (const entry of materialData.factory_entries) {
        if (entry.warehouse === '廠務部A' && entry.quantity > 0) {
          factoryAStockQuantity = Number(entry.quantity);
        } else if (entry.warehouse === '廠務部B' && entry.quantity > 0) {
          factoryBStockQuantity = Number(entry.quantity);
        } else if (entry.warehouse === '廠務部C' && entry.quantity > 0) {
          factoryCStockQuantity = Number(entry.quantity);
        } else if (entry.warehouse === '廠務部D' && entry.quantity > 0) {
          factoryDStockQuantity = Number(entry.quantity);
        } else if (entry.warehouse === '廠務部E' && entry.quantity > 0) {
          factoryEStockQuantity = Number(entry.quantity);
        }
      }
    }

    // 皮料相關欄位已刪除

    // 創建新的原物料資料
    const newMaterial = {
      id,
      code: materialData.code,
      name: materialData.name,
      specification: materialData.specification,
      unit: materialData.unit,
      color: materialData.color,
      category: materialData.category,
      description: materialData.description,
      requester: materialData.requester,
      department: materialData.department,
      notes: materialData.notes,
      image_url: materialData.image_url || null,

      // 不再需要總庫存數量

      // 分別存儲台北部和廠務部的數量
      taipei_stock_quantity: taipeiStockQuantity,
      taipei_available_quantity: taipeiStockQuantity,
      factory_stock_quantity: factoryStockQuantity,
      factory_available_quantity: factoryStockQuantity,

      // 廠務部A-E的數量
      factory_a_stock_quantity: factoryAStockQuantity,
      factory_a_available_quantity: factoryAStockQuantity,
      factory_b_stock_quantity: factoryBStockQuantity,
      factory_b_available_quantity: factoryBStockQuantity,
      factory_c_stock_quantity: factoryCStockQuantity,
      factory_c_available_quantity: factoryCStockQuantity,
      factory_d_stock_quantity: factoryDStockQuantity,
      factory_d_available_quantity: factoryDStockQuantity,
      factory_e_stock_quantity: factoryEStockQuantity,
      factory_e_available_quantity: factoryEStockQuantity,

      // 皮料相關欄位已刪除

      is_active: true,
      createdAt: now,
      updatedAt: now
    };

    await db('materials').insert(newMaterial);



    // 通過數據庫查詢返回插入後的數據，避免返回可能包含循環引用的對象
    return await getMaterialById(id);
  } catch (error) {
    console.error('創建原物料失敗:', error);
    throw error;
  }
};

// 更新原物料
const updateMaterial = async (id, materialData) => {
  try {
    const now = new Date();

    // 如果提供了台北部或廠務部的數量，需要重新計算總數量
    if (materialData.taipei_quantity !== undefined || materialData.factory_quantity !== undefined) {
      // 先獲取現有的原物料數據
      const existingMaterial = await getMaterialById(id);
      if (!existingMaterial) {
        throw new Error('找不到該原物料');
      }

      // 使用提供的數量，不進行累加，因為 createMaterialTransaction 會處理累加
      const taipeiStockQuantity = materialData.taipei_quantity !== undefined
        ? Number(materialData.taipei_quantity)
        : Number(existingMaterial.taipei_stock_quantity);

      // 使用提供的數量，不進行累加，因為 createMaterialTransaction 會處理累加
      const factoryStockQuantity = materialData.factory_quantity !== undefined
        ? Number(materialData.factory_quantity)
        : Number(existingMaterial.factory_stock_quantity);

      // 處理廠務部A-E的數量
      let factoryAStockQuantity = existingMaterial.factory_a_stock_quantity || 0;
      let factoryBStockQuantity = existingMaterial.factory_b_stock_quantity || 0;
      let factoryCStockQuantity = existingMaterial.factory_c_stock_quantity || 0;
      let factoryDStockQuantity = existingMaterial.factory_d_stock_quantity || 0;
      let factoryEStockQuantity = existingMaterial.factory_e_stock_quantity || 0;

      // 如果有廠務部各倉庫的數量數據，則使用它們
      if (materialData.factory_entries && Array.isArray(materialData.factory_entries)) {
        for (const entry of materialData.factory_entries) {
          if (entry.warehouse === '廠務部A' && entry.quantity > 0) {
            factoryAStockQuantity = Number(entry.quantity);
          } else if (entry.warehouse === '廠務部B' && entry.quantity > 0) {
            factoryBStockQuantity = Number(entry.quantity);
          } else if (entry.warehouse === '廠務部C' && entry.quantity > 0) {
            factoryCStockQuantity = Number(entry.quantity);
          } else if (entry.warehouse === '廠務部D' && entry.quantity > 0) {
            factoryDStockQuantity = Number(entry.quantity);
          } else if (entry.warehouse === '廠務部E' && entry.quantity > 0) {
            factoryEStockQuantity = Number(entry.quantity);
          }
        }
      }

      // 皮料相關欄位已刪除

      // 更新數量相關欄位
      materialData.taipei_stock_quantity = taipeiStockQuantity;
      materialData.taipei_available_quantity = taipeiStockQuantity;
      materialData.factory_stock_quantity = factoryStockQuantity;
      materialData.factory_available_quantity = factoryStockQuantity;

      // 更新廠務部A-E的數量
      materialData.factory_a_stock_quantity = factoryAStockQuantity;
      materialData.factory_a_available_quantity = factoryAStockQuantity;
      materialData.factory_b_stock_quantity = factoryBStockQuantity;
      materialData.factory_b_available_quantity = factoryBStockQuantity;
      materialData.factory_c_stock_quantity = factoryCStockQuantity;
      materialData.factory_c_available_quantity = factoryCStockQuantity;
      materialData.factory_d_stock_quantity = factoryDStockQuantity;
      materialData.factory_d_available_quantity = factoryDStockQuantity;
      materialData.factory_e_stock_quantity = factoryEStockQuantity;
      materialData.factory_e_available_quantity = factoryEStockQuantity;

      // 不再需要更新總數量

      // 移除臨時欄位
      delete materialData.taipei_quantity;
      delete materialData.factory_quantity;
    }

    const updateData = {
      ...materialData,
      updatedAt: now
    };

    await db('materials')
      .where({ id, is_active: true })
      .update(updateData);



    // 通過數據庫查詢返回更新後的數據，避免返回可能包含循環引用的對象
    return await getMaterialById(id);
  } catch (error) {
    console.error('更新原物料失敗:', error);
    throw error;
  }
};

// 直接更新原物料，不進行任何額外處理
const updateMaterialDirectly = async (id, updateData) => {
  try {
    const now = new Date();

    // 添加更新時間
    updateData.updatedAt = now;

    await db('materials')
      .where({ id, is_active: true })
      .update(updateData);

    // 通過數據庫查詢返回更新後的數據
    return await getMaterialById(id);
  } catch (error) {
    console.error('直接更新原物料失敗:', error);
    throw error;
  }
};

// 刪除原物料（軟刪除）
const deleteMaterial = async (id) => {
  try {
    await db('materials')
      .where({ id })
      .update({
        is_active: false,
        updatedAt: db.fn.now()
      });
    return { id };
  } catch (error) {
    console.error('刪除原物料失敗:', error);
    throw error;
  }
};

// 創建原物料交易記錄(領用或入庫)
const createMaterialTransaction = async (transactionData) => {
  try {
    const id = uuidv4();
    const now = new Date();

    // 獲取原物料信息
    const material = await getMaterialById(transactionData.material_id);
    if (!material) {
      throw new Error('找不到該原物料');
    }

    // 確保數量是數字且不是 NaN
    const quantity = Math.abs(Number(transactionData.quantity) || 0);

    // 判斷交易發生的倉庫位置
    const storageLocation = transactionData.storage_location || '總倉庫';

    // 創建交易記錄
    const newTransaction = {
      id,
      material_id: transactionData.material_id,
      material_code: material.code,
      material_name: material.name,
      quantity: quantity, // 存儲絕對值
      transaction_type: transactionData.transaction_type,
      requester: transactionData.requester,
      department: transactionData.department,
      purpose: transactionData.purpose,
      storage_location: storageLocation,
      notes: transactionData.notes,
      created_by: transactionData.created_by,
      created_by_name: transactionData.created_by_name,
      createdAt: now,
      updatedAt: now
    };

    await db('material_transactions').insert(newTransaction);

    // 只有在數量大於 0 且不是創建原物料時才更新庫存
    // 如果是創建原物料時的交易記錄，不需要更新庫存，因為已經在 createMaterial 中處理了
    if (quantity > 0 && !transactionData.is_initial_transaction) {
      // 根據倉庫位置更新相應的數量
      let updateData = {};

      if (transactionData.transaction_type === '入庫') {
        // 入庫操作
        if (storageLocation === '台北部' || storageLocation === '總倉庫') {
          // 更新台北部數量
          updateData.taipei_stock_quantity = db.raw(`taipei_stock_quantity + ${quantity}`);
          updateData.taipei_available_quantity = db.raw(`taipei_available_quantity + ${quantity}`);
        }

        // 判斷是否為廠務部相關倉庫
        if (storageLocation === '廠務部A' || storageLocation === '總倉庫') {
          // 更新廠務部A數量
          updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
          updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
          updateData.factory_a_stock_quantity = db.raw(`factory_a_stock_quantity + ${quantity}`);
          updateData.factory_a_available_quantity = db.raw(`factory_a_available_quantity + ${quantity}`);
        } else if (storageLocation === '廠務部B') {
          // 更新廠務部B數量
          updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
          updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
          updateData.factory_b_stock_quantity = db.raw(`factory_b_stock_quantity + ${quantity}`);
          updateData.factory_b_available_quantity = db.raw(`factory_b_available_quantity + ${quantity}`);
        } else if (storageLocation === '廠務部C') {
          // 更新廠務部C數量
          updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
          updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
          updateData.factory_c_stock_quantity = db.raw(`factory_c_stock_quantity + ${quantity}`);
          updateData.factory_c_available_quantity = db.raw(`factory_c_available_quantity + ${quantity}`);
        } else if (storageLocation === '廠務部D') {
          // 更新廠務部D數量
          updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
          updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
          updateData.factory_d_stock_quantity = db.raw(`factory_d_stock_quantity + ${quantity}`);
          updateData.factory_d_available_quantity = db.raw(`factory_d_available_quantity + ${quantity}`);
        } else if (storageLocation === '廠務部E') {
          // 更新廠務部E數量
          updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
          updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
          updateData.factory_e_stock_quantity = db.raw(`factory_e_stock_quantity + ${quantity}`);
          updateData.factory_e_available_quantity = db.raw(`factory_e_available_quantity + ${quantity}`);
        }

        // 不再需要更新總數量

      } else if (transactionData.transaction_type === '領用') {
        // 領用操作
        if (storageLocation === '台北部' || storageLocation === '總倉庫') {
          // 更新台北部可用數量
          updateData.taipei_available_quantity = db.raw(`taipei_available_quantity - ${quantity}`);
        }

        // 判斷是否為廠務部相關倉庫
        if (storageLocation === '廠務部A' || storageLocation === '總倉庫') {
          // 更新廠務部A可用數量
          updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
          updateData.factory_a_available_quantity = db.raw(`factory_a_available_quantity - ${quantity}`);
        } else if (storageLocation === '廠務部B') {
          // 更新廠務部B可用數量
          updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
          updateData.factory_b_available_quantity = db.raw(`factory_b_available_quantity - ${quantity}`);
        } else if (storageLocation === '廠務部C') {
          // 更新廠務部C可用數量
          updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
          updateData.factory_c_available_quantity = db.raw(`factory_c_available_quantity - ${quantity}`);
        } else if (storageLocation === '廠務部D') {
          // 更新廠務部D可用數量
          updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
          updateData.factory_d_available_quantity = db.raw(`factory_d_available_quantity - ${quantity}`);
        } else if (storageLocation === '廠務部E') {
          // 更新廠務部E可用數量
          updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
          updateData.factory_e_available_quantity = db.raw(`factory_e_available_quantity - ${quantity}`);
        }

        // 不再需要更新總可用數量
      }

      // 只有在有更新數據時才執行更新
      if (Object.keys(updateData).length > 0) {
        await db('materials')
          .where({ id: transactionData.material_id })
          .update(updateData);
      }
    }

    // 獲取更新後的交易記錄
    return await db('material_transactions')
      .where({ id })
      .first();
  } catch (error) {
    console.error('創建原物料交易記錄失敗:', error);
    throw error;
  }
};

// 創建原物料調撥交易記錄
const createMaterialTransfer = async (transferData) => {
  try {
    const id = uuidv4();
    const now = new Date();

    // 獲取原物料信息
    const material = await getMaterialById(transferData.material_id);
    if (!material) {
      throw new Error('找不到該原物料');
    }

    // 確保數量是數字且不是 NaN
    const quantity = Math.abs(Number(transferData.quantity) || 0);

    // 調出和調入倉庫
    const fromWarehouse = transferData.from_warehouse;
    const toWarehouse = transferData.to_warehouse;

    if (!fromWarehouse || !toWarehouse) {
      throw new Error('調出倉庫和調入倉庫為必填欄位');
    }

    if (fromWarehouse === toWarehouse) {
      throw new Error('調出倉庫和調入倉庫不能相同');
    }

    // 檢查調出倉庫的可用數量是否足夠
    let availableQuantity = 0;

    // 如果是皮料類別，從 leather_materials 表中獲取可用數量
    if (material.category === '皮料' && transferData.is_leather && transferData.leather_id) {
      try {
        // 獲取指定皮革的信息
        const leather = await db('leather_materials')
          .where({ id: transferData.leather_id, warehouse: fromWarehouse, is_active: true })
          .first();

        if (leather) {
          // 皮革材料的可用數量為 1（一張皮革）
          availableQuantity = 1;

          // 在事務中更新皮革材料的倉庫信息
          console.log(`皮革材料調撥: 從 ${fromWarehouse} 調撥至 ${toWarehouse}, 皮革ID: ${transferData.leather_id}`);
        } else {
          console.log(`找不到指定的皮革材料，ID: ${transferData.leather_id}, 倉庫: ${fromWarehouse}`);
          availableQuantity = 0;
        }
      } catch (error) {
        console.error('獲取皮革材料信息失敗:', error);
        availableQuantity = 0;
      }
    } else {
      // 如果不是皮料類別，從 materials 表中獲取可用數量
      if (fromWarehouse === '台北部') {
        availableQuantity = material.taipei_available_quantity || 0;
      } else if (fromWarehouse === '廠務部A') {
        availableQuantity = material.factory_a_available_quantity || 0;
      } else if (fromWarehouse === '廠務部B') {
        availableQuantity = material.factory_b_available_quantity || 0;
      } else if (fromWarehouse === '廠務部C') {
        availableQuantity = material.factory_c_available_quantity || 0;
      } else if (fromWarehouse === '廠務部D') {
        availableQuantity = material.factory_d_available_quantity || 0;
      } else if (fromWarehouse === '廠務部E') {
        availableQuantity = material.factory_e_available_quantity || 0;
      }
    }

    if (availableQuantity < quantity) {
      throw new Error(`調出倉庫 ${fromWarehouse} 的可用數量不足，當前可用: ${availableQuantity}`);
    }

    // 創建調出交易記錄
    const outTransaction = {
      id: uuidv4(),
      material_id: transferData.material_id,
      material_code: material.code,
      material_name: material.name,
      quantity: quantity,
      transaction_type: '調出',
      requester: transferData.requester,
      department: fromWarehouse,
      storage_location: fromWarehouse,
      notes: transferData.notes || `從 ${fromWarehouse} 調撥至 ${toWarehouse}`,
      transfer_id: id, // 關聯到調撥記錄
      created_by: transferData.created_by,
      created_by_name: transferData.created_by_name,
      createdAt: now,
      updatedAt: now
    };

    // 創建調入交易記錄
    const inTransaction = {
      id: uuidv4(),
      material_id: transferData.material_id,
      material_code: material.code,
      material_name: material.name,
      quantity: quantity,
      transaction_type: '調入',
      requester: transferData.requester,
      department: toWarehouse,
      storage_location: toWarehouse,
      notes: transferData.notes || `從 ${fromWarehouse} 調撥至 ${toWarehouse}`,
      transfer_id: id, // 關聯到調撥記錄
      created_by: transferData.created_by,
      created_by_name: transferData.created_by_name,
      createdAt: now,
      updatedAt: now
    };

    // 創建主調撥記錄
    const transferRecord = {
      id,
      material_id: transferData.material_id,
      material_code: material.code,
      material_name: material.name,
      quantity: quantity,
      from_warehouse: fromWarehouse,
      to_warehouse: toWarehouse,
      requester: transferData.requester,
      notes: transferData.notes,
      created_by: transferData.created_by,
      created_by_name: transferData.created_by_name,
      createdAt: now,
      updatedAt: now
    };

    // 更新原物料庫存數量
    let updateData = {};

    // 從調出倉庫減少可用數量
    if (fromWarehouse === '台北部') {
      updateData.taipei_available_quantity = db.raw(`taipei_available_quantity - ${quantity}`);
      updateData.taipei_stock_quantity = db.raw(`taipei_stock_quantity - ${quantity}`);
    } else if (fromWarehouse === '廠務部A') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity - ${quantity}`);
      updateData.factory_a_available_quantity = db.raw(`factory_a_available_quantity - ${quantity}`);
      updateData.factory_a_stock_quantity = db.raw(`factory_a_stock_quantity - ${quantity}`);
    } else if (fromWarehouse === '廠務部B') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity - ${quantity}`);
      updateData.factory_b_available_quantity = db.raw(`factory_b_available_quantity - ${quantity}`);
      updateData.factory_b_stock_quantity = db.raw(`factory_b_stock_quantity - ${quantity}`);
    } else if (fromWarehouse === '廠務部C') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity - ${quantity}`);
      updateData.factory_c_available_quantity = db.raw(`factory_c_available_quantity - ${quantity}`);
      updateData.factory_c_stock_quantity = db.raw(`factory_c_stock_quantity - ${quantity}`);
    } else if (fromWarehouse === '廠務部D') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity - ${quantity}`);
      updateData.factory_d_available_quantity = db.raw(`factory_d_available_quantity - ${quantity}`);
      updateData.factory_d_stock_quantity = db.raw(`factory_d_stock_quantity - ${quantity}`);
    } else if (fromWarehouse === '廠務部E') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity - ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity - ${quantity}`);
      updateData.factory_e_available_quantity = db.raw(`factory_e_available_quantity - ${quantity}`);
      updateData.factory_e_stock_quantity = db.raw(`factory_e_stock_quantity - ${quantity}`);
    }

    // 向調入倉庫增加可用和總數量
    if (toWarehouse === '台北部') {
      updateData.taipei_available_quantity = db.raw(`taipei_available_quantity + ${quantity}`);
      updateData.taipei_stock_quantity = db.raw(`taipei_stock_quantity + ${quantity}`);
    } else if (toWarehouse === '廠務部A') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
      updateData.factory_a_available_quantity = db.raw(`factory_a_available_quantity + ${quantity}`);
      updateData.factory_a_stock_quantity = db.raw(`factory_a_stock_quantity + ${quantity}`);
    } else if (toWarehouse === '廠務部B') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
      updateData.factory_b_available_quantity = db.raw(`factory_b_available_quantity + ${quantity}`);
      updateData.factory_b_stock_quantity = db.raw(`factory_b_stock_quantity + ${quantity}`);
    } else if (toWarehouse === '廠務部C') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
      updateData.factory_c_available_quantity = db.raw(`factory_c_available_quantity + ${quantity}`);
      updateData.factory_c_stock_quantity = db.raw(`factory_c_stock_quantity + ${quantity}`);
    } else if (toWarehouse === '廠務部D') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
      updateData.factory_d_available_quantity = db.raw(`factory_d_available_quantity + ${quantity}`);
      updateData.factory_d_stock_quantity = db.raw(`factory_d_stock_quantity + ${quantity}`);
    } else if (toWarehouse === '廠務部E') {
      updateData.factory_available_quantity = db.raw(`factory_available_quantity + ${quantity}`);
      updateData.factory_stock_quantity = db.raw(`factory_stock_quantity + ${quantity}`);
      updateData.factory_e_available_quantity = db.raw(`factory_e_available_quantity + ${quantity}`);
      updateData.factory_e_stock_quantity = db.raw(`factory_e_stock_quantity + ${quantity}`);
    }

    // 使用事務來確保數據一致性
    await db.transaction(async trx => {
      // 插入調撥記錄
      await trx('material_transfers').insert(transferRecord);

      // 插入調出和調入交易記錄
      await trx('material_transactions').insert([outTransaction, inTransaction]);

      // 更新原物料庫存
      await trx('materials')
        .where({ id: transferData.material_id })
        .update(updateData);

      // 如果是皮料類別且有皮革ID，更新皮革材料的倉庫信息
      if (material.category === '皮料' && transferData.is_leather && transferData.leather_id) {
        await trx('leather_materials')
          .where({ id: transferData.leather_id })
          .update({
            warehouse: toWarehouse,
            updatedAt: now,
            notes: transferData.notes || `從 ${fromWarehouse} 調撥至 ${toWarehouse}`
          });
        console.log(`皮革材料倉庫更新成功: 從 ${fromWarehouse} 調撥至 ${toWarehouse}, 皮革ID: ${transferData.leather_id}`);
      }
    });

    // 返回調撥記錄
    return transferRecord;
  } catch (error) {
    console.error('創建原物料調撥記錄失敗:', error);
    throw error;
  }
};

// 獲取原物料交易記錄
const getMaterialTransactions = async (filters = {}) => {
  try {
    let query = db('material_transactions');

    // 添加篩選條件
    if (filters.material_id) {
      query = query.where('material_id', filters.material_id);
    }

    if (filters.transaction_type) {
      query = query.where('transaction_type', filters.transaction_type);
    }

    if (filters.department) {
      query = query.where('department', filters.department);
    }

    if (filters.start_date) {
      query = query.where('createdAt', '>=', filters.start_date);
    }

    if (filters.end_date) {
      // 設定結束日期為該日23:59:59
      const endDate = new Date(filters.end_date);
      endDate.setHours(23, 59, 59, 999);
      query = query.where('createdAt', '<=', endDate);
    }

    // 按創建時間降序排序，最新記錄在前
    return await query.orderBy('createdAt', 'desc');
  } catch (error) {
    console.error('獲取原物料交易記錄失敗:', error);
    throw error;
  }
};

// 獲取指定部門的原物料
const getMaterialsByDepartment = async (department, search = '') => {
  try {
    let query = db('materials').where('is_active', true);

    // 根據部門篩選原物料
    if (department === '台北部') {
      // 台北部有庫存的原物料
      query = query.where('taipei_stock_quantity', '>', 0);
    } else if (department === '廠務部A') {
      // 廠務部A有庫存的原物料
      query = query.where('factory_a_stock_quantity', '>', 0);
    } else if (department === '廠務部B') {
      // 廠務部B有庫存的原物料
      query = query.where('factory_b_stock_quantity', '>', 0);
    } else if (department === '廠務部C') {
      // 廠務部C有庫存的原物料
      query = query.where('factory_c_stock_quantity', '>', 0);
    } else if (department === '廠務部D') {
      // 廠務部D有庫存的原物料
      query = query.where('factory_d_stock_quantity', '>', 0);
    } else if (department === '廠務部E') {
      // 廠務部E有庫存的原物料
      query = query.where('factory_e_stock_quantity', '>', 0);
    }

    // 如果提供了搜索關鍵字
    if (search) {
      query = query.where(builder => {
        builder.where('code', 'like', `%${search}%`)
          .orWhere('name', 'like', `%${search}%`);
      });
    }

    // 按創建時間降序排序，最新條目在前
    const materials = await query.orderBy('createdAt', 'desc');

    // 為每個材料添加正確的可用數量和存儲位置信息
    return materials.map(material => {
      let availableQuantity = 0;
      let storageLocation = department;

      // 根據部門設置可用數量
      if (department === '台北部') {
        availableQuantity = material.taipei_available_quantity || 0;
      } else if (department === '廠務部A') {
        availableQuantity = material.factory_a_available_quantity || 0;
      } else if (department === '廠務部B') {
        availableQuantity = material.factory_b_available_quantity || 0;
      } else if (department === '廠務部C') {
        availableQuantity = material.factory_c_available_quantity || 0;
      } else if (department === '廠務部D') {
        availableQuantity = material.factory_d_available_quantity || 0;
      } else if (department === '廠務部E') {
        availableQuantity = material.factory_e_available_quantity || 0;
      }

      return {
        ...material,
        available_quantity: availableQuantity,
        storage_location: storageLocation
      };
    });
  } catch (error) {
    console.error('獲取部門原物料列表失敗:', error);
    throw error;
  }
};

module.exports = {
  getAllMaterials,
  getMaterialById,
  createMaterial,
  updateMaterial,
  updateMaterialDirectly,
  deleteMaterial,
  createMaterialTransaction,
  createMaterialTransfer,
  getMaterialTransactions,
  getMaterialsByDepartment,
  getLeatherDetails
};