const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');

// 獲取所有皮料特殊資料
const getAllLeatherMaterials = async (filters = {}) => {
  try {
    let query = db('leather_materials').where('is_active', true);

    // 應用過濾條件
    if (filters.material_id) {
      query = query.where('material_id', filters.material_id);
    }
    if (filters.warehouse) {
      query = query.where('warehouse', filters.warehouse);
    }
    if (filters.leather_code) {
      query = query.where('leather_code', 'like', `%${filters.leather_code}%`);
    }

    // 默認按建立時間降序排列（最新的在前）
    query = query.orderBy('createdAt', 'desc');

    return await query;
  } catch (error) {
    console.error('獲取皮料資料失敗:', error);
    throw error;
  }
};

// 根據ID獲取皮料特殊資料
const getLeatherMaterialById = async (id) => {
  try {
    return await db('leather_materials').where({ id }).first();
  } catch (error) {
    console.error('獲取皮料資料失敗:', error);
    throw error;
  }
};

// 生成唯一的皮料編碼
const generateLeatherCode = async (materialCode) => {
  try {
    // 獲取原物料編號的後兩碼
    const lastTwoDigits = materialCode.slice(-2);

    // 獲取當前日期：YYMMDD 格式
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 構建基本編碼格式：材料編號後兩碼+日期
    const codePrefix = `${lastTwoDigits}${dateStr}`;

    // 查詢今天已經存在的相同前綴的編碼
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    const existingCodes = await db('leather_materials')
      .where('leather_code', 'like', `${codePrefix}%`)
      .where('createdAt', '>=', startOfDay)
      .where('createdAt', '<=', endOfDay)
      .orderBy('leather_code', 'desc')
      .select('leather_code');

    // 找出最大的序號
    let maxSeq = 0;
    if (existingCodes.length > 0) {
      const latestCode = existingCodes[0].leather_code;
      // 取出最後3位序號
      const seqStr = latestCode.slice(-3);
      maxSeq = parseInt(seqStr, 10);
    }

    // 生成新的序號
    const newSeq = (maxSeq + 1).toString().padStart(3, '0');

    // 構建完整的皮料編碼
    const leatherCode = `${codePrefix}${newSeq}`;

    return leatherCode;
  } catch (error) {
    console.error('生成皮料編碼失敗:', error);
    throw error;
  }
};

// 創建皮料特殊資料
const createLeatherMaterial = async (leatherData) => {
  try {
    const id = uuidv4();
    const now = new Date();

    // 生成新的皮料編碼
    const leatherCode = await generateLeatherCode(leatherData.material_code);

    // 創建皮料特殊資料記錄
    const newLeatherMaterial = {
      id,
      material_id: leatherData.material_id,
      material_code: leatherData.material_code,
      material_name: leatherData.material_name,
      warehouse: leatherData.warehouse,
      area: Number(leatherData.area || 0),
      quantity: Number(leatherData.quantity || 0),
      leather_code: leatherCode,
      transaction_id: leatherData.transaction_id,
      created_by: leatherData.created_by,
      created_by_name: leatherData.created_by_name,
      notes: leatherData.notes,
      createdAt: now,
      updatedAt: now
    };

    await db('leather_materials').insert(newLeatherMaterial);

    // 返回創建的皮料資料和生成的編碼
    return {
      ...newLeatherMaterial,
      leather_code: leatherCode
    };
  } catch (error) {
    console.error('創建皮料特殊資料失敗:', error);
    throw error;
  }
};

// 更新皮料特殊資料
const updateLeatherMaterial = async (id, updateData) => {
  try {
    const now = new Date();
    updateData.updatedAt = now;

    await db('leather_materials').where({ id }).update(updateData);

    return await getLeatherMaterialById(id);
  } catch (error) {
    console.error('更新皮料特殊資料失敗:', error);
    throw error;
  }
};

// 刪除皮料特殊資料（軟刪除）
const deleteLeatherMaterial = async (id) => {
  try {
    await db('leather_materials').where({ id }).update({
      is_active: false,
      updatedAt: new Date()
    });

    return { success: true };
  } catch (error) {
    console.error('刪除皮料特殊資料失敗:', error);
    throw error;
  }
};

module.exports = {
  getAllLeatherMaterials,
  getLeatherMaterialById,
  createLeatherMaterial,
  updateLeatherMaterial,
  deleteLeatherMaterial
};