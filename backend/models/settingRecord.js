const db = require('../database/db');

// 確保settingRecord表存在
const createSettingRecordTableIfNotExists = async () => {
  const tableExists = await db.schema.hasTable('settingRecord');
  
  if (!tableExists) {
    await db.schema.createTable('settingRecord', (table) => {
      table.increments('id').primary();
      table.string('type').notNullable().comment('設定類型，例如：顏色、規格等');
      table.string('name').notNullable().comment('設定名稱');
      table.string('value').notNullable().comment('設定值');
      table.text('description').nullable().comment('描述');
      table.integer('sort_order').defaultTo(0).comment('排序順序');
      table.boolean('is_active').defaultTo(true).comment('是否啟用');
      table.timestamp('createdAt').defaultTo(db.fn.now());
      table.timestamp('updatedAt').defaultTo(db.fn.now());
    });
    
    console.log('settingRecord表已創建');
  }
};

// 初始化
createSettingRecordTableIfNotExists();

module.exports = {
  createSettingRecordTableIfNotExists
}; 