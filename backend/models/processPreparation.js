// 備料工序模型
const db = require('../database/db');
const { v4: uuidv4 } = require('uuid');

class ProcessPreparation {
  // 根據 BOM ID 獲取備料工序列表
  static async getByBomId(bomId) {
    try {
      const processList = await db('process_preparation')
        .where({ bomId })
        .orderBy('createdAt', 'asc');
      
      // 確保返回的是純淨的物件，不含循環引用
      const safeProcessList = processList.map(item => ({
        id: item.id,
        bomId: item.bomId,
        processCode: item.processCode,
        majorProcess: item.majorProcess,
        minorProcess: item.minorProcess,
        sequenceNumber: item.sequenceNumber,
        pieceGroup: item.pieceGroup,
        pieceDetail: item.pieceDetail,
        pieceName: item.pieceName,
        material: item.material,
        quantity: item.quantity,
        tool: item.tool,
        consumable: item.consumable,
        processDescription: item.processDescription,
        standardTime: item.standardTime,
        actualTime: item.actualTime,
        order: item.order,
        created_by: item.created_by,
        created_by_name: item.created_by_name,
        sourcePosition: item.sourcePosition,
        sourceProcessId: item.sourceProcessId,
        isCompleted: item.isCompleted,
        isFixed: item.isFixed,

        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
      
      return { status: 'success', data: safeProcessList };
    } catch (error) {
      console.error('獲取備料工序列表失敗:', error);
      return { status: 'error', message: '獲取備料工序列表失敗' };
    }
  }

  // 創建備料工序
  static async create(processData) {
    try {
      const { 
        bomId, 
        processCode, 
        majorProcess, 
        minorProcess, 
        pieceGroup, 
        pieceDetail, 
        pieceName, 
        material, 
        quantity, 
        tool, 
        consumable, 
        processDescription, 
        standardTime, 
        actualTime, 
        order, 
        created_by, 
        created_by_name,
        sourcePosition,
        sourceProcessId,
        isCompleted,
        isFixed
      } = processData;
      
      // 生成序號 (大工序代碼 + 小工序代碼 + 順序號)
      const sequenceNumber = majorProcess.charAt(0) + minorProcess.split(' ')[0] + order;
      
      // 使用 JavaScript 的 ISO 字串格式化時間，避免 db.fn.now() 可能的循環引用
      const now = new Date().toISOString();
      
      const newProcess = {
        id: uuidv4(),
        bomId,
        processCode,
        majorProcess,
        minorProcess,
        sequenceNumber,
        pieceGroup,
        pieceDetail,
        pieceName,
        material: material || null,
        quantity: quantity || 1,
        tool: tool || null,
        consumable: consumable || null,
        processDescription: processDescription || null,
        standardTime: standardTime || 0,
        actualTime: actualTime || 0,
        order,
        created_by: created_by || null,
        created_by_name: created_by_name || null,
        sourcePosition: sourcePosition || null,
        sourceProcessId: sourceProcessId || null,
        isCompleted: isCompleted || false,
        isFixed: isFixed || false,
        createdAt: now,
        updatedAt: now
      };
      
      // 將新物件插入資料庫
      await db('process_preparation').insert(newProcess);
      
      // 確保返回的是純淨的物件，避免任何可能的循環引用
      const safeProcessObject = { ...newProcess };
      
      return { status: 'success', data: safeProcessObject };
    } catch (error) {
      console.error('創建備料工序失敗:', error);
      return { status: 'error', message: '創建備料工序失敗' };
    }
  }

  // 更新備料工序
  static async update(id, processData) {
    try {
      // 使用 JavaScript 的 ISO 字串格式化時間，避免 db.fn.now() 可能的循環引用
      const now = new Date().toISOString();
      
      const updateData = {
        ...processData,
        updatedAt: now
      };
      
      delete updateData.id; // 避免更新主鍵
      
      await db('process_preparation')
        .where({ id })
        .update(updateData);
      
      return { status: 'success', message: '備料工序更新成功' };
    } catch (error) {
      console.error('更新備料工序失敗:', error);
      return { status: 'error', message: '更新備料工序失敗' };
    }
  }

  // 刪除備料工序
  static async delete(id) {
    try {
      await db('process_preparation')
        .where({ id })
        .del();
      
      return { status: 'success', message: '備料工序刪除成功' };
    } catch (error) {
      console.error('刪除備料工序失敗:', error);
      return { status: 'error', message: '刪除備料工序失敗' };
    }
  }
  
  // 獲取工具列表
  static async getTools() {
    try {
      const tools = await db('process_tools')
        .where({ is_active: true })
        .orderBy(['processKey', 'sort_order']);
      
      // 確保返回的是純淨的物件，不含循環引用
      const safeTools = tools.map(tool => ({
        id: tool.id,
        processKey: tool.processKey,
        toolName: tool.toolName,
        sort_order: tool.sort_order,
        is_active: tool.is_active,
        createdAt: tool.createdAt,
        updatedAt: tool.updatedAt
      }));
      
      return { status: 'success', data: safeTools };
    } catch (error) {
      console.error('獲取工具列表失敗:', error);
      return { status: 'error', message: '獲取工具列表失敗' };
    }
  }
  
  // 獲取耗材列表
  static async getConsumables() {
    try {
      const consumables = await db('process_consumables')
        .where({ is_active: true })
        .orderBy(['processKey', 'sort_order']);
      
      // 確保返回的是純淨的物件，不含循環引用
      const safeConsumables = consumables.map(consumable => ({
        id: consumable.id,
        processKey: consumable.processKey,
        consumableName: consumable.consumableName,
        sort_order: consumable.sort_order,
        is_active: consumable.is_active,
        createdAt: consumable.createdAt,
        updatedAt: consumable.updatedAt
      }));
      
      return { status: 'success', data: safeConsumables };
    } catch (error) {
      console.error('獲取耗材列表失敗:', error);
      return { status: 'error', message: '獲取耗材列表失敗' };
    }
  }
}

module.exports = ProcessPreparation; 