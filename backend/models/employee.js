const db = require('../database/db');
const bcrypt = require('bcryptjs');

class Employee {
  // 根據ID查找員工
  static async findById(id) {
    return await db('employees').where({ id }).first();
  }

  // 根據用戶名查找員工
  static async findByUsername(username) {
    return await db('employees').where({ username }).first();
  }

  // 根據工號查找員工
  static async findByEmployeeId(employeeId) {
    return await db('employees').where({ employeeId }).first();
  }

  // 創建新員工
  static async create(employeeData) {
    // 在實際應用中，應該使用bcrypt加密密碼
    // 但為了簡化，這里暫時使用明文密碼
    const [id] = await db('employees').insert({
      ...employeeData,
      updated_at: db.fn.now()
    });
    return await this.findById(id);
  }

  // 更新員工資料
  static async update(id, updateData) {
    await db('employees').where({ id }).update({
      ...updateData,
      updated_at: db.fn.now()
    });
    return await this.findById(id);
  }

  // 刪除員工
  static async delete(id) {
    return await db('employees').where({ id }).del();
  }

  // 查找所有員工
  static async findAll() {
    return await db('employees').select('*');
  }

  // 檢查密碼
  static async checkPassword(username, candidatePassword) {
    const employee = await this.findByUsername(username);
    if (!employee) return false;
    
    // 在實際應用中，應該使用bcrypt比較密碼
    // 但為了簡化，這里暫時直接比較
    return employee.password === candidatePassword;
  }
}

module.exports = Employee; 